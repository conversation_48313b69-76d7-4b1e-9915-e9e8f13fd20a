 /**
 * Flex Grid Mixin
 * ----------
 * @param  {integer} $items
 * @param  {number} $spacing
 * @param  {number} $vertical-spacing
 * @param  {list} $columns
 * ----------------------
 * Usage Example:
 * --------------
 * @include grid(
 *     $items: 3,
 *     $spacing: 50px,
 *     $vertical-spacing: 30px,
 *     $columns: (auto, 650px, auto) // You can also use % here instead of px
 * );
 */
 @mixin flexgrid(
	$columns: false, 
	$spacing: 0px, // Horizontal spacing
	$vertical-spacing: false, // Vertical spacing
	$horizontal-align: false, // start, center or end
	$vertical-align: false, // start, center or end
	$breakpoint: large up // e.g. large up
) {
	$items : 0;
	// Getting the number of columns
	@if $columns and type-of($columns) == number and unitless($columns) {
		$items : $columns;
	} @else {
		$items : length($columns);
	}

	@include breakpoint($breakpoint) {
		display: flex;
		flex-wrap: wrap;
		@if $horizontal-align {
			justify-content: $horizontal-align;
		}
		@if $horizontal-align {
			align-items: $vertical-align;
		}
	}
	@if not $vertical-spacing { $vertical-spacing: $spacing; }
	> * {
		@include breakpoint($breakpoint) {

			// Default flex width
			flex-grow: 0;
			flex-shrink: 0;
			//flex-basis: calc(#{100 / $items}% - #{$spacing - ($spacing / $items)});
			width: calc(#{100 / $items}% - #{$spacing - ($spacing / $items)});

			// Specific flex items widths
			@if length($columns) > 1 {

				// Get all the fixed widths first
				$fixed-widths-px: 0px;
				$fixed-widths-percents: 0%;
				$number-of-fixed-items: 0;
				@each $value in $columns {
					@if type-of($value) == 'number' {
						@if unit($value) == 'px' { $fixed-widths-px: $fixed-widths-px + $value; }
						@if unit($value) == '%' { $fixed-widths-percents: $fixed-widths-percents + $value; }
						$number-of-fixed-items: $number-of-fixed-items + 1;
					}
				}

				// Error, the number of widths values needs to match the number of items.
				// e.g. $items: 3, $columns: (auto, 650px, auto)
				@if length($columns) != $items {
					@error "The number of widths values doesn't match the number of items.";
				}

				// Now set specific widths
				@for $i from 1 through length($columns) {
				    $value: nth($columns, $i);
				    &:nth-child(#{$i}n + #{$i}) {
				    	@if type-of($value) == 'number' { 
			    		        	flex-grow: 0;
							        flex-shrink: 0;
					    			width: #{$value};
					    		}
				    	@else {
				    		$new-items: $items - $number-of-fixed-items;
				    		$total-spacing: $spacing * $number-of-fixed-items;
				    		@if $total-spacing == 0 { $total-spacing: '0px'; }
				    		        	flex-grow: 0;
								        flex-shrink: 0;
								        //flex-basis: calc(#{100 / $new-items}% - #{$fixed-widths-px / $new-items} - #{$fixed-widths-percents / $new-items} - #{$total-spacing});  flex: 0 0 calc(#{100 / $new-items}% - #{$fixed-widths-px / $new-items} - #{$fixed-widths-percents / $new-items} - #{$total-spacing});
								        width: calc(#{100 / $new-items}% - #{$fixed-widths-px / $new-items} - #{$fixed-widths-percents / $new-items} - #{$total-spacing}); 
				    	}
				    }
				    @if $i == length($columns) {
				    	$value: nth($columns, 1);
					    &:nth-child(#{$i+1}n + #{$i+1}) {
					    	@if type-of($value) == 'number' { 
			    		        	flex-grow: 0;
							        flex-shrink: 0;
					    			width: #{$value};
					    		}
					    	@else {
					    		$new-items: $items - $number-of-fixed-items;
					    		$total-spacing: $spacing * $number-of-fixed-items;
					    		@if $total-spacing == 0 { $total-spacing: '0px'; }
					    					flex-grow: 0;
								        	flex-shrink: 0;
								        	//flex-basis: calc(#{100 / $new-items}% - #{$fixed-widths-px / $new-items} - #{$fixed-widths-percents / $new-items} - #{$total-spacing});
								        	width: calc(#{100 / $new-items}% - #{$fixed-widths-px / $new-items} - #{$fixed-widths-percents / $new-items} - #{$total-spacing});
					    	}
					    }
				    }
				}
			}

			// Spacings
			margin-right: $spacing;
			margin-top: $vertical-spacing;
			&:nth-child(-n+#{$items}) {
				margin-top: 0;
			}
			&:nth-child(#{$items}n+#{$items}) {
				margin-right: 0;
			}
			&:last-child {
				margin-right: 0;
			}

		}

	}
}