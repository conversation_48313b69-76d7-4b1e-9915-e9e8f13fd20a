// ==========
// Grid Mixin
// ==========
// <AUTHOR> <<EMAIL>>
// @version 2.0.0
// ==============
@mixin grid(
	$rows: false, 
	$columns: false, 
	$spacing: 0, // Horizontal spacing
	$vertical-spacing: false, // Vertical spacing
	$horizontal-align: false, // start, center or end
	$vertical-align: false, // start, center or end
	$breakpoint: large up // e.g. large up
) {

	// All Recent Browsers
	// ===================

	// Default top spacing
	@if not $vertical-spacing { $vertical-spacing: $spacing; }

	// ===================================================
	// All Recent Browsers
	// ===================
	@include breakpoint($breakpoint) {

		// Vertical Alignment
		@if $vertical-align == middle { $vertical-align: center; }
		@if $vertical-align == top { $vertical-align: start; }
		@if $vertical-align == bottom { $vertical-align: end; }
		@if $vertical-align { align-items: $vertical-align; }

		// Horizontal Alignment
		@if $horizontal-align == middle { $horizontal-align: center; }
		@if $horizontal-align == left { $horizontal-align: start; }
		@if $horizontal-align == right { $horizontal-align: end; }
		@if $horizontal-align { justify-content: $horizontal-align; }

		// Set the grid
		display: grid;
		// Columns
		@if $columns and type-of($columns) == number and unitless($columns) {
			grid-template-columns: repeat($columns, 1fr);
		} @elseif $columns {
			grid-template-columns: $columns;
		}
		// Rows
		@if $rows and type-of($rows) == number and unitless($rows) {
			grid-template-rows: repeat($rows, 1fr);
		} @elseif $rows {
			grid-template-columns: $rows;
		}
		grid-gap: #{$spacing} #{$vertical-spacing};
	}

	// ===================================================
	// Internet Explorer Fallback
	// ==========================
	@if mixin-exists(ie) { @include ie($breakpoint) {
		@include flexgrid(
				$columns: $columns,
				$spacing: $spacing,
				$vertical-spacing: $vertical-spacing,
				$horizontal-align: $horizontal-align,
				$vertical-align: $vertical-align,
				$breakpoint: $breakpoint
				);
	}}

}