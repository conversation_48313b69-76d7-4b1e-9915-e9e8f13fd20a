
// Global Mixins
// =============

// Container
@mixin container($max-width: 1230px, $gutter: 25px) {
	max-width: $max-width;
	padding-left: $gutter;
	padding-right: $gutter;
    margin-right: auto;
    margin-left: auto;
}

// Transitions
@mixin transitions($duration: .2s) {
	-webkit-transition: all $duration;
	-moz-transition: all $duration;
	-ms-transition: all $duration;
	-o-transition: all $duration;
	transition: all $duration;
}

// Remove transitions
@mixin no-transitions() {
	-webkit-transition: none;
	-moz-transition: none;
	-ms-transition: none;
	-o-transition: none;
	transition: none;
}

// Horizontal & Vertical Alignment
@mixin horizontal-vertical-align() {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
}

// Horizontal Alignment
@mixin horizontal-align() {
	position: absolute;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
}

// Vertical Alignment
@mixin vertical-align() {
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
}

// CSS Text Ellipsis
@mixin text-ellipsis() {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

// Disable Text Selection
@mixin disable-selection() {
	-webkit-touch-callout: none; /* iOS Safari */
	-webkit-user-select: none;   /* Chrome/Safari/Opera */
	-khtml-user-select: none;    /* Konqueror */
	-moz-user-select: none;      /* Firefox */
	-ms-user-select: none;       /* IE/Edge */
	user-select: none;
}

// Disable input (can't click on it)
@mixin disable-input() {
	opacity: 0.7;
	pointer-events: none;
}

// Enable input
@mixin enable-input() {
	opacity: 1;
	pointer-events: auto;
}

// Animation KeyFrames
@mixin animation-keyframes($settings) {
	-webkit-animation: $settings;
	-moz-animation: $settings;
	-ms-animation: $settings;
	-o-animation: $settings;
	animation: $settings;
	$animation-name: unique-id();
	animation-name: $animation-name;
	@keyframes #{$animation-name} {
		@content;
	}
}

// Loading Spinner
@mixin loading-spinner($width, $height, $border-width, $color) {
	@include animation-keyframes(.75s all infinite) {
		0% {
			-webkit-transform: rotate(0deg);
			-moz-transform: rotate(0deg);
			-ms-transform: rotate(0deg);
			-o-transform: rotate(0deg);
			transform: rotate(0deg);
		}
		100% {
			-webkit-transform: rotate(360deg);
			-moz-transform: rotate(360deg);
			-ms-transform: rotate(360deg);
			-o-transform: rotate(360deg);
			transform: rotate(360deg);
		}
	}
	height: $height;
	width: $width;
	border: $border-width solid $color;
	border-right-color: transparent;
	border-radius: 50%;
	display: inline-block;
	@include horizontal-vertical-align();
}