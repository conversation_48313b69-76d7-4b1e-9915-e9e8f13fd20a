// =============================
// === RESPOSNIVE NAVIGATION ===
// =============================
// Index:
// 1 - Wrapper
// 2 - Navigation
// 3 - Burger Icon
// ==================


// 1 - Wrapper
// ===================
.responsive_menu {
	background-color: $black;
	display: none;
	height: 100%;
	position: fixed;
	left: 0;
	z-index: 99;
	width: 100%;

	&__inner {
		align-items: center;
		display: flex;
		height: 100%;
		justify-content: center;
		overflow: scroll;
		width: 100%;
	}

	.close-icon {
		cursor: pointer;
		color: $white;
		font-size: 1.5rem;
		position: absolute;
		right: 30px;
		top: 30px;
		@include transitions();
		
		&:hover {
			color: $primary-color;
		}
	}
}


// 2 - Navigation
// ======================
.responsive_navigation {
	$block: &;
	height: 100%;
	padding: 80px 10px 30px;
	text-align: center;
	width: 100%;
	overflow: scroll;

	&__item {
		color: white;
		display: block;
		font-size: 1.125rem;
		font-weight: $weight-bold;
		text-transform: uppercase;
		position: relative;
		@include transitions();

		&:not(:last-of-type) { 
			margin-bottom: 20px;
		}

		&:hover,
		&.active,
		&.open {
			color: $primary-color;
		}

		&--arrow:after {
			content: '\f0a3';
			color: inherit;
			font-family: 'Icomoon';
			font-size: .85em;
			font-weight: normal;
			line-height: 1;
			margin-left: 7px;
			position: relative;
		}

		&--button {
			color: $white;
			margin-bottom: 20px;
			padding-top: 8px;
			padding-bottom: 8px;
			
			&:hover {
				color: $white;
			}
		}

		&--mobile {
			font-size: 0.9rem;
		}
	}

	&__dropdown {
		margin-bottom: 30px;
		margin-top: -10px;

		#{ $block }__item {
			color: $white;
			font-size: 1.1875rem;
			font-weight: $weight-normal;
			margin-bottom: 5px;
			text-transform: none;

			&:hover, &.active {
				color: $primary-color;
			}
		}
	}

}


// 3 - Burger Icon
// ======================
.burger {
	&__icon {
		display: block;
		@include transitions();
		
		// Each span is a line of the icon
		span {
			background-color: $headings-color;
			display: block;
			height: 2px;
			width: 26px;
			@include transitions();

			&:not(:last-child){
				margin-bottom: 7px;
			}
		}

		&:hover,
		&:focus {
			span {
				background-color: $primary-color;
				outline: none;
			}
		}
	}
}