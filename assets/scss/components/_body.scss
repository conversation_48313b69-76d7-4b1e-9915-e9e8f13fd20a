// =====================
// === BODY & LAYOUT ===
// =====================
// Index:
// 1 - Body General
// 2 - Loading Bar of instantclick
// 3 - <PERSON>ie Message
// 4 - Container
// ==================


// 1 - Body basics
// ===================
body {
	width: 100%;
	display: block !important;
	font-family: $body-font-family, Helvetica, Arial, sans-serif;
	font-weight: normal;
	line-height: $global-line-height;
	color: $text-color;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;

	// Loading state
	&.loading {
		&::before {
			content: "";
			display: block;
			position: fixed;
			z-index: 99;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, .7);
		}

		&:after {
			content: "";
			@include loading-spinner(30px, 30px, 3px, $primary-color);
			position: fixed;
			z-index: 100;
		}
	}

}
 
*, *:before, *:after {
    box-sizing: border-box;
}


// 2 - Loading Bar
// ================
#instantclick-bar {
	background: $primary-color;
}

// 3 - Cookie Message
// ====================
#cookieMessage{
	position: fixed;
    bottom: 0;
    width: 100%;
    background-color: $background-1;
    padding-top: 16px;
    text-align: center;
    line-height: 24px;
    color: $text-color;
    font-size: 0.75rem;
    z-index: 100;
    padding-left: 20px;
	padding-right: 20px;
	@include breakpoint(small down) {
		padding-bottom: 16px;
	}
	a{
		margin-left: 20px;
		font-size: 0.75rem;
		padding: 5px 25px;
		min-width: 0;
		vertical-align: top;
		font-weight: bold;
		min-height: 0;
		max-height: 24px;
		@include breakpoint(small down) {
			display: flex;
			margin: auto;
			margin-top: 10px;
			width: 79px;
		}
	}
}

// 4 - Container
// ================
.main_container {
	$container: &;
	&__inner {
		display: flex;
		justify-content: center;
		margin-top: 60px;
		margin-bottom: 50px;

		@include breakpoint(medium down) {
			margin-top: 50px;
			flex-direction: column;
		}

		&.container {
			padding: 0;
		}

		#{$container}__content {
            @include breakpoint(large) {
                order: 2;
                width: calc(100% - #{$sidebar-width} - #{$sidebar-margin});    
            }
		}

		#{$container}__sidebar {
            @include breakpoint(large) {         
			    width: $sidebar-width;
			}
			@include breakpoint(medium down) {         
			    margin: 50px auto 0;
			    max-width: 400px;
            }
		}

		
	}
	&--with-sidebar {
		#{$container}__inner {
			justify-content: space-between;
			&.container {
				padding: 0 25px;
			}
		}
		.block_alternating_text, 
		.block_mission_statement {
			.container {
				padding: 0;
			}
		}
		.block_layers {
			& + .block_alternating_text {
				padding-top: 32px;
				padding-bottom: 0;
			}
		}
		.block_layers + .block_family_support,
		.block_layers + .block_featured_info,
		.block_featured_info + .block_layers {
			margin-top: 48px;
		}
		.block_mission_statement,
		.block_mission_statement + .block_layers,
		.block_alternating_text + .block_layers,
		.block_alternating_text + .block_family_support,
		.block_family_support + .block_layers {
			margin-top: 64px;
		}
		.block_family_support {
			& + .block_alternating_text + .block_layers {
				margin-top: 0;
			}
		}
		.layer_paragraph,
		p, ul {
			& + h2,
			& + h3,
			& + h4,
			& + h5,
			& + h6 {
				margin-top: 48px;
			}
		}
		a + .layer_paragraph {
			margin-top: 24px;
		}
		.layer_paragraph + .content_image,
		.block_layers + .block_our_programmes {
			margin-top: 32px;
		}
	}
}

.container {
	@include container();
	@include breakpoint( medium down ) {
		max-width: 800px;
	}
}