// ==================
// === STYLEGUIDE ===
// ==================
// Index:
// 0 - Global settins
// 1 - Text & Headings
// 2 - Buttons
// 3 - Links
// 4 - Lists
// 5 - Blockquote
// 6 - Forms
// 7 - Images
// 8 - Tables
// 9 - Videos
// ==================


// 0 - Global Settings
// ======================

// Layout
// =======

$sidebar-width: 252px;
$sidebar-margin: 89px;

// Fonts
// ======

// Font families
$body-font-family: 'Nunito';
$headings-font-family: 'Nunito';

// Font sizes
$global-font-size: 1rem;

// Font weights
$weight-normal: 400;
$weight-normal-italic: 400i;
$weight-semibold: 600;
$weight-bold: 700;
$weight-bold-italic: 700i;
$weight-extrabold: 800;
$weight-extrabold-italic: 800i;

// Line heights
$global-line-height: 2.125;
$headings-line-height: 1.2;

// 1 - Text & Headings
// ===================
$headings-font-weight: $weight-bold;
$headings-line-height: 1.3125;
$headings-margin-bottom: 0.22em;
$paragraphs-line-height: 2.125;
$global-margin-bottom: rem-calc(19);

h1, h2, h3, h4, h5, h6 {
	color: $headings-color;
	font-weight: $headings-font-weight;
	line-height: 1.2;
	margin-bottom: $headings-margin-bottom;
}

h1,
.heading--h1 {
	font-size: rem-calc(50);
	text-transform: uppercase;
	@include breakpoint(small down) {
		font-size: rem-calc(34);
	}
}

h2,
.heading--h2 {
	font-size: rem-calc(40);
	text-transform: uppercase;
	@include breakpoint(small down) {
		font-size: rem-calc(30);
	}
}

h3,
.heading--h3 {
	font-size: rem-calc(35);
	text-transform: uppercase;
	@include breakpoint(small down) {
		font-size: rem-calc(28);
	}
}

h4,
.heading--h4 {
	font-size: rem-calc(32);
	text-transform: uppercase;
	@include breakpoint(small down) {
		font-size: rem-calc(26);
	}
}

h5,
.heading--h5 {
	font-size: rem-calc(26);
	text-transform: uppercase;
}

h6,
.heading--h6 {
	font-size: rem-calc(22);
	text-transform: uppercase;

}

div, dl, dt, dd, ul, ol, li, pre , form, p, blockquote, th, td {
	color: $text-color;
	font-size: $global-font-size;
	line-height: $global-line-height;
}

p {
	font-size: $global-font-size;
	line-height: $paragraphs-line-height;
	margin-bottom: $global-margin-bottom;

	a {
		word-break: break-word;
	}
	
	strong{
		font-weight: $weight-bold;
	}
}

::selection {
	background: lighten($primary-color,50%);
}

::-moz-selection {
	background: lighten($primary-color,50%);
}

bold, 
b {
	font-weight: $weight-bold;
}


// 2 - Buttons
// ===========
.button {
	text-align: center;
	background-color: $primary-color;
	border-radius: 2px;
	line-height: 1.4;
	color: $white;
	display: inline-block;
	padding-top: 12px;
	padding-bottom: 12px;
	text-transform: uppercase;
	font-weight: $weight-extrabold;
	margin-bottom: $global-margin-bottom;
	min-width: 190px;
	padding-left: 20px;
	padding-right: 20px;
	font-size: rem-calc(16);

	@include transitions();
	&:hover{
		background-color: darken($primary-color, 10%);
		color: $white;
	}
	
	// Buttons inline
	& + .button{
		@include breakpoint(medium up) {
			margin-left: 5px;
		}
	}
	
	// Secondary button
	&--secondary {
		// blue
		background-color: $secondary-color;
		color: $white;

		&:hover{
			background-color: darken($secondary-color, 10%);
			color: $white;
		}
	}
	
	// Tertiary button
	&--tertiary {
		// blue
		background-color: $tertiary-color;
		color: $headings-color;
		
		&:hover{
			background-color: $tertiary-hover-color;
			color: $headings-color;
		}
	}

	// Quartenary button
	&--quartenary {
		background-color: $quartenary-color;
		color: $white;
		&:hover{
			background-color: darken($quartenary-color, 10%);
			color: $white;
		}
	}
	
	// White button
	&--white{
		background-color: white;
		color: $secondary-color;
		&:hover{
			background-color: darken($white, 10%);
			color: $secondary-color;
		}
	}
	
	// White + secondary button
	&--white-secondary{
		background-color: $white;
		color: $secondary-color;
		&:hover{
			background-color: $white;
			color: $secondary-color;
			opacity: 0.8;
		}
	}
}


// 3 - Links
// =========
a {
	color: $primary-color;
	line-height: inherit;
	text-decoration: none;
	font-weight: $weight-bold;
	@include transitions();

	&:hover {
		color: darken($primary-color, 10%);
	}
}

.link__alternative {
	color: $primary-color;
	display: inline-block;
	font-weight: $weight-extrabold;
	letter-spacing: 0.4px;	
	text-transform: uppercase;

	@include transitions();
	
	& + .link__alternative{
		margin-left: 20px;
	}
	
	&:hover {
		color: darken($primary-color, 10%);
	}
	
	&--red{
		color: $secondary-color;
		&:hover {
			color: rgba($secondary-color, 0.8);
		}
	}
	
	&--yellow{
		color: $tertiary-color;
		&:hover {
			color: rgba($tertiary-color, 0.8);
		}
	}
	&--white{
		color: $white;
		text-decoration: underline;
		&:hover {
			color: rgba($white, 0.8);
		}
	}
	
	// Right
	&:not(.link__alternative--left):after {
		content: '\f078';
		color: inherit;
		font-family: 'Icomoon';	    
		font-size: .85em;
		font-weight: normal;		
		line-height: 1;
		margin-left: 10px;
		position: relative;
		top: 0.07em;
	}
	
	// Alternative Left
	&--left{
		&:before {
			content: '\f078';
			color: inherit;
			font-family: 'Icomoon';
			font-size: .85em;
			font-weight: normal;
			line-height: 1;
			position: relative;
			top: 0.1em;
		}
		&:after {
			display: none;
		}
	}
}

// 4 - Lists
// =========
// Apply unstyled class to every html element that
// doesn't need to inherit the default style for lists (i.e. main menu)
ul:not(.unstyled) {
	margin-left: 20px;
	margin-bottom: $global-margin-bottom;

	li {
		font-size: rem-calc(16);
		line-height:$global-line-height;
		list-style-type: none;
		margin-bottom: 7px;
		position: relative;
		
		&::before {
			background: $tertiary-color;
			border-radius: 100%;
			content: "";
			display: inline-block;
			height: 7px;
			left: -20px;
			position: absolute;
			top: 13px;
			width: 7px;
		}
	}
}


// 5 - Blockquote
// ==============
blockquote {
	font-size: rem-calc(19);
	font-style: italic;
	font-weight: $weight-normal-italic;
	line-height: 2.3125rem;
	margin-bottom: 1em;
	padding: 8px 30px;
	position: relative;
	
	&::before{
		content: " ";		
		background: linear-gradient(to bottom, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
		height: 100%;
		left: 0;
		position: absolute;
		top: 0;
		width: 4px;
		
	}
	cite {
		color: $headings-color;
		display: block;
		font-size: rem-calc(19);
		font-weight: $weight-extrabold;
		font-style: italic;
	}
}

// 6 - Forms
// =========
$placeholder-color: #A1A1A1;
input[type=text],
input[type=email],
input[type=search],
select,
textarea {
	background: $white;	
	border: 1px solid $border-color;
	border-radius:2px;
	box-shadow: none;
	padding: 5px 10px;
	width: 100%;
	outline: 0;
	
	&::-webkit-input-placeholder { /* WebKit, Blink, Edge */
		color: $placeholder-color;
	}
	&:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
		color: $placeholder-color;
	}
	&::-moz-placeholder { /* Mozilla Firefox 19+ */
		color: $placeholder-color;
	}
	&:-ms-input-placeholder { /* Internet Explorer 10-11 */
		color: $placeholder-color;
	}
	&::-ms-input-placeholder { /* Microsoft Edge */
		color: $placeholder-color;
	}
}

textarea {
	min-height: 120px;
	resize: none;
}
select {
	-webkit-appearance: none; 
	-moz-appearance: none;
	appearance: none;
	background-size: 11px 8px;
	background-color: rgba(219,228,232,0.27);
	border-radius:2px;
	color: rgba($text-color, 0.8);
	background-position: calc(100% - 20px) 18px;
}

select::-ms-expand {
    display: none;
}

.select_wrapper {
	margin-left: auto;
	margin-right: auto;
	position: relative;
	width: 100%;
	
	&:after {
		content: '\f0a3';
		color: $white;
		display: block;
		font-family: 'icomoon';
		font-size: 1rem;
		height: 30px;
		line-height: 30px;
		pointer-events: none;
		position: absolute;
		right: 10px;
		bottom: 8px;
		text-align: center;
		width: 30px;
	}
	
	select {
		border-radius: 2px;
		background: $primary-color;
		color: $white;
		font-weight: $weight-normal;
		font-size: 0.8125rem;
		letter-spacing: 0.45px;
		height: 45px;
		text-transform: uppercase;
		width: 100%;
		padding-left: 20px;
		padding-right: 40px;
		cursor: pointer;
		padding: 6px 40px 4px 20px;

		option {
			background-color: $primary-color;
			opacity: 0.5;
		}
		
		&,
		&:active,
		&:focus {
			box-shadow: none;
			font-size: 0.9375rem;
			letter-spacing: 0.38px;			
			outline: none;
		}
		
		@include breakpoint( small down ) {
			width: 100%;
		}
	}
}
// 7 - Images
// ============================================================
.cover_image {
	height: 100%;
	overflow: hidden;
	width: 100%;
	position: relative;
	
	img {
		display: block;
		height: 100%;
		object-fit: cover;
		@include transitions();
		width: 100%;
	}

	.img {
		background-size: cover;
		background-position: center;
		height: 100%;
		width: 100%;
		@include transitions();
	}

	a:hover & img,
	&--link:hover img {
		transform: scale(1.1);
	}
}



.contain_image {
	height: 100%;
	overflow: hidden;
	width: 100%;
	position: relative;
	
	img {
		height: 100%;
		object-fit: contain;
		@include transitions();
		width: 100%;
	}

	.img {
		background-size: contain;
		background-position: center;
		background-repeat: no-repeat;
		height: 100%;
		width: 100%;
		@include transitions();
	}

	a:hover & img,
	&--link:hover img {
		transform: scale(1.1);
	}
}

// 8 - Tables
// ============
table {
	border: 1px solid #d0d7d9;
	border-radius: 3px;	
	margin-bottom: 1.875rem;
	margin-top: 30px;
	width: 100%;
	border-collapse: separate;

	tbody {
		tr {
			&:nth-child(even) {
				background: $background-1;
			}
		}
	}
	
	thead{
		background: $primary-color;
		border-radius: 3px 3px 0 0;	
		border-collapse: separate;	
	}
	
	th {
		color: $white;
		line-height: 1.875rem;
		font-weight: $weight-bold;
		padding: 3px 30px;
		text-align: left;
		font-size: rem-calc(16);
		white-space: pre;
	}
	
	td {
		color: $text-color;
		padding: 6px 31px;
		font-size: rem-calc(14);
	}
	
	@include breakpoint( small down ) {
		
		/* Force table to not be like tables anymore */
		table, thead, tbody, th, td { 
			display: block; 
		}
		
		/* Hide table headers (but not display: none;, for accessibility) */
		thead tr { 
			position: absolute;
			top: -9999px;
			left: -9999px;
		}
		
		tbody {
			tr {
				&:nth-child(even) {
					background: $background-1;
				}
			}
		}
		
		tr {
			border: 1px solid $border-color;
			display: flex;
			flex-direction: column;
		}
		
		td { 
			border: none;
			border-bottom: 1px solid $border-color; 
			position: relative;
		}
		
		td::before {
			content: attr(data-name);
			display: block;
			font-weight: $weight-normal;
		}
	}
}

// 9 - Video
// ===========================
.video {
	align-items: center;
	display: flex;
	height: 315px;
	justify-content: center;
	margin-bottom: 30px;
	margin-top: 30px;
	overflow: hidden;
	
	// For zoom in effect
	&:hover {
		img {
			transform: scale(1.1);
		}
		&::after {
			box-shadow: 0 0 3px 0 rgba(0,0,0, .02);
			color: darken($primary-color, 10%);
		}
	}
	
	&__container {
		height: 0;
		padding-top: 30px;
		padding-bottom: 56.25%;
		position: relative;
		overflow: hidden;
		width: 100%;
		
		iframe {
			border: none;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 2;
		}
	} 
	
	&::after {
		align-items: center;
		border: trasparent;
		color: $primary-color;
		content: ' ';
		background: url('/assets/images/flaticons/circle.svg');
		display: block;
		display: inline-flex;
		font-family: 'Flaticon';
		font-size: 1.15em;
		height: 55px;
		width: 55px;
		justify-content: center;
		position: absolute;
		pointer-events: none;
		z-index: 9;
		
		@include transitions();
		@include breakpoint( medium down ) {
			transform: scale(.8);
		}
	}
	
	&__image {
		align-items: center;
		cursor: pointer;
		display: inline-flex;
		justify-content: center;
		position: relative;
		height: 100%;
		left: 0;
		position: absolute;
		top: 0;
		width: 100%;
		z-index: 3;
		
		&:after{
			background-color: $video-overlay;
			content: '';
			height: 100%;
			left: 0;
			position: absolute;
			top: 0;
			width: 100%;
			z-index: 7;
			opacity: 0.5;
		}
		
		&:hover {
			&:before {
				opacity: .8;
			}
		}
	}
}

.alert_status {
	position: fixed;
	bottom: 25px;
	right: 25px;
	background: $white;
	z-index: 10;
	padding: 25px 25px 10px;
	box-shadow: 0 5px 20px #ddd;

	&__close {
		position: absolute;
		right: 15px;
		top: 10px;
		color: $primary-color;
		cursor: pointer;
		font-size: 0.9em;
		@include transitions();

		&:hover {
			color: $secondary-color;
		}
	}
}

.link-box {
    position: relative;
    &:focus-visible,
    &:focus {
        &::after {
            outline-width: 3px;
            outline-color: #78aeda;
            outline-style: solid;
        }
    }
    a {
        z-index: 1;
        &:after {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1;
            content: "";
        }
    }
}
