// ==================
// === LIGHTBOXES ===
// ==================
// Index:
// 1 - Settings
// 2 - General styles
// 3 - Arrows
// 4 - Spinner
// ==================

// 1 - Settings
// ========
$lightbox-overlay-color                     : rgba($secondary-color, 0.94);
$lightbox-background-color                  : white;
$lightbox-close-link-color                  : $headings-color;
$lightbox-close-link-color-hover            : $primary-color;
$lightbox-video-close-link-background-color : #000;
$lightbox-padding				            : 55px 60px 40px;


// 2 - General Styles
// ======================

// Hide the lightbox element by default
.lightbox {
	display: none;
}

// Show the Lightbox
.js-lightbox, .js-lightbox-redirect, .js-lightbox-video {
	display: none;
	position:fixed;
	top: 0; right: 0; bottom: 0; left: 0;
	z-index: 2147483647;
	text-align: center;
	white-space: nowrap;
	background: $lightbox-overlay;

	@include breakpoint(small only) {
		padding: 30px 20px;
	}

	&:before {
		content: '';
		display: inline-block;
		height: 100%;
		vertical-align: middle;
		margin-right: -0.25em;
	}
	&-content {
		box-shadow:0 2px 37px 0 rgba(0,0,0,0.15);
		max-width: 1040px;
		position: relative;
		text-align: left;
		vertical-align: middle;
		display: inline-block;
		overflow: auto;
		padding: $lightbox-padding;
		@include breakpoint(medium down) {
			padding: 30px 45px;
		}
		@include breakpoint(small only) {
			padding: 20px 0;
		}
		background-color: $lightbox-background-color;
		margin-left: 5%;
		margin-right: 5%;
		max-height: 95%;
		white-space: normal;
		min-width: 600px;
		border-radius: 3px;
		@include breakpoint(medium down) {
			min-width: 500px;
		}
		@include breakpoint(small only) {
			width: 100%;
			margin: 0;
			min-width: 0;
		}
	}
	&-inner {
		display: block;
	}
	&-close-icon {
		position: absolute;
		z-index: 9999;
		top: 25px;
		right: 25px;
	    font-size: 14px;
		line-height: 25px;
		width: 25px;
		cursor: pointer;
		text-align: center;
		font-family: Arial, sans-serif;
		color: $lightbox-close-link-color;
		@include transitions();
		&:hover {
			color: $lightbox-close-link-color-hover;
		}
		@include breakpoint( small down ) {
			right: 10px;
			top: 12px;
		}
	}

	// Videos / Iframes only
	&-iframe {
		.js-lightbox-content {
			padding: 0;
			background-color: transparent;
			width: 80%;
			height: 80%;
			@include breakpoint(medium only) {
				height: 400px;
			}
			@include breakpoint(small only) {
				height: 300px;
			}
		}
		.js-lightbox-close-icon {
			top: 0;
			right: 0;
			background-color: $lightbox-video-close-link-background-color;
			width: auto;
			padding: 5px 15px;
			&:hover {
				background-color: none;
				color: white;
			}
		}
		iframe {
			display: block;
			width: 100%;
			height: 100%;
		}
	}
	&-video {
		.js-lightbox-video-content {
			padding: 0 !important;
			background-color: transparent;
			width: 80%;
			height: auto !important;
			max-height: 100% !important;
		}
		.js-lightbox-close-icon {
			top: 0;
			right: 0;
			background-color: $lightbox-video-close-link-background-color;
			width: auto;
			padding: 5px 15px;
			&:hover {
				background-color: none;
				color: white;
			}
		}
		.video-container {
			position: relative;
			padding-bottom: 56.25%;
			padding-top: 30px;
			height: 0;
			overflow: hidden;
		}
		iframe {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}
	}

	// Map Overview Lightbox
	&-map {
		background-color: transparent;

		.js-lightbox-close-icon {
			color: $secondary-color;
			font-size: .7rem;
			@include transitions();
			&:hover {
				color: darken( $secondary-color, 10% );
			}
		}
	}
	// Gallery
	&-gallery {
		.js-lightbox-content {
			align-items: flex-start;
			display: inline-flex;
			justify-content: center;
			padding: 50px 180px;
			@include breakpoint( medium down ) {
				padding: 30px 50px;
			}
			img {
				height: auto!important;
			}
		}
	}
}

// 3 - Arrows for galleries
// ===========================
$lightbox_arrows_position: 64px;
$lightbox_arrows_position__medium: 20px;

.lightbox__arrow {
	$block: &;

	cursor: pointer;
	margin-top: -26px;
	position: absolute;
	top: 50%;
	z-index: 20;

	&:hover {
		&:after {
			color: $primary-color;
		}
	}

	&:after {
		color: $secondary-color;
		display: block;
		font-family: 'icomoon';
		font-size: 1.9rem;
		@include transitions();
	}

	&--previous {
		left: $lightbox_arrows_position;

		@include breakpoint( medium down ) {
			left: $lightbox_arrows_position__medium;
		}

		&:after {
			content: '\f0a4';
		}
	}

	&--next {
		right: $lightbox_arrows_position;

		@include breakpoint( medium down ) {
			right: $lightbox_arrows_position__medium;
		}

		&:after {
			content: '\f078';
		}
	}
}

// 4 - Loading Spinner (Ajax Only)
// =================================
.loading-spinner {
	@include loading-spinner($width: 30px, $height: 30px, $border-width: 1px, $color: #{$secondary-color});
	top: 40%;
}