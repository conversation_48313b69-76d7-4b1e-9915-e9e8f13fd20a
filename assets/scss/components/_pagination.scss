// ==================
// === PAGINATION ===
// ==================
// Index:
// 1 - General
// 2 - Arrows
// ==================


// 1 - General
// =================
$pagination_button_width: 41px; 
$pagination_button_height: 41px;
$pagination_font_weight: $weight-bold;

.pagination_block {
	list-style-type: none;
	text-align: center;
	
	// Item Wrapper
	&__item {
		align-items: center;
		background: lighten($tertiary-color, 30%);
		border-radius: 2px;
		color: $headings-color;
		display: inline-flex;
		font-size: 1rem;
		font-weight: $pagination_font_weight;
		height: $pagination_button_height;
		justify-content: center;
		line-height: $pagination_button_height;
		margin: 0 .001rem;
		width: $pagination_button_width;
		@include transitions();

		&:hover,
		&.current {
			background-color: $tertiary-color;
		}
	} 
	
	// The actual link inside the list item
	&__link {
		color: lighten($text-color,30%);
		height: 100%;
		width: 100%;
		@include transitions();

		&:hover {
			color: $text-color;
		}
	}
	
// 2 - Arrows
// =================
	&__arrow {
		align-items: center;
		display: inline-flex;
		font-size: .8125rem;
		height: $pagination_button_height;
		justify-content: center;
		line-height: $pagination_button_height;
	}

	&__arrow_link {
		padding: 0 .28125rem;

		&:after {
			color: $text-color;
			display: block;
			font-family: 'icomoon';
			height: 100%;
			width: 100%;
			@include transitions();
		}

		&:hover:after {
			color: $primary-color;
		}
	}

	&__arrow--next {
		.pagination_block__arrow_link:after {
			content: '\f054';
		}
	}

	&__arrow--previous {
		.pagination_block__arrow_link:after {
			content: '\f053';
		}
	}
}