div.json-viewer {
  background-color: #2d3b45;
  color: white;
  font-family: monospace, monospace;
  padding: 40px 50px;
  font-size: 12px;
  box-shadow: 0 14px 34px rgba(0, 0, 0, 0.4);
  position: relative;
  margin: 40px 0;
  &:before {
    content: "BLOCK DATA";
    position: absolute;
    right: 10px;
    top: 3px;
    font-size: 17px;
    letter-spacing: -2px;
    color: #00d0ff;
    font-weight: bold;
    font-family: Helvetica, Arial, sans-serif;
    background: -webkit-linear-gradient(left, #00d0ff, #c000ff);
    -webkit-background-clip: text;
    padding-right: 2px;
    -webkit-text-fill-color: transparent;
  }
}
span.bracket {
  font-weight: bold;
  font-size: 11px;
  color: white;
  -webkit-font-smoothing: subpixel-antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* Syntax highlighting for JSON objects */
ul.json-dict, ol.json-array {
  list-style-type: none;
  margin: 0 0 0 1px;
  border-left: 1px solid rgba(204, 204, 204, 0.07);
  padding-left: 20px;
  margin-left: 3px !important;
  li {
    line-height: 1.5 !important;
    margin-bottom: 0 !important;
    font-size: 12px !important;
    -webkit-font-smoothing: subpixel-antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    color: #00d3fb !important;
    &:before {
      display: none !important;
    }
  }
}
.json-string {
  color: #ea4d80;
}
.json-literal {
  color: #1A01CC;
  font-weight: bold;
}

/* Toggle button */
a.json-toggle {
  position: relative;
  color: inherit;
  text-decoration: none;
  font-weight: 600;
}
a.json-toggle:focus {
  outline: none;
}
a.json-toggle:before {
  content: "";
  position: absolute;
  left: -14px;
  top: 5px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 6.9px 4px 0 4px;
  border-color: white transparent transparent transparent;
}
a.json-toggle.collapsed:before {
  transform: rotate(29deg); /* Use rotated down arrow, prevents right arrow appearing smaller than down arrow in some browsers */
  -ms-transform: rotate(29deg);
  -webkit-transform: rotate(29deg);
}

/* Collapsable placeholder links */
a.json-placeholder {
  color: #aaa;
  padding: 0 1em;
  text-decoration: none;
}
a.json-placeholder:hover {
  text-decoration: underline;
}