/**
 * Follow-Height JS 0.1.0
 * ----------------------
 * <AUTHOR> <www.christophecorbalan.com>
 * https://github.com/blackslash333/follow-height-js
 * License: MIT
 */

(function(global) {

	// ----------------------------------------------------------------------------- //

	/**
	 * FollowHeight Function
	 * @param object options
	 */
	global.FollowHeight = function(options) {

		// Can be useful guys
		var context = this;

		// Destroyed object ?
		this.destroyed = false;

		// Default options
		this.options = {

			// CSS Selector to use
			selector: '[data-follow-height]',

			// Breakpoint selector for specific elements
			bp_selector: '[data-follow-height-break-on]',

			// Default breakpoint where we wants to reset heights
			break_on: '', // e.g. medium or width (640, 1024, etc.)

			// Default breakpoints, can be changed
			breakpoints: {
				small: 640,
				medium: 1024,
				large: 1200
			}

		};

		// Check if there is custom options
		if(options !== null && typeof options === 'object') {
			this.options = this.mergeOptions(this.options, options);
		}

		// Let's see if our selectors are in the page, if yes update the heights
		var elements = document.querySelector(this.options.selector) !== null;

		window.onload = function () {
			return (!context.destroyed) ? context.update() : null;
		}

		var resizeTimer = null;
		window.onresize = function(event) {
			clearTimeout(resizeTimer);
			resizeTimer = setTimeout(function() {
				return (!context.destroyed) ? context.update() : null;
			}, 250);
		};

	};

	// ----------------------------------------------------------------------------- //

	// Main Functions
	// -----------------

	/**
	 * Update Function
	 * @param  string selector   
	 * @param  string bp_selector
	 * @return N/A
	 */
	FollowHeight.prototype.update = function(selector, bp_selector) {

		// Context !
		var context = this;

		// Wants to use custom selectors ? Of course you can
		var selector = (typeof selector === 'undefined') ? this.options.selector : selector;
		var bp_selector = (typeof bp_selector === 'undefined') ? this.options.bp_selector : bp_selector;
		var formatted_selector = selector.replace('[', '').replace(']', '');
		var formatted_bp_selector = bp_selector.replace('[', '').replace(']', '');

		// Let's find all the elements and resize them
		var elements = document.querySelectorAll(selector);

		// First, remove the transitions (Fix for safari)
		this.removeTransitions(this.options.selector);

		// Save better heights
		var better_heights = [];

		// Change heights !
		this.forEach(elements, function(i, $target) {

			// Find the better height for this specific attribute id (data-follow-height=myid)
			var id = $target.getAttribute(formatted_selector);
			better_heights[id] = Math.max.apply(null, Array.prototype.map.call(document.querySelectorAll('[' + formatted_selector + '="' + id + '"]'), function(el) {
			    var original_style_height = el.style.height;
			    el.style.height = '';
			    var offset_height = el.offsetHeight;
			    el.style.height = original_style_height;
			    return offset_height;
			}));

			if(context.options.break_on != '') {
				if(context.options.break_on % 1 === 0 && window.innerWidth <= context.options.break_on) { // Numeric value
					return $target.style.height = '';
				} else if(context.options.breakpoints[context.options.break_on] && window.innerWidth <= context.options.breakpoints[context.options.break_on]) {
					return $target.style.height = '';
				} else return $target.style.height = better_heights[id] + 'px';
			}
			if($target.hasAttribute(formatted_bp_selector) && ((context.options.breakpoints[$target.getAttribute(formatted_bp_selector)] && window.innerWidth <= context.options.breakpoints[$target.getAttribute(formatted_bp_selector)]) || window.innerWidth <= $target.getAttribute(formatted_bp_selector))) {
				// Meh, it's breakpoint time, let's remove the height
				return $target.style.height = '';
			}
			$target.style.height = better_heights[id] + 'px';

		});

		// Restore Transitions
		this.restoreTransitions(this.options.selector);

	};

	FollowHeight.prototype.destroy = function() {
		var elements = document.querySelectorAll(this.options.selector);
		this.forEach(elements, function(index, $target) {
			return $target.style.height = '';
		});
		return this.destroyed = true;
	};

	// ----------------------------------------------------------------------------- //

	// Helpful Functions
	// -----------------
	
	/**
	 * Merge Options
	 * ref: http://stackoverflow.com/a/171256
	 * @param  object obj1
	 * @param  object obj2
	 * @return object
	 */
	FollowHeight.prototype.mergeOptions = function(primary, secondary){
	    var object = {};
	    for (var attrname in primary) { object[attrname] = primary[attrname]; }
	    for (var attrname in secondary) { object[attrname] = secondary[attrname]; }
	    return object;
	}

	/**
	 * forEach Function
	 * @param  array    array   
	 * @param  function callback
	 * @param  object   scope   
	 * @return N/A
	 */
	FollowHeight.prototype.forEach = function (array, callback, scope) {
		for (var i = 0; i < array.length; i++) {
			callback.call(scope, i, array[i]);
		}
	};

	/**
	 * Remove Transitions from Elements
	 * @param  string selector CSS/JS Selector
	 * @return N/A
	 */
	FollowHeight.prototype.removeTransitions = function(selector) {
		var elements = document.querySelectorAll(selector);
		this.forEach(elements, function(index, $target) {
			$target.style.webkitTransition = 'none';
			$target.style.mozTransition = 'none';
			$target.style.msTransition = 'none';
			$target.style.oTransition = 'none';
			$target.style.transition = 'none';
		});
	};

	/**
	 * Restore Transitions for Elements
	 * @param  string selector CSS/JS Selector
	 * @return N/A
	 */
	FollowHeight.prototype.restoreTransitions = function(selector) {
		var elements = document.querySelectorAll(selector);
		this.forEach(elements, function(index, $target) {
			$target.style.webkitTransition = '';
			$target.style.mozTransition = '';
			$target.style.msTransition = '';
			$target.style.oTransition = '';
			$target.style.transition = '';
		});
	};

	// ----------------------------------------------------------------------------- //
	
	new FollowHeight();

})(window);