/**
 * OPENING AND CLOSING DROPDOWN MENUS
 *
 * Usage:
 * 1) Add data-dropdown-target to the menu trigger and set a custom value (i.e. data-dropdown-target="mymenu1").
 * 2) Add data-dropdown with the same value as the trigger to the actual dropdown.
 * 3) If you want the dropdown to open also on hover you can add data-dropdown-hover to the trigger. 
 *
 * Example:
 * 	<a href="#" data-dropdown-target="my-menu-1" data-dropdown-hover>Menu Opener</a>
 *	<div data-dropdown="my-menu-1">
 *		<a href="/mypage">Link</a>
 *		<a href="/mypage2">Link</a> 
 *	</div>
 */


$(document).ready(function() {

		var currently_hovering = ''; 		// Name of the hovered element
		var closing_menu = false;
		var opening_menu = ''; 				// Name of the opening menu
		var dropdowns_hover_selector = ''; 
		var max_wating_time = 400; 			// Time waited before checking if the user is out of the menu item

		/**
		 * SHARED METHODS FOR OPENING AND CLOSING
		 */
		
		// Opens dropdown
		$.openDropdown = function( trigger ) {
			var $trigger = $(trigger);	

			opening_menu = $trigger.attr('data-dropdown-target');

			// Closing other dropdowns
			if ( ! closing_menu && $trigger.is(':not(.open)') ) {
				closing_menu = true;
				$('[data-dropdown-target].open').not($trigger).click();
				closing_menu = false;
			}

			// Open current dropdown
			var $dropdown = $('[data-dropdown="' + opening_menu + '"]');
			$dropdown.slideDown(400, function(){
				opening_menu = '';
			});
			$('.icon', $trigger).removeClass('icon-chevron-down2').addClass('icon-chevron-up2');
			$trigger.addClass('open');	
		};

		// Closes dropdown
		$.closeDropdown = function( element ) {
			var name = $(element).attr('data-dropdown') ? $(element).attr('data-dropdown') : $(element).attr('data-dropdown-target');
			var $trigger = $('[data-dropdown-target="' + name + '"]');

			if ( name == opening_menu ) {
				return;
			}

			// Open current dropdown
			var $dropdown = $('[data-dropdown="' + $trigger.attr('data-dropdown-target') + '"]');
			$dropdown.slideUp();
			$('.icon', $trigger).addClass('icon-chevron-down2').removeClass('icon-chevron-up2');
			$trigger.removeClass('open');		
		};

		/**
		 * CLICKABLE TRIGGER
		 */

		// Triggering menu open/close on click
		$('[data-dropdown]').hide();
		$('[data-dropdown-target]').off('click');
		$('[data-dropdown-target]').on('click', function(e){
			e.preventDefault();
			if ( $(this).hasClass('open') ) {
				$.closeDropdown( this );
			} else {
				$.openDropdown( this );
			}
		});

		/**
		 * HOVER TRIGGER
		 */

		// Getting a list of selectors for hoverable drop down menus
		$('[data-dropdown-hover]').each(function(){
			var menu_name = $(this).attr('data-dropdown-target');
			var dropdowns = [];
			if(menu_name && $('[data-dropdown="' + menu_name + '"]')) {
				dropdowns.push('[data-dropdown="' + menu_name + '"]');
				$('[data-dropdown="' + menu_name + '"]').attr('data-dropdown-hover','');
			}
			dropdowns_hover_selector = dropdowns.join(',');
		});

		// In case there are hoverable dropdown menus
		if(dropdowns_hover_selector) {
			// Triggering menu open/close on hover intent
			$('[data-dropdown-hover]').hoverIntent( 
				function(){
					$.openDropdown( this );
				},
				function(){
					var trigger = this;
					var name = $(this).attr('data-dropdown-target');
					// Waits a little bit in case the user is moving from the link to the dropdown
					setTimeout(function(){
						if ( currently_hovering !== name ) {
							$.closeDropdown( trigger );
						}
					}, max_wating_time);
				}
			);

			// Detecting name of current dropdown hovered (both trigger and actual menu)
			// so if a user goes from the link to the dropdown (and the opposite) the dropdown won't be closed
			$(dropdowns_hover_selector).on('mouseover',
				function(){
					var $menu = $(this);
					var name = $menu.attr('data-dropdown');
					currently_hovering = name;
				}
			);
			$(dropdowns_hover_selector).on('mouseout',
				function(){
					var $menu = $(this);
					var name = $menu.attr('data-dropdown');
					currently_hovering = '';
				}
			);

			// Detecting just dropdown hovering
			$('[data-dropdown]').on('mouseout',
				function(){
					var menu = this;
					var name = $(menu).attr('data-dropdown');
					// Waits a little bit in case the user is moving from the dropdown to the link
					setTimeout(function(){
						if ( currently_hovering != name ) {
							$.closeDropdown( menu );
						}
					}, max_wating_time);
				}
			);
		}
});