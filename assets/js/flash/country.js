/**
 * JS User Country Detector
 * ------------------------
 */

function getUserCountry(callback) {
	if(readCookie('user_country')) return callback(readCookie('user_country'));
	$.getJSON('http://ip-api.com/json/?callback=?', function(data) {
        createCookie('user_country', data.countryCode);
        return callback(data.countryCode);
    });
}

// Usage:
// ======
// getUserCountry(function(country) {
// 	console.log('user country: ' + country);
// });