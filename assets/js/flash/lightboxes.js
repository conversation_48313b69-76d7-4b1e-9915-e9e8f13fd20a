/**
 * Lightbox System (Featherlight JS)
 * ---------------------------------
 */

// FeatherLight Global Object
var $featherLight = $.featherlight;

// Cancel featherLight loading
$.featherlight.autoBind = false;

// Custom configuration
var lightbox_settings = {
	namespace: 'js-lightbox',
	targetAttr: 'data-lightbox',
	closeIcon: '<i class="icon icon-close"></i>',
	persist: true
};

// Custom configuration for gallery
var lightbox_gallery_settings = {
	namespace: 'js-lightbox',
	targetAttr: 'data-lightbox-gallery',
	closeIcon: '<i class="icon icon-close"></i>',
	previousIcon: '<div class="lightbox__arrow lightbox__arrow--previous"></div>',
	nextIcon: '<div class="lightbox__arrow lightbox__arrow--next"></div>',
	variant: 'js-lightbox-gallery'
};

$(document).ready(function() {

	// Change the data-attribute
	$('[data-lightbox]').featherlight(lightbox_settings);

	// Change the data-attribute
	$('[data-lightbox-gallery]').featherlightGallery(lightbox_gallery_settings);

	// Ajax Call !
	$('[data-lightbox-ajax]').click(function(e) {
		e.preventDefault();
		var _ajax_url = $(this).attr('data-lightbox-ajax');
		$featherLight('<div class="lightbox"><div data-ajax-call="' + _ajax_url + '" data-ajax-auto-delete><div class="loading-spinner"></div></div></div>', lightbox_settings);
	});

	// Video Call !
	$('[data-lightbox-video]').click(function(e) {

		e.preventDefault();

		// Get Video URL
		var _video_url = $(this).attr('data-lightbox-video');

		// Get the video type
		var _video_type = getVideoType(_video_url);

		// Get the video ID
		var _video_id = getVideoID(_video_url);

		// Generate the iframe
		if(_video_type == 'youtube') {
			var _output = '<div class="video-container"><iframe src="//www.youtube.com/embed/' + _video_id + '?autoplay=1&showinfo=0" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>';
		} else if(_video_type == 'vimeo') {
			var _output = '<div class="video-container"><iframe src="//player.vimeo.com/video/' + _video_id + '?title=0&amp;byline=0&amp;portrait=0&amp;color=96c159&amp;api=1&amp;autoplay=1&amp;player_id=video_' + _video_id + '" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>';
		}

		// Generate the lightbox
		$featherLight(_output, {
			namespace: 'js-lightbox-video',
			targetAttr: 'data-lightbox',
			closeIcon: '<i class="icon icon-close"></i>'
		});

	});

	// Execute this only one time
	oneTimeExecution(function() {

		// We want to open a group lightbox
		var group_lightboxes = [];
		var $active_group_lightbox = '';
		$(document).on('click', '[data-lightbox-group]', function(e) {

			// Get the lightbox group name
			// and find all the lightboxes
			// from this group
			e.preventDefault();
			var group = $(this).attr('data-lightbox-group');
			group_lightboxes = [];
			$('[data-lightbox-group="' + group + '"]').each(function() {
				group_lightboxes.push($($(this).attr('data-lightbox')));
			});

			// Change the active lightbox group
			$active_group_lightbox = $($(this).attr('data-lightbox'));
			
		});

		// Next/Previous Links
		$(document).on('click', '[data-lightbox-next], [data-lightbox-previous]', function(e) {
			e.preventDefault();
			
			// Get the active lightbox, go each
			// lightboxes into the current group
			// and find the next lightbox to show
			var $prev = group_lightboxes[group_lightboxes.length - 1];
			var $next = group_lightboxes[0];
			$.each(group_lightboxes, function(index, $lightbox) {
				if($lightbox.is($active_group_lightbox)) {
					if(group_lightboxes[index - 1] !== void 0) $prev = group_lightboxes[index - 1];
					if(group_lightboxes[index + 1] !== void 0) $next = group_lightboxes[index + 1];
				}
			});

			// Update the lightbox content
			if($(this).is('[data-lightbox-previous]')) {
				$.featherlight.current().setContent($prev.clone());
				$active_group_lightbox = $prev;
			} else {
				$.featherlight.current().setContent($next.clone());
				$active_group_lightbox = $next;
			}

		});

	});

});

// -------------------------------------------------------------------------------------
// Helpers
// -------------------------------------------------------------------------------------

// Get Video Type
function getVideoType(video_url) {
	if(video_url.indexOf('yout') >= 0) return 'youtube';
	if(video_url.indexOf('vimeo') >= 0) return 'vimeo';
}

// Get Video ID based on the URL
function getVideoID(video_url) {

	var _video_type = getVideoType(video_url);
	var _video_id;

	// Get youtube video ID
	if(_video_type == "youtube") {
		var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
		var match = video_url.match(regExp);
		_video_id = (2 in match) ? match[2] : '';
	}

	// Get Vimeo video ID
	if(_video_type == "vimeo") {
		if(video_url.indexOf("clip_id") > -1) {
			var regExp = /(clip_id=(\d+))/;
			var match = video_url.match(regExp);
		} else if(video_url.indexOf("player") > -1) {
			var regExp = /player\.vimeo\.com\/video\/([0-9]*)/;
			var match = video_url.match(regExp);
			match[2] = match[1];
		} else {
			var regExp = /(?:http|https):\/\/(www\.)?vimeo.com\/(\d+)($|\/)/;
			var match = video_url.match(regExp);
		}
		_video_id = (2 in match) ? match[2] : '';
	}

	return _video_id;

}