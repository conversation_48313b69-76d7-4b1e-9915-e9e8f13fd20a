// ======================================================
// Sync a select with a list of links
// =======================================================
// 
// Usage
// 
// <div id="links">
// 	<a href="#" data-tab-target="tab1">Link 1</a>
// 	<a href="#" data-tab-target="tab2">Link 2</a>
// </div>
// 
// <select data-sync-select="#links|data-tab-target">
// 	<option value="tab1">Link 1</option>
// 	<option value="tab2">Link 2</option>
// </select>


$(document).ready(function() {

	if($('[data-sync-select]').length) {
		var prevent_refresh = false; // prevents select -> button -> select infinite loop

		$('[data-sync-select]').each( function(index, select){
			var settings = $(select).attr('data-sync-select').split('|');
			var $links = $(settings[0]); // The buttons list

			// If settings are not correct, the function just returns
			if(settings.length != 2 || $links.length == 0 || $('[' + settings[1] + ']').length == 0) {
				return;
			}

			// Sync select --> buttons
			$(select).off('change');
			$(select).on('change', function(){

				if ( prevent_refresh ) {
					return;
				}

				var current_sector = $(this).val();

				$('[' + settings[1] + '="' + current_sector + '"]').click();

			});

			// Sync buttons --> select
			$('[' + settings[1] + ']', $links).off('click');
			$('[' + settings[1] + ']', $links).on('click', function(){
				var current_sector = $(this).attr(settings[1]);
				prevent_refresh = true;
				$(select).val(current_sector);
				prevent_refresh = false;
			});
		});

	}

});