/**
 * JS Small Search System
 * ----------------------
 */
$(document).on('submit', '[data-search-form]', function(e) {
	e.preventDefault();

	// Selectors
	var $form = $(this);
	var $input = $(this).find('input[type=search]');
	var $error = $(this).parent().find('[data-no-search-results]');
	var $results = $(this).parent().find('[data-search-results]');
	var template = $(this).parent().find('[data-search-results-template]')[0].outerHTML;

	// Get the search keywords
	var keywords = $input.val();

	// No value :(
	if(!keywords.replace(/\s/g, '').length) {
		return $input.val('');
	}

	// Look for results
	var results = [];
	$.each(search_data, function(index, entry) {
		if(entry.title.toLowerCase().indexOf(keywords) >= 0) {
			results.push(entry);
		}
	});

	// Hide everything (results, error message, etc)
	$error.hide();
	$results.hide();

	// Remove all existing results
	$results.find('> *:not([data-search-results-template])').remove();

	// No results :(
	if(!results.length) {
		return $error.fadeIn();
	}

	// We found something :)
	$.each(results, function(index, entry) {
		$results.append(template
			.replace(/\[\[\sentry.url\s\]\]/g, entry.url)
			.replace(/\[\[\sentry.title\s\]\]/g, entry.title)
			.replace(/\[\[\sentry.date\s\]\]/g, entry.date)
			.replace(/\[\[\sentry.type\s\]\]/g, entry.component.replace(' Module', ''))
			.replace(/data-search-results-template/g, ''));
	});

	// Show the results
	$results.fadeIn();

});