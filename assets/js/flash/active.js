/**
 * ADDING "ACTIVE" CLASS TO ANCHORS AND TAGS BASED ON CURRENT URL
 * 
 *
 * Exaples of usage:
 * 
 * 	<a href="/mypage" data-active>Link</a>   				<--- To target a single link
 *
 * 
 *	<div data-active-children>      						<--- To add a list of links in a tag
 *		<a href="/mypage">Link</a>
 *		<a href="/mypage2" data-active-ignore>Link</a>   	<--- To ignore a link in a list
 *	</div>
 *
 *
 *	<div data-active-on="/my-url|/my-url-2">Fake Link</div>   		<--- To target any tag
 */


 function flashActive() {
	// Prepares the collection of elements
	var $links = $('[data-active]:not([data-active-ignore]), [data-active-children] a:not([data-active-ignore]), [data-active-on]:not([data-active-ignore])');

	// Stops if there are no links to watch for
	if ( ! $links.length ) {
		return;
	}

	$links.each( function(){
		var $link = $(this);
	   	var matches = false;
		
		// Check if already applied
		$link.addClass('js_init--flashActive');

		// Gets the url of the current element
		if ( $link.attr('data-active-on') ) {
			var link_urls = $link.attr('data-active-on').split('|');
		} else {
			var link_urls = [ $link.attr('href') ];
		}

		if ( link_urls ) {

		link_urls.forEach(function( link ){

				switch( link ) {
					// If the link url is to the home
					case '/':
						matches = ( window.location.pathname === link );
						break;
					case '#':
						matches = false;
						break;
					// If the link url is to any other page
					default:
						matches = ( window.location.pathname.indexOf(link) >= 0 );
						break;
				}

		});

			// If url matches, the class is applied
			if ( matches ) {
				$link.addClass('active');
			}
		}
	});
}

// Init
 $(document).ready( function(){
	flashActive();
 });