/**
 * Flash Slider System (SlickJS)
 * -----------------------------
 */

function SB_Sliders() {

    // Instanciate new sliders
    // -----------------------
    $('[data-slider]').each(function() {

        var $instance = $(this);
        if($instance.is('[data-slidered]')) return;
        var slider_name = $instance.attr('data-slider');

        // Responsive Rules
        // ----------------
        var items = ($instance.is('[data-slider-items]') ? parseInt($instance.attr('data-slider-items')) : 1);
        var items_on_xlarge = ($instance.is('[data-slider-items-on-xlarge]') ? parseInt($instance.attr('data-slider-items-on-xlarge')) : items);
        var items_on_large = ($instance.is('[data-slider-items-on-large]') ? parseInt($instance.attr('data-slider-items-on-large')) : items);
        var items_on_medium = ($instance.is('[data-slider-items-on-medium]') ? parseInt($instance.attr('data-slider-items-on-medium')) : items);
        var items_on_small = ($instance.is('[data-slider-items-on-small]') ? parseInt($instance.attr('data-slider-items-on-small')) : items);

        // Create the Slick Instance
        // -------------------------
        $instance.slick({
            infinite       : true,
            arrows         : ($instance.is('[data-slider-arrows]') ? true : false),
            slidesToShow   : items_on_xlarge,
            slidesToScroll : 1,
            autoplay       : ($instance.is('[data-slider-autoplay]') ? true : false),
            autoplaySpeed  : ($instance.is('[data-slider-autoplay]') && $instance.attr('data-slider-autoplay') ? parseInt($instance.attr('data-slider-autoplay')) : 3000),
            centerMode     : ($instance.is('[data-slider-center-mode]') ? true : false),
            adaptiveHeight : true,
            swipe          : false,
            variableWidth  : ($instance.is('[data-variable-width]') ? true : false),
            dots           : ($instance.is('[data-slider-dots]') ? true : false),
            responsive: [
                {
                    breakpoint: 1200,
                    settings: { slidesToShow: items_on_large }
                },
                {
                    breakpoint: 1024,
                    settings: { slidesToShow: items_on_medium }
                },
                {
                    breakpoint: 640,
                    settings: { slidesToShow: items_on_small, variableWidth: false }
                }
            ]
        });
        $instance.attr('data-slidered', '');

        // Remove/Add active classes on navigation elements
        // ------------------------------------------------
        if(slider_name) {
            $('[data-slider-for="' + slider_name + '"][data-slider-slide]').removeClass('active');
            $('[data-slider-for="' + slider_name + '"][data-slider-slide=1]').addClass('active');
            $instance.on('beforeChange', function(event, slick, currentSlide, nextSlide){
                var slide = (nextSlide + 1);
                $('[data-slider-for="' + slider_name + '"][data-slider-slide]').removeClass('active');
                $('[data-slider-for="' + slider_name + '"][data-slider-slide=' + slide + ']').addClass('active');
            });
        }

    });

    // Prev Links
    // ----------
    $('[data-slider-slide="prev"]').each(function() {
        var $instance = $('[data-slider="' + $(this).attr('data-slider-for') + '"]');
        if($instance.length < 0 || $(this).is('[data-slidered]')) return;
        $(this).attr('data-slidered', '');
        $(this).click(function(e) {
            e.preventDefault();
            $instance.slick('slickPrev');
        });
    });

    // Next Links
    // ----------
    $('[data-slider-slide="next"]').each(function() {
        var $instance = $('[data-slider="' + $(this).attr('data-slider-for') + '"]');
        if($instance.length <= 0 || $(this).is('[data-slidered]')) return;
        $(this).attr('data-slidered', '');
        $(this).click(function(e) {
            e.preventDefault();
            $instance.slick('slickNext');
        });
    });

    // Custom Navigation
    // -----------------
    $('[data-slider-slide]').each(function() {
        var slide = $(this).attr('data-slider-slide');
        var $instance = $('[data-slider="' + $(this).attr('data-slider-for') + '"]');
        if(slide == 'next' || slide == 'prev' || $instance.length <= 0 || $(this).is('[data-slidered]')) return;
        $(this).attr('data-slidered', '');
        $(this).click(function(e) {
            e.preventDefault();
            $instance.slick('slickGoTo', parseInt(slide) - 1);
        });
    });

}

$(document).ready(function() { SB_Sliders(); });