/**
 * Simple & Lightweight Lazy Loading JS System
 * -------------------------------------------
 */
$(document).ready(function() {

	// Is the element on viewport ?
	$.fn.isInViewport = function() {
	    var elementTop = $(this).offset().top;
	    var elementBottom = elementTop + $(this).outerHeight();
	    var viewportTop = $(window).scrollTop();
	    var viewportBottom = viewportTop + $(window).height();
	    return elementBottom > viewportTop && elementTop < viewportBottom;
	};

	// Remove specific element transitions
	$.fn.removeTransitions = function() {
		$(this)[0].style.webkitTransition = 'none';
		$(this)[0].style.mozTransition = 'none';
		$(this)[0].style.msTransition = 'none';
		$(this)[0].style.oTransition = 'none';
		$(this)[0].style.transition = 'none';
	};

	// Restore element transitions
	$.fn.restoreTransitions = function() {
		$(this)[0].style.webkitTransition = '';
		$(this)[0].style.mozTransition = '';
		$(this)[0].style.msTransition = '';
		$(this)[0].style.oTransition = '';
		$(this)[0].style.transition = '';
	};

	// Lazy load images function
	var fetchImages = function() {
		$('[data-lazy]').each(function() {
			if($(this).isInViewport()) {
				
				var $target = $(this);
				var tag = $target.prop('tagName');
				var src = $target.attr('data-lazy');

				// Preload the image
				$('<img/>').attr('src', src).on('load', function() {
					if(tag == 'IMG') {
						$target.attr('src', src);
					} else {
						$target.css('background-image', 'url(' + src + ')');
					}
					$target.removeTransitions();
					$target.css('opacity', 0);
					$target.animate({ opacity: 1 }, 1000, function() {
						$target.restoreTransitions();
					});
					$target.removeAttr('data-lazy');
				});

			}
		});
	};

	// Execute it onload and onscroll
	fetchImages();
	oneTimeExecution(function() {
		$(window).scroll(function() {
			fetchImages();
		});
	});

});