/**
 * Automatic Slide Toggle System
 * --------------------------------------------------
 * Example of use :
 * data-slide-toggle="#myselector"
 * data-speed="3000" <!-- Optional parameter -->
 */

$(document).ready(function() {

	function scrollTo($target) {
		if($target.length) $("html, body").animate({ scrollTop: $target.offset().top }, 800);
	}

	// Hide all selectors
	$('[data-slide-toggle]').each(function() {
		var $target = $($(this).attr('data-slide-toggle'));
		if($target.length) $target.hide();
	});

	// Check if a link is already triggered
	var _triggered = false;

	// On click !
	$('[data-slide-toggle]').click(function(e) {
		e.preventDefault();

		// Already called :: Prevent weird slide toggle bugs
		// Note: stopPropagation & stopImmediatePropagation
		// are not effectives here.
		if(_triggered) return;
		else _triggered = true;

		// Remove class active to everyone and add it to this one
		$('[data-slide-toggle]').removeClass('active');
		$(this).addClass('active');

		// Target
		var $target = $($(this).attr('data-slide-toggle'));
		if(!$target.length) return;

		// Speed
		var speed = $(this).attr('data-speed');
		if(!speed) speed = 400;

		// Already open target ! Just close it !
		if($target.hasClass('js-deployed-slide-toggle')) {
			$target.slideToggle(speed, 'swing', function() {
				enableScroll();
				_triggered = false;
			}).removeClass('js-deployed-slide-toggle');
			$(this).removeClass('active');
			return;
		}

		// Disable Scroll while slideToggling !
		disableScroll();

		$target.slideToggle(speed, 'swing', function() {
			enableScroll();
			_triggered = false;
		}).addClass('js-deployed-slide-toggle');

	});

});