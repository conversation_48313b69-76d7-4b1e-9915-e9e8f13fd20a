/**
 * JS Follow Navigation System
 * ---------------------------
 */
$(document).ready(function() {

	function followNavigation() {
		$('[data-follow-navigation]').each(function() {

			// First run ?
			var _f = ($(this).hasClass('follow-instance')) ? true : false;

			// Transition
			var transition = ($(this).attr('data-transition')) ? $(this).attr('data-transition') : '.4s';

			// Get the current nav
			var $nav = $(this);

			// Hide the follow navigation onload
			var nav_height = $(this).outerHeight();
			if(!_f) {
				$nav.css('top', '-' + nav_height * 2 + 'px');
				$nav.addClass('follow-instance');
			}

			// Get the "Start follow after" element
			var $after = $($(this).attr('data-start-follow-after'));
			if(!$after.length) return console.log('Cannot enable follow navigation.');

			// Get the scroll position
			var scroll_position = $(window).scrollTop();

			// Get the position where the follow nav should appear
			var element_position = $after.position().top + $after.outerHeight();

			// Follow navigation deployed or not ?
			var deployed = ($nav.hasClass('follow-enabled')) ? true : false;
			
			// Did we reach this position ?
			if(scroll_position >= element_position) {
				if(!deployed) {
					// Show the navigation
					$nav.css('opacity', 1);
					$nav.show();
					// Add transitions
					$nav.css({
						WebkitTransition : 'all ' + transition,
					    MozTransition    : 'all ' + transition,
					    MsTransition     : 'all ' + transition,
					    OTransition      : 'all ' + transition,
					    transition       : 'all ' + transition
					});
					// Make the transition
					$nav.css('top', '0px');
					$nav.addClass('follow-enabled');
				}
			} else if(scroll_position <= (element_position - (element_position / 2))) {
				if(deployed) {
					// Remove Transitions
					$nav.css({
						WebkitTransition : 'none',
					    MozTransition    : 'none',
					    MsTransition     : 'none',
					    OTransition      : 'none',
					    transition       : 'none'
					});
					// Hide it
					$nav.css('opacity', 0);
					$nav.css('top', '-' + nav_height * 2 + 'px');
					$nav.removeClass('follow-enabled');
				}
			}

		});
	}

	followNavigation();
	$(window).scroll(function() { followNavigation(); });

});