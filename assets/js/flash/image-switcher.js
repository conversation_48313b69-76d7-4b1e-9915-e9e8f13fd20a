/**
 * Simple Image Switcher
 * ---------------------
 */
(function() {

	"use strict";

	// Current context settings/data
	var context = {
		attribute: 'data-image-switcher',
		target: {}
	};

	// Preload all the images first
	$('[' + context.attribute + ']').each(function() {
		return (new Image).src = $(this).attr(context.attribute);
	});

	// Change image on hover and restore original one on mouseout
	$('[' + context.attribute + ']').hover(function() {
		var image_src = $(this).attr(context.attribute);
		context.target = $($(this).attr(context.attribute + '-target'));
		if(!image_src || !context.target.length) return;
		context.target.original_image_src = (context.target.prop('tagName') == 'IMG') ? context.target.attr('src') : context.target.css('background-image');
		if(context.target.prop('tagName') == 'IMG') {
			context.target.attr('src', image_src);
		} else {
			context.target.css('background-image', 'url(' + image_src + ')');
		}
	}, function() {
		if(context.target.prop('tagName') == 'IMG') {
			context.target.attr('src', context.target.original_image_src);
		} else {
			context.target.css('background-image', context.target.original_image_src);
		}
	});

})();