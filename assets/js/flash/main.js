/**
 * Flash - Main JS File
 * --------------------
 */

// jQuery loaded & ready to use
var jqueryReady = true;

$(document).ready(function() {
	// If you need to add some custom javascript, you
	// can add it here below this comment.
    if(navigator.userAgent.match('MSIE 10.0;')) {
    	$('<div class="browser_message" style="position: fixed; width: 100%; height: 100%; z-index: 1000000; top: 0; background: #142435; text-align: center; font-size: 0.8em; padding-top: 50px;"><div class="wrapper"><img style="width:195px;margin-bottom: 40px;" src="/assets/images/design/logo-white.svg" alt="{site_name} Logo" /><h1 style="color: white;">Uh Oh! Your browser is too old to view our site.</h1><h2 style="color: white;">Here is what you need to do</h2><p style="color: white;">Please <a style="color:#D11368;" href="http://browsehappy.com/" target="_blank" rel="noopener">upgrade your browser</a> or install <a style="color:#D11368;" href="https://www.google.com/intl/en/chrome/browser/" target="_blank" rel="noopener">Google Chrome</a> to experience this website.</p></div></div><div class="overlay"></div>' ).appendTo('body');
	}
	if(!Modernizr.objectfit) {
        // $('.cover_image, .contain_image').each(function(){
        //     var $container = $(this);
        //     var img_url = $('img', $container).prop('src');
        //     $('img', $container).hide();
        //     $container.append('<div class="img"></div>');
        //     $('div', $container).css('background-image', 'url(\'' + img_url + '\')');
        // });
    } else {
        $('.cover_image, .contain_image').each(function(){
            $(this).html($(this).html()+'');
        });
    }	

    if(readCookie('status_lightbox')) $(".alert_status").hide();

    $(".alert_status__close").click(function(){
        $(".alert_status").hide();
        if(!readCookie('status_lightbox')) createCookie('status_lightbox', true, 0.0125);
    });
	
});

/**
 * detect IE
 * returns version of IE or false, if browser is not Internet Explorer
 */

(function detectIE() {
    var ua = window.navigator.userAgent;

    var msie = ua.indexOf('MSIE ');
    if (msie > 0) {
        // IE 10 or older => return version number
        var ieV = parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
        document.querySelector('body').className += ' IE';
    }

    var trident = ua.indexOf('Trident/');
    if (trident > 0) {
        // IE 11 => return version number
        var rv = ua.indexOf('rv:');
        var ieV = parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
        document.querySelector('body').className += ' IE11';
    }

    var edge = ua.indexOf('Edge/');
    if (edge > 0) {
       // IE 12 (aka Edge) => return version number
       var ieV = parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
        document.querySelector('body').className += ' EDGE';
    }

    // other browser
    return false;
})();