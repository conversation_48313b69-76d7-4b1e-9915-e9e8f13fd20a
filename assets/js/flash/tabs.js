/**
 * Simple JS Tabs System
 * ---------------------
 */

/**
 * Tabs initialization
 * 
 * @return void
 */
function initTabs() {
	$('[data-tab]').hide();
	$('[data-tab-target]').each(function() {
		if($(this).hasClass('active')) {
			$('[data-tab="' + $(this).attr('data-tab-target') + '"]').show();
		}
	});
}

/**
 * Update specific tabs
 * 
 * @return void
 */
$.fn.updateTabs = function() {
	var $tab = $('[data-tab="' + $(this).attr('data-tab-target') + '"]');

	// This part of the code is for supporting multiple tab groups on the page
	// It searches for the parent of the group of tabs and then toggles just the tabs of this group
	var $tab_group = $(this).parent();
	// Searches for the parent of the group, so it goes up one level in the DOM
	// until it finds more than one [data-tab-target] element in the children of the
	// current object
	while($('[data-tab-target]', $tab_group).length == 1 && !$tab_group.is('body')) {
		$tab_group = $tab_group.parent();
	}
	// Toggles just the tabs in the current group
	$('[data-tab-target]', $tab_group).each(function(index, el) {
		$('[data-tab="' + $(el).attr('data-tab-target') + '"]').hide();
	});
	$('[data-tab-target]', $tab_group).removeClass('active');
	$('[data-tab-target="' + $(this).attr('data-tab-target') + '"]', $tab_group).addClass('active');
	$tab.fadeIn();
};

$(document).ready(function() {

	initTabs();

	$(document).on('click', '[data-tab-target]', function(e) {
		e.preventDefault();
		$(this).parent().parent().find('[data-tab-target]').removeClass('active');
		$(this).addClass('active');
		$(this).updateTabs();
	});

	// Current tab on page load if url hash is not empty
	if(window.location.hash) {
	  $('[data-tab-target="' + window.location.hash.replace('#', '') + '"]').click();
	}
	
});