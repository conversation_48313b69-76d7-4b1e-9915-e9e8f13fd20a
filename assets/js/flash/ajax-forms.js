/**
 * Flash Forms Management
 * ---------------------------
 */

var flash_current_form = null;

// Form Submission
function flashForm(element) {
    var self = this;
    // If an element is passed, the form is initialised
    if(element) {
        self.$form = $(element);
        if(!self.$form.hasClass('js_init--flash-form')) {
            self.getSettings();
            self.initialise();
            self.fieldsEvents();
        }
    }
}

// Submissions handler url
flashForm.prototype.flash_submissions_handler = 'https://463z3kbl0f.execute-api.eu-west-1.amazonaws.com/default/flash-forms-handler-multipart-v2';

// Check if submissions are enabled
flashForm.prototype.isEnabled = function() {
    var self = this;

    return self.settings.enabled;
}

// For initialisation
flashForm.prototype.initialise = function(){
    var self = this;

    // Add init class
    self.$form.addClass('js_init--flash-form');

    // JUST AJAX SUBMISSIONS
    if(self.settings.ajax_submit) {
        
        self.$form.submit(function(event){
            flash_current_form = self;
            self.preSubmissionActions();
            event.preventDefault();

            // Check if the form is enabled
            if(!self.isEnabled()) {
                self.afterSubmissionActions('error');
                return;
            }
            
            // Submission promises
            var submissions = [];

            // If the form has the cnf file
            // the script will submit also to lambda
            if($('[name="cnf"]', self.$form).length) {
                submissions.push(self.flashHandler());
            }
    
            // If the form has the action attribute
            // the script will submit also the the custom post url
            if($(self.$form).is('[action]')) {
                submissions.push(self.otherHandler());
            }
            
            // Waiting for the requests to be completed
            $.when.apply($, submissions).then(function ( data ) {
                self.afterSubmissionActions('success');
            }, function(error){
                console.log(error)
                self.afterSubmissionActions('error');
            });
        });
    } else if(self.$form.is('[action]') && self.$form.attr('action') != '') {
        // NOT JUST AJAX SUBMISSIONS   
        // Add this class to prevent automatic posting to the action URL
        self.$form.addClass('js-prevent-submit');
        // Handling submission
        self.$form.submit(function(event){
            flash_current_form = self;
            if(self.$form.hasClass('js-prevent-submit')) {
                self.preSubmissionActions();
                event.preventDefault();
                // Submission promises
                var submissions = [];

                // If the form has the cnf file
                // the script will submit also to lambda
                if($('[name="cnf"]', self.$form).length) {
                    submissions.push(self.flashHandler());
                }

                // Waiting for the requests to be completed
                $.when.apply($, submissions).then(function ( data ) {
                    self.$form.removeClass('js-prevent-submit');
                    self.$form.submit();
                }, function(error){
                    self.afterSubmissionActions('error');
                });
            }
        });
    }
}

// Events bound to fields
flashForm.prototype.fieldsEvents = function(){
    var self = this;

    // Filter Options Based on another field
    if($('option[data-filter]', self.$form).length) {
        $('option[data-filter]', self.$form).each(function(index, el) {
            var $field = $(el);
            var filters = $field.attr('data-filter').split('|');

            $('[name="' + filters[0] + '"]', self.$form).on('change', function(){
                if($('[name="' + filters[0] + '"]', self.$form) && ($('[name="' + filters[0] + '"]', self.$form).val() == filters[1] || $('[name="' + filters[0] + '"]', self.$form).val() == '' || !$('[name="' + filters[0] + '"]', self.$form).val())) {
                    $field.show();
                } else {
                    $field.hide();
                }  
            }).change();
        });
    }

    // Change settings based on a field
    if($('[name="cnf"][data-conditional]', self.$form).length) {
        var listened_fields = [];
        $('[name="cnf"][data-conditional]', self.$form).each(function(){
            var $option = $(this);
            // Checking all the variants of the cnf
            if(listened_fields.indexOf($option.attr('data-conditional')) == -1) {
                // Name of the field to watch for
                var field_name = $option.attr('data-conditional');
                var $field = $('[name="' + field_name + '"]', self.$form);

                $field.on('change', function(){
                    $('[name="cnf"]').removeAttr('checked');
                    $('[name="cnf"][data-conditional-value="' + $field.val() + '"]', self.$form).attr('checked', 'checked');
                }).change();

                listened_fields.push($option.attr('data-conditional'));
            }
        });
    }
}

// Get form settings
flashForm.prototype.getSettings = function(){
    var self = this;
    
    // Initialise settings
    self.settings = {
        ajax_submit : true,
        enabled: true,
        prevent_thank_you_message: false
    };

    // Get the settings if they are there
    if(self.$form.is('[data-flash-form]')) {
        try {
            Object.assign(self.settings, JSON.parse('{' + self.$form.attr('data-flash-form') + '}'));
        } catch(error) {
            
        }
    }
}

// Pre submission actions
flashForm.prototype.preSubmissionActions = function(){
    var self = this;
    
    self.$form.css('opacity', '0.6');

    // Custom Action
    if(self.settings.pre_submission_callback) {
        self.settings.enabled = eval(self.settings.pre_submission_callback);
    }
}

// After ajax submission events
flashForm.prototype.afterSubmissionActions = function(event){
    var self = this;

    switch (event) {
        case 'success':
            // Custom callback
            if(self.settings.success_callback) {
                eval(self.settings.success_callback);
            } else if(self.settings.thank_you_url) {
                // Redirect to Thank you Page
                window.location.href = self.settings.thank_you_url;
            }
            if(!self.settings.prevent_thank_you_message && !self.settings.thank_you_url) {               
                // Show Thank You message
                self.$form.css('opacity', '1');
                $('.form__container', self.$form).hide();
                $('.form__error', self.$form).hide();
                $('.form__thank_you_message', self.$form).fadeIn();
            } 
            break;
        
        case 'error':
            self.$form.css('opacity', '1');
            $('.form__error', self.$form).fadeIn();
            break;
    }
}

// Submission on flash lambda handler
flashForm.prototype.flashHandler = function(){
    var self = this;

    var deferred = $.Deferred();
    
    var handler_settings = {
        url: self.flash_submissions_handler,
        type: 'POST',
        data: ''
    }

    if(self.$form.attr('enctype') == 'multipart/form-data') {
        handler_settings.data = new FormData(self.$form[0]);
        handler_settings.cache = false;
        handler_settings.contentType = false;
        handler_settings.processData = false;
    } else {
        handler_settings.data = self.$form.serialize();
    }

    $.ajax(handler_settings)
    .done(function(data) {
        deferred.resolve('yay');
    })  
    .fail(function() {
        deferred.reject('no');
    });

    return deferred.promise();
}

// Submission on custom handler
flashForm.prototype.otherHandler = function(){
    var self = this;
    var deferred = $.Deferred();

    var handler_settings = {
        url: self.$form.attr('action'),
        type: "POST",
        data: ''
    };

    if(self.$form.attr('enctype') == 'multipart/form-data') {
        handler_settings.data = new FormData(self.$form[0]);
        handler_settings.cache = false;
        handler_settings.contentType = false;
        handler_settings.processData = false;
    } else {
        handler_settings.data = self.$form.serialize();
    }

    // Jsonp option in case of CORS not working with normal requests
    // if(self.settings.enable_jsonp) {
        handler_settings.dataType = "jsonp";
        handler_settings.crossDomain = true;
    // }

    $.ajax(handler_settings)
    .done(function(data) {
        deferred.resolve('yay');
    })  
    .fail(function() {
        deferred.reject('no');
    });

    return deferred.promise();
}

// Callback for jsonp requests
function formCallback(data) {
    if(data.submission_status) {
        self.afterSubmissionActions('success');
    } else {
        self.afterSubmissionActions('error');
    }
}

// Init function
function initFlashForms() {
    $('form:not(.js_form--no_flash):not(.js_form--just_in_lightbox), .js-lightbox form.js_form--just_in_lightbox').each(function(index, form){
        var flash_form = new flashForm(form);
    });
}

// Init
$(document).ready(function(){
    initFlashForms();
});