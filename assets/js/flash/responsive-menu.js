/**
 * Responsive Menu System
 * ----------------------
 */

// Active Menu ?
var active_menu = false;

// Responsive Menu
var $responsive_menu = $('[data-responsive-menu]');

// Open Menu Function
function openResponsiveMenu() {
	// Move Responsive Menu, but keep the last position in memory.
	$responsive_menu.prependTo('body').fadeIn();

	// No scroll allowed here
	$('body').css({
		'overflow': 'hidden',
		'position': 'fixed',
		'height': $(window).height()
	});

	// Active Menu ?
	setTimeout(function() {
		active_menu = ($responsive_menu.is(':visible')) ? true : false;
	}, 100);
}

// Close Menu Function
function closeResponsiveMenu() {
	if(!active_menu) return;
	$('body').css({
		'overflow': 'auto',
		'position': 'initial',
		'height': 'auto'
	});
	$responsive_menu.fadeOut();
	active_menu = false;
}

// Open the menu !
$('[data-responsive-menu-trigger]').click(function(e) {
	e.preventDefault();
	openResponsiveMenu();
});

// Close the menu !
$('div.responsive_menu').click(function(e){
	if($(e.target).hasClass('responsive_menu')) {
		closeResponsiveMenu();
	}
});
$('div.responsive_menu a.close-icon').click(function(e){
	closeResponsiveMenu();
});