((e,t)=>{"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(e.document)return t(e);throw new Error("jQuery requires a window with a document")}:t(e)})("undefined"!=typeof window?window:this,function(k,H){function y(e){return"function"==typeof e&&"number"!=typeof e.nodeType}function g(e){return null!=e&&e===e.window}var e=[],x=k.document,I=Object.getPrototypeOf,a=e.slice,M=e.concat,q=e.push,z=e.indexOf,W={},R=W.toString,F=W.hasOwnProperty,B=F.toString,U=B.call(Object),m={},X={type:!0,src:!0,nonce:!0,noModule:!0};function Y(e,t,n){var i,o,r=(n=n||x).createElement("script");if(r.text=e,t)for(i in X)(o=t[i]||t.getAttribute&&t.getAttribute(i))&&r.setAttribute(i,o);n.head.appendChild(r).parentNode.removeChild(r)}function h(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?W[R.call(e)]||"object":typeof e}var t="3.4.1",$=function(e,t){return new $.fn.init(e,t)},G=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;function V(e){var t=!!e&&"length"in e&&e.length,n=h(e);return!y(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}$.fn=$.prototype={jquery:t,constructor:$,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){e=$.merge(this.constructor(),e);return e.prevObject=this,e},each:function(e){return $.each(this,e)},map:function(n){return this.pushStack($.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor()},push:q,sort:e.sort,splice:e.splice},$.extend=$.fn.extend=function(){var e,t,n,i,o,r=arguments[0]||{},s=1,a=arguments.length,l=!1;for("boolean"==typeof r&&(l=r,r=arguments[s]||{},s++),"object"==typeof r||y(r)||(r={}),s===a&&(r=this,s--);s<a;s++)if(null!=(e=arguments[s]))for(t in e)n=e[t],"__proto__"!==t&&r!==n&&(l&&n&&($.isPlainObject(n)||(i=Array.isArray(n)))?(o=r[t],o=i&&!Array.isArray(o)?[]:i||$.isPlainObject(o)?o:{},i=!1,r[t]=$.extend(l,o,n)):void 0!==n&&(r[t]=n));return r},$.extend({expando:"jQuery"+(t+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){return!(!e||"[object Object]"!==R.call(e)||(e=I(e))&&("function"!=typeof(e=F.call(e,"constructor")&&e.constructor)||B.call(e)!==U))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t){Y(e,{nonce:t&&t.nonce})},each:function(e,t){var n,i=0;if(V(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},trim:function(e){return null==e?"":(e+"").replace(G,"")},makeArray:function(e,t){t=t||[];return null!=e&&(V(Object(e))?$.merge(t,"string"==typeof e?[e]:e):q.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:z.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,o=e.length;i<n;i++)e[o++]=t[i];return e.length=o,e},grep:function(e,t,n){for(var i=[],o=0,r=e.length,s=!n;o<r;o++)!t(e[o],o)!=s&&i.push(e[o]);return i},map:function(e,t,n){var i,o,r=0,s=[];if(V(e))for(i=e.length;r<i;r++)null!=(o=t(e[r],r,n))&&s.push(o);else for(r in e)null!=(o=t(e[r],r,n))&&s.push(o);return M.apply([],s)},guid:1,support:m}),"function"==typeof Symbol&&($.fn[Symbol.iterator]=e[Symbol.iterator]),$.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){W["[object "+t+"]"]=t.toLowerCase()});function i(e,t,n){for(var i=[],o=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(o&&$(e).is(n))break;i.push(e)}return i}function Q(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var t=(H=>{function u(e,t,n){var i="0x"+t-65536;return i!=i||n?t:i<0?String.fromCharCode(65536+i):String.fromCharCode(i>>10|55296,1023&i|56320)}function I(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}function M(){x()}var e,p,w,r,q,f,z,W,k,l,c,x,$,n,T,h,i,o,g,C="sizzle"+ +new Date,m=H.document,_=0,R=0,F=D(),B=D(),U=D(),v=D(),X=function(e,t){return e===t&&(c=!0),0},Y={}.hasOwnProperty,t=[],G=t.pop,V=t.push,S=t.push,Q=t.slice,y=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},K="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",s="[\\x20\\t\\r\\n\\f]",a="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",J="\\["+s+"*("+a+")(?:"+s+"*([*^$|!~]?=)"+s+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+a+"))|)"+s+"*\\]",Z=":("+a+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+J+")*)|.*)\\)|)",ee=new RegExp(s+"+","g"),b=new RegExp("^"+s+"+|((?:^|[^\\\\])(?:\\\\.)*)"+s+"+$","g"),te=new RegExp("^"+s+"*,"+s+"*"),ne=new RegExp("^"+s+"*([>+~]|"+s+")"+s+"*"),ie=new RegExp(s+"|>"),oe=new RegExp(Z),re=new RegExp("^"+a+"$"),j={ID:new RegExp("^#("+a+")"),CLASS:new RegExp("^\\.("+a+")"),TAG:new RegExp("^("+a+"|[*])"),ATTR:new RegExp("^"+J),PSEUDO:new RegExp("^"+Z),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+s+"*(even|odd|(([+-]|)(\\d*)n|)"+s+"*(?:([+-]|)"+s+"*(\\d+)|))"+s+"*\\)|)","i"),bool:new RegExp("^(?:"+K+")$","i"),needsContext:new RegExp("^"+s+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+s+"*((?:-\\d)?\\d*)"+s+"*\\)|)(?=[^-]|$)","i")},se=/HTML$/i,ae=/^(?:input|select|textarea|button)$/i,le=/^h\d$/i,d=/^[^{]+\{\s*\[native \w/,ce=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,de=/[+~]/,E=new RegExp("\\\\([\\da-f]{1,6}"+s+"?|("+s+")|.)","ig"),ue=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,pe=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{S.apply(t=Q.call(m.childNodes),m.childNodes),t[m.childNodes.length].nodeType}catch(e){S={apply:t.length?function(e,t){V.apply(e,Q.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function A(t,e,n,i){var o,r,s,a,l,c,d=e&&e.ownerDocument,u=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==u&&9!==u&&11!==u)return n;if(!i&&((e?e.ownerDocument||e:m)!==$&&x(e),e=e||$,T)){if(11!==u&&(a=ce.exec(t)))if(o=a[1]){if(9===u){if(!(c=e.getElementById(o)))return n;if(c.id===o)return n.push(c),n}else if(d&&(c=d.getElementById(o))&&g(e,c)&&c.id===o)return n.push(c),n}else{if(a[2])return S.apply(n,e.getElementsByTagName(t)),n;if((o=a[3])&&p.getElementsByClassName&&e.getElementsByClassName)return S.apply(n,e.getElementsByClassName(o)),n}if(p.qsa&&!v[t+" "]&&(!h||!h.test(t))&&(1!==u||"object"!==e.nodeName.toLowerCase())){if(c=t,d=e,1===u&&ie.test(t)){for((s=e.getAttribute("id"))?s=s.replace(ue,I):e.setAttribute("id",s=C),r=(l=f(t)).length;r--;)l[r]="#"+s+" "+P(l[r]);c=l.join(","),d=de.test(t)&&me(e.parentNode)||e}try{return S.apply(n,d.querySelectorAll(c)),n}catch(e){v(t,!0)}finally{s===C&&e.removeAttribute("id")}}}return W(t.replace(b,"$1"),e,n,i)}function D(){var n=[];function i(e,t){return n.push(e+" ")>w.cacheLength&&delete i[n.shift()],i[e+" "]=t}return i}function O(e){return e[C]=!0,e}function L(e){var t=$.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function fe(e,t){for(var n=e.split("|"),i=n.length;i--;)w.attrHandle[n[i]]=t}function he(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function ge(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&pe(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function N(s){return O(function(r){return r=+r,O(function(e,t){for(var n,i=s([],e.length,r),o=i.length;o--;)e[n=i[o]]&&(e[n]=!(t[n]=e[n]))})})}function me(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in p=A.support={},q=A.isXML=function(e){var t=e.namespaceURI,e=(e.ownerDocument||e).documentElement;return!se.test(t||e&&e.nodeName||"HTML")},x=A.setDocument=function(e){var e=e?e.ownerDocument||e:m;return e!==$&&9===e.nodeType&&e.documentElement&&(n=($=e).documentElement,T=!q($),m!==$&&(e=$.defaultView)&&e.top!==e&&(e.addEventListener?e.addEventListener("unload",M,!1):e.attachEvent&&e.attachEvent("onunload",M)),p.attributes=L(function(e){return e.className="i",!e.getAttribute("className")}),p.getElementsByTagName=L(function(e){return e.appendChild($.createComment("")),!e.getElementsByTagName("*").length}),p.getElementsByClassName=d.test($.getElementsByClassName),p.getById=L(function(e){return n.appendChild(e).id=C,!$.getElementsByName||!$.getElementsByName(C).length}),p.getById?(w.filter.ID=function(e){var t=e.replace(E,u);return function(e){return e.getAttribute("id")===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&T)return(t=t.getElementById(e))?[t]:[]}):(w.filter.ID=function(e){var t=e.replace(E,u);return function(e){e=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return e&&e.value===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n,i,o,r=t.getElementById(e);if(r){if((n=r.getAttributeNode("id"))&&n.value===e)return[r];for(o=t.getElementsByName(e),i=0;r=o[i++];)if((n=r.getAttributeNode("id"))&&n.value===e)return[r]}return[]}}),w.find.TAG=p.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):p.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],o=0,r=t.getElementsByTagName(e);if("*"!==e)return r;for(;n=r[o++];)1===n.nodeType&&i.push(n);return i},w.find.CLASS=p.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&T)return t.getElementsByClassName(e)},i=[],h=[],(p.qsa=d.test($.querySelectorAll))&&(L(function(e){n.appendChild(e).innerHTML="<a id='"+C+"'></a><select id='"+C+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&h.push("[*^$]="+s+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||h.push("\\["+s+"*(?:value|"+K+")"),e.querySelectorAll("[id~="+C+"-]").length||h.push("~="),e.querySelectorAll(":checked").length||h.push(":checked"),e.querySelectorAll("a#"+C+"+*").length||h.push(".#.+[+~]")}),L(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=$.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&h.push("name"+s+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&h.push(":enabled",":disabled"),n.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&h.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),h.push(",.*:")})),(p.matchesSelector=d.test(o=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.oMatchesSelector||n.msMatchesSelector))&&L(function(e){p.disconnectedMatch=o.call(e,"*"),o.call(e,"[s!='']:x"),i.push("!=",Z)}),h=h.length&&new RegExp(h.join("|")),i=i.length&&new RegExp(i.join("|")),e=d.test(n.compareDocumentPosition),g=e||d.test(n.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},X=e?function(e,t){var n;return e===t?(c=!0,0):(n=!e.compareDocumentPosition-!t.compareDocumentPosition)||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!p.sortDetached&&t.compareDocumentPosition(e)===n?e===$||e.ownerDocument===m&&g(m,e)?-1:t===$||t.ownerDocument===m&&g(m,t)?1:l?y(l,e)-y(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,i=0,o=e.parentNode,r=t.parentNode,s=[e],a=[t];if(!o||!r)return e===$?-1:t===$?1:o?-1:r?1:l?y(l,e)-y(l,t):0;if(o===r)return he(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?he(s[i],a[i]):s[i]===m?-1:a[i]===m?1:0}),$},A.matches=function(e,t){return A(e,null,null,t)},A.matchesSelector=function(e,t){if((e.ownerDocument||e)!==$&&x(e),p.matchesSelector&&T&&!v[t+" "]&&(!i||!i.test(t))&&(!h||!h.test(t)))try{var n=o.call(e,t);if(n||p.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){v(t,!0)}return 0<A(t,$,null,[e]).length},A.contains=function(e,t){return(e.ownerDocument||e)!==$&&x(e),g(e,t)},A.attr=function(e,t){(e.ownerDocument||e)!==$&&x(e);var n=w.attrHandle[t.toLowerCase()],n=n&&Y.call(w.attrHandle,t.toLowerCase())?n(e,t,!T):void 0;return void 0!==n?n:p.attributes||!T?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},A.escape=function(e){return(e+"").replace(ue,I)},A.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},A.uniqueSort=function(e){var t,n=[],i=0,o=0;if(c=!p.detectDuplicates,l=!p.sortStable&&e.slice(0),e.sort(X),c){for(;t=e[o++];)t===e[o]&&(i=n.push(o));for(;i--;)e.splice(n[i],1)}return l=null,e},r=A.getText=function(e){var t,n="",i=0,o=e.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=r(e)}else if(3===o||4===o)return e.nodeValue}else for(;t=e[i++];)n+=r(t);return n},(w=A.selectors={cacheLength:50,createPseudo:O,match:j,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(E,u),e[3]=(e[3]||e[4]||e[5]||"").replace(E,u),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||A.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&A.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return j.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&oe.test(n)&&(t=(t=f(n,!0))&&n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(E,u).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=F[e+" "];return t||(t=new RegExp("(^|"+s+")"+e+"("+s+"|$)"))&&F(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(e){e=A.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===i:"!="===n?e!==i:"^="===n?i&&0===e.indexOf(i):"*="===n?i&&-1<e.indexOf(i):"$="===n?i&&e.slice(-i.length)===i:"~="===n?-1<(" "+e.replace(ee," ")+" ").indexOf(i):"|="===n&&(e===i||e.slice(0,i.length+1)===i+"-"))}},CHILD:function(h,e,t,g,m){var v="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,o,r,s,a,l,c=v!=y?"nextSibling":"previousSibling",d=e.parentNode,u=b&&e.nodeName.toLowerCase(),p=!n&&!b,f=!1;if(d){if(v){for(;c;){for(s=e;s=s[c];)if(b?s.nodeName.toLowerCase()===u:1===s.nodeType)return!1;l=c="only"===h&&!l&&"nextSibling"}return!0}if(l=[y?d.firstChild:d.lastChild],y&&p){for(f=(a=(i=(o=(r=(s=d)[C]||(s[C]={}))[s.uniqueID]||(r[s.uniqueID]={}))[h]||[])[0]===_&&i[1])&&i[2],s=a&&d.childNodes[a];s=++a&&s&&s[c]||(f=a=0,l.pop());)if(1===s.nodeType&&++f&&s===e){o[h]=[_,a,f];break}}else if(!1===(f=p?a=(i=(o=(r=(s=e)[C]||(s[C]={}))[s.uniqueID]||(r[s.uniqueID]={}))[h]||[])[0]===_&&i[1]:f))for(;(s=++a&&s&&s[c]||(f=a=0,l.pop()))&&((b?s.nodeName.toLowerCase()!==u:1!==s.nodeType)||!++f||(p&&((o=(r=s[C]||(s[C]={}))[s.uniqueID]||(r[s.uniqueID]={}))[h]=[_,f]),s!==e)););return(f-=m)===g||f%g==0&&0<=f/g}}},PSEUDO:function(e,r){var t,s=w.pseudos[e]||w.setFilters[e.toLowerCase()]||A.error("unsupported pseudo: "+e);return s[C]?s(r):1<s.length?(t=[e,e,"",r],w.setFilters.hasOwnProperty(e.toLowerCase())?O(function(e,t){for(var n,i=s(e,r),o=i.length;o--;)e[n=y(e,i[o])]=!(t[n]=i[o])}):function(e){return s(e,0,t)}):s}},pseudos:{not:O(function(e){var i=[],o=[],a=z(e.replace(b,"$1"));return a[C]?O(function(e,t,n,i){for(var o,r=a(e,null,i,[]),s=e.length;s--;)(o=r[s])&&(e[s]=!(t[s]=o))}):function(e,t,n){return i[0]=e,a(i,null,n,o),i[0]=null,!o.pop()}}),has:O(function(t){return function(e){return 0<A(t,e).length}}),contains:O(function(t){return t=t.replace(E,u),function(e){return-1<(e.textContent||r(e)).indexOf(t)}}),lang:O(function(n){return re.test(n||"")||A.error("unsupported lang: "+n),n=n.replace(E,u).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=H.location&&H.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===n},focus:function(e){return e===$.activeElement&&(!$.hasFocus||$.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ge(!1),disabled:ge(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},header:function(e){return le.test(e.nodeName)},input:function(e){return ae.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:N(function(){return[0]}),last:N(function(e,t){return[t-1]}),eq:N(function(e,t,n){return[n<0?n+t:n]}),even:N(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:N(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:N(function(e,t,n){for(var i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:N(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[e]=(t=>function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t})(e);for(e in{submit:!0,reset:!0})w.pseudos[e]=(n=>function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n})(e);function ve(){}function P(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function ye(s,e,t){var a=e.dir,l=e.next,c=l||a,d=t&&"parentNode"===c,u=R++;return e.first?function(e,t,n){for(;e=e[a];)if(1===e.nodeType||d)return s(e,t,n);return!1}:function(e,t,n){var i,o,r=[_,u];if(n){for(;e=e[a];)if((1===e.nodeType||d)&&s(e,t,n))return!0}else for(;e=e[a];)if(1===e.nodeType||d)if(o=(o=e[C]||(e[C]={}))[e.uniqueID]||(o[e.uniqueID]={}),l&&l===e.nodeName.toLowerCase())e=e[a]||e;else{if((i=o[c])&&i[0]===_&&i[1]===u)return r[2]=i[2];if((o[c]=r)[2]=s(e,t,n))return!0}return!1}}function be(o){return 1<o.length?function(e,t,n){for(var i=o.length;i--;)if(!o[i](e,t,n))return!1;return!0}:o[0]}function we(e,t,n,i,o){for(var r,s=[],a=0,l=e.length,c=null!=t;a<l;a++)!(r=e[a])||n&&!n(r,i,o)||(s.push(r),c&&t.push(a));return s}function ke(f,h,g,m,v,e){return m&&!m[C]&&(m=ke(m)),v&&!v[C]&&(v=ke(v,e)),O(function(e,t,n,i){var o,r,s,a=[],l=[],c=t.length,d=e||((e,t,n)=>{for(var i=0,o=t.length;i<o;i++)A(e,t[i],n);return n})(h||"*",n.nodeType?[n]:n,[]),u=!f||!e&&h?d:we(d,a,f,n,i),p=g?v||(e?f:c||m)?[]:t:u;if(g&&g(u,p,n,i),m)for(o=we(p,l),m(o,[],n,i),r=o.length;r--;)(s=o[r])&&(p[l[r]]=!(u[l[r]]=s));if(e){if(v||f){if(v){for(o=[],r=p.length;r--;)(s=p[r])&&o.push(u[r]=s);v(null,p=[],o,i)}for(r=p.length;r--;)(s=p[r])&&-1<(o=v?y(e,s):a[r])&&(e[o]=!(t[o]=s))}}else p=we(p===t?p.splice(c,p.length):p),v?v(null,t,p,i):S.apply(t,p)})}function xe(m,v){function e(e,t,n,i,o){var r,s,a,l=0,c="0",d=e&&[],u=[],p=k,f=e||b&&w.find.TAG("*",o),h=_+=null==p?1:Math.random()||.1,g=f.length;for(o&&(k=t===$||t||o);c!==g&&null!=(r=f[c]);c++){if(b&&r){for(s=0,t||r.ownerDocument===$||(x(r),n=!T);a=m[s++];)if(a(r,t||$,n)){i.push(r);break}o&&(_=h)}y&&((r=!a&&r)&&l--,e)&&d.push(r)}if(l+=c,y&&c!==l){for(s=0;a=v[s++];)a(d,u,t,n);if(e){if(0<l)for(;c--;)d[c]||u[c]||(u[c]=G.call(i));u=we(u)}S.apply(i,u),o&&!e&&0<u.length&&1<l+v.length&&A.uniqueSort(i)}return o&&(_=h,k=p),d}var y=0<v.length,b=0<m.length;return y?O(e):e}return ve.prototype=w.filters=w.pseudos,w.setFilters=new ve,f=A.tokenize=function(e,t){var n,i,o,r,s,a,l,c=B[e+" "];if(c)return t?0:c.slice(0);for(s=e,a=[],l=w.preFilter;s;){for(r in n&&!(i=te.exec(s))||(i&&(s=s.slice(i[0].length)||s),a.push(o=[])),n=!1,(i=ne.exec(s))&&(n=i.shift(),o.push({value:n,type:i[0].replace(b," ")}),s=s.slice(n.length)),w.filter)!(i=j[r].exec(s))||l[r]&&!(i=l[r](i))||(n=i.shift(),o.push({value:n,type:r,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?A.error(e):B(e,a).slice(0)},z=A.compile=function(e,t){var n,i=[],o=[],r=U[e+" "];if(!r){for(n=(t=t||f(e)).length;n--;)((r=function e(t){for(var i,n,o,r=t.length,s=w.relative[t[0].type],a=s||w.relative[" "],l=s?1:0,c=ye(function(e){return e===i},a,!0),d=ye(function(e){return-1<y(i,e)},a,!0),u=[function(e,t,n){return e=!s&&(n||t!==k)||((i=t).nodeType?c:d)(e,t,n),i=null,e}];l<r;l++)if(n=w.relative[t[l].type])u=[ye(be(u),n)];else{if((n=w.filter[t[l].type].apply(null,t[l].matches))[C]){for(o=++l;o<r&&!w.relative[t[o].type];o++);return ke(1<l&&be(u),1<l&&P(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(b,"$1"),n,l<o&&e(t.slice(l,o)),o<r&&e(t=t.slice(o)),o<r&&P(t))}u.push(n)}return be(u)}(t[n]))[C]?i:o).push(r);(r=U(e,xe(o,i))).selector=e}return r},W=A.select=function(e,t,n,i){var o,r,s,a,l,c="function"==typeof e&&e,d=!i&&f(e=c.selector||e);if(n=n||[],1===d.length){if(2<(r=d[0]=d[0].slice(0)).length&&"ID"===(s=r[0]).type&&9===t.nodeType&&T&&w.relative[r[1].type]){if(!(t=(w.find.ID(s.matches[0].replace(E,u),t)||[])[0]))return n;c&&(t=t.parentNode),e=e.slice(r.shift().value.length)}for(o=j.needsContext.test(e)?0:r.length;o--&&(s=r[o],!w.relative[a=s.type]);)if((l=w.find[a])&&(i=l(s.matches[0].replace(E,u),de.test(r[0].type)&&me(t.parentNode)||t))){if(r.splice(o,1),e=i.length&&P(r))break;return S.apply(n,i),n}}return(c||z(e,d))(i,t,!T,n,!t||de.test(e)&&me(t.parentNode)||t),n},p.sortStable=C.split("").sort(X).join("")===C,p.detectDuplicates=!!c,x(),p.sortDetached=L(function(e){return 1&e.compareDocumentPosition($.createElement("fieldset"))}),L(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||fe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),p.attributes&&L(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||fe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),L(function(e){return null==e.getAttribute("disabled")})||fe(K,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}),A})(k),K=($.find=t,$.expr=t.selectors,$.expr[":"]=$.expr.pseudos,$.uniqueSort=$.unique=t.uniqueSort,$.text=t.getText,$.isXMLDoc=t.isXML,$.contains=t.contains,$.escapeSelector=t.escape,$.expr.match.needsContext);function l(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var J=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Z(e,n,i){return y(n)?$.grep(e,function(e,t){return!!n.call(e,t,e)!==i}):n.nodeType?$.grep(e,function(e){return e===n!==i}):"string"!=typeof n?$.grep(e,function(e){return-1<z.call(n,e)!==i}):$.filter(n,e,i)}$.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?$.find.matchesSelector(i,e)?[i]:[]:$.find.matches(e,$.grep(t,function(e){return 1===e.nodeType}))},$.fn.extend({find:function(e){var t,n,i=this.length,o=this;if("string"!=typeof e)return this.pushStack($(e).filter(function(){for(t=0;t<i;t++)if($.contains(o[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)$.find(e,o[t],n);return 1<i?$.uniqueSort(n):n},filter:function(e){return this.pushStack(Z(this,e||[],!1))},not:function(e){return this.pushStack(Z(this,e||[],!0))},is:function(e){return!!Z(this,"string"==typeof e&&K.test(e)?$(e):e||[],!1).length}});var ee,te=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,ne=(($.fn.init=function(e,t,n){if(e){if(n=n||ee,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):y(e)?void 0!==n.ready?n.ready(e):e($):$.makeArray(e,this);if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:te.exec(e))||!i[1]&&t)return(!t||t.jquery?t||n:this.constructor(t)).find(e);if(i[1]){if(t=t instanceof $?t[0]:t,$.merge(this,$.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:x,!0)),J.test(i[1])&&$.isPlainObject(t))for(var i in t)y(this[i])?this[i](t[i]):this.attr(i,t[i])}else(n=x.getElementById(i[2]))&&(this[0]=n,this.length=1)}return this}).prototype=$.fn,ee=$(x),/^(?:parents|prev(?:Until|All))/),ie={children:!0,contents:!0,next:!0,prev:!0};function oe(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}$.fn.extend({has:function(e){var t=$(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if($.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,o=this.length,r=[],s="string"!=typeof e&&$(e);if(!K.test(e))for(;i<o;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&$.find.matchesSelector(n,e))){r.push(n);break}return this.pushStack(1<r.length?$.uniqueSort(r):r)},index:function(e){return e?"string"==typeof e?z.call($(e),this[0]):z.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack($.uniqueSort($.merge(this.get(),$(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),$.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return i(e,"parentNode")},parentsUntil:function(e,t,n){return i(e,"parentNode",n)},next:function(e){return oe(e,"nextSibling")},prev:function(e){return oe(e,"previousSibling")},nextAll:function(e){return i(e,"nextSibling")},prevAll:function(e){return i(e,"previousSibling")},nextUntil:function(e,t,n){return i(e,"nextSibling",n)},prevUntil:function(e,t,n){return i(e,"previousSibling",n)},siblings:function(e){return Q((e.parentNode||{}).firstChild,e)},children:function(e){return Q(e.firstChild)},contents:function(e){return void 0!==e.contentDocument?e.contentDocument:(l(e,"template")&&(e=e.content||e),$.merge([],e.childNodes))}},function(i,o){$.fn[i]=function(e,t){var n=$.map(this,o,e);return(t="Until"!==i.slice(-5)?e:t)&&"string"==typeof t&&(n=$.filter(t,n)),1<this.length&&(ie[i]||$.uniqueSort(n),ne.test(i))&&n.reverse(),this.pushStack(n)}});var T=/[^\x20\t\r\n\f]+/g;function d(e){return e}function re(e){throw e}function se(e,t,n,i){var o;try{e&&y(o=e.promise)?o.call(e).done(t).fail(n):e&&y(o=e.then)?o.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}$.Callbacks=function(i){var e,n;i="string"==typeof i?(e=i,n={},$.each(e.match(T)||[],function(e,t){n[t]=!0}),n):$.extend({},i);function o(){for(a=a||i.once,s=r=!0;c.length;d=-1)for(t=c.shift();++d<l.length;)!1===l[d].apply(t[0],t[1])&&i.stopOnFalse&&(d=l.length,t=!1);i.memory||(t=!1),r=!1,a&&(l=t?[]:"")}var r,t,s,a,l=[],c=[],d=-1,u={add:function(){return l&&(t&&!r&&(d=l.length-1,c.push(t)),function n(e){$.each(e,function(e,t){y(t)?i.unique&&u.has(t)||l.push(t):t&&t.length&&"string"!==h(t)&&n(t)})}(arguments),t)&&!r&&o(),this},remove:function(){return $.each(arguments,function(e,t){for(var n;-1<(n=$.inArray(t,l,n));)l.splice(n,1),n<=d&&d--}),this},has:function(e){return e?-1<$.inArray(e,l):0<l.length},empty:function(){return l=l&&[],this},disable:function(){return a=c=[],l=t="",this},disabled:function(){return!l},lock:function(){return a=c=[],t||r||(l=t=""),this},locked:function(){return!!a},fireWith:function(e,t){return a||(t=[e,(t=t||[]).slice?t.slice():t],c.push(t),r)||o(),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!s}};return u},$.extend({Deferred:function(e){var r=[["notify","progress",$.Callbacks("memory"),$.Callbacks("memory"),2],["resolve","done",$.Callbacks("once memory"),$.Callbacks("once memory"),0,"resolved"],["reject","fail",$.Callbacks("once memory"),$.Callbacks("once memory"),1,"rejected"]],o="pending",s={state:function(){return o},always:function(){return a.done(arguments).fail(arguments),this},catch:function(e){return s.then(null,e)},pipe:function(){var o=arguments;return $.Deferred(function(i){$.each(r,function(e,t){var n=y(o[t[4]])&&o[t[4]];a[t[1]](function(){var e=n&&n.apply(this,arguments);e&&y(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this,n?[e]:arguments)})}),o=null}).promise()},then:function(t,n,i){var l=0;function c(o,r,s,a){return function(){function e(){var e,t;if(!(o<l)){if((e=s.apply(n,i))===r.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,y(t)?a?t.call(e,c(l,r,d,a),c(l,r,re,a)):(l++,t.call(e,c(l,r,d,a),c(l,r,re,a),c(l,r,d,r.notifyWith))):(s!==d&&(n=void 0,i=[e]),(a||r.resolveWith)(n,i))}}var n=this,i=arguments,t=a?e:function(){try{e()}catch(e){$.Deferred.exceptionHook&&$.Deferred.exceptionHook(e,t.stackTrace),l<=o+1&&(s!==re&&(n=void 0,i=[e]),r.rejectWith(n,i))}};o?t():($.Deferred.getStackHook&&(t.stackTrace=$.Deferred.getStackHook()),k.setTimeout(t))}}return $.Deferred(function(e){r[0][3].add(c(0,e,y(i)?i:d,e.notifyWith)),r[1][3].add(c(0,e,y(t)?t:d)),r[2][3].add(c(0,e,y(n)?n:re))}).promise()},promise:function(e){return null!=e?$.extend(e,s):s}},a={};return $.each(r,function(e,t){var n=t[2],i=t[5];s[t[1]]=n.add,i&&n.add(function(){o=i},r[3-e][2].disable,r[3-e][3].disable,r[0][2].lock,r[0][3].lock),n.add(t[3].fire),a[t[0]]=function(){return a[t[0]+"With"](this===a?void 0:this,arguments),this},a[t[0]+"With"]=n.fireWith}),s.promise(a),e&&e.call(a,a),a},when:function(e){function t(t){return function(e){o[t]=this,r[t]=1<arguments.length?a.call(arguments):e,--n||s.resolveWith(o,r)}}var n=arguments.length,i=n,o=Array(i),r=a.call(arguments),s=$.Deferred();if(n<=1&&(se(e,s.done(t(i)).resolve,s.reject,!n),"pending"===s.state()||y(r[i]&&r[i].then)))return s.then();for(;i--;)se(r[i],t(i),s.reject);return s.promise()}});var ae=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/,le=($.Deferred.exceptionHook=function(e,t){k.console&&k.console.warn&&e&&ae.test(e.name)&&k.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},$.readyException=function(e){k.setTimeout(function(){throw e})},$.Deferred());function ce(){x.removeEventListener("DOMContentLoaded",ce),k.removeEventListener("load",ce),$.ready()}$.fn.ready=function(e){return le.then(e).catch(function(e){$.readyException(e)}),this},$.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--$.readyWait:$.isReady)||($.isReady=!0)!==e&&0<--$.readyWait||le.resolveWith(x,[$])}}),$.ready.then=le.then,"complete"===x.readyState||"loading"!==x.readyState&&!x.documentElement.doScroll?k.setTimeout($.ready):(x.addEventListener("DOMContentLoaded",ce),k.addEventListener("load",ce));function u(e,t,n,i,o,r,s){var a=0,l=e.length,c=null==n;if("object"===h(n))for(a in o=!0,n)u(e,t,a,n[a],!0,r,s);else if(void 0!==i&&(o=!0,y(i)||(s=!0),t=c?s?(t.call(e,i),null):(c=t,function(e,t,n){return c.call($(e),n)}):t))for(;a<l;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return o?e:c?t.call(e):l?t(e[0],n):r}var de=/^-ms-/,ue=/-([a-z])/g;function pe(e,t){return t.toUpperCase()}function b(e){return e.replace(de,"ms-").replace(ue,pe)}function fe(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function n(){this.expando=$.expando+n.uid++}n.uid=1,n.prototype={cache:function(e){var t=e[this.expando];return t||(t={},fe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,o=this.cache(e);if("string"==typeof t)o[b(t)]=n;else for(i in t)o[b(i)]=t[i];return o},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][b(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(b):(t=b(t))in i?[t]:t.match(T)||[]).length;for(;n--;)delete i[t[n]]}void 0!==t&&!$.isEmptyObject(i)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){e=e[this.expando];return void 0!==e&&!$.isEmptyObject(e)}};var v=new n,c=new n,he=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ge=/[A-Z]/g;function me(e,t,n){var i,o;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(ge,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(o=n)||"false"!==o&&("null"===o?null:o===+o+""?+o:he.test(o)?JSON.parse(o):o)}catch(e){}c.set(e,t,n)}else n=void 0;return n}$.extend({hasData:function(e){return c.hasData(e)||v.hasData(e)},data:function(e,t,n){return c.access(e,t,n)},removeData:function(e,t){c.remove(e,t)},_data:function(e,t,n){return v.access(e,t,n)},_removeData:function(e,t){v.remove(e,t)}}),$.fn.extend({data:function(n,e){var t,i,o,r=this[0],s=r&&r.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){c.set(this,n)}):u(this,function(e){var t;if(r&&void 0===e)return void 0!==(t=c.get(r,n))||void 0!==(t=me(r,n))?t:void 0;this.each(function(){c.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(o=c.get(r),1===r.nodeType)&&!v.get(r,"hasDataAttrs")){for(t=s.length;t--;)s[t]&&0===(i=s[t].name).indexOf("data-")&&(i=b(i.slice(5)),me(r,i,o[i]));v.set(r,"hasDataAttrs",!0)}return o},removeData:function(e){return this.each(function(){c.remove(this,e)})}}),$.extend({queue:function(e,t,n){var i;if(e)return i=v.get(e,t=(t||"fx")+"queue"),n&&(!i||Array.isArray(n)?i=v.access(e,t,$.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=$.queue(e,t),i=n.length,o=n.shift(),r=$._queueHooks(e,t);"inprogress"===o&&(o=n.shift(),i--),o&&("fx"===t&&n.unshift("inprogress"),delete r.stop,o.call(e,function(){$.dequeue(e,t)},r)),!i&&r&&r.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v.get(e,n)||v.access(e,n,{empty:$.Callbacks("once memory").add(function(){v.remove(e,[t+"queue",n])})})}}),$.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?$.queue(this[0],t):void 0===n?this:this.each(function(){var e=$.queue(this,t,n);$._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&$.dequeue(this,t)})},dequeue:function(e){return this.each(function(){$.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--o||r.resolveWith(s,[s])}var i,o=1,r=$.Deferred(),s=this,a=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(i=v.get(s[a],e+"queueHooks"))&&i.empty&&(o++,i.empty.add(n));return n(),r.promise(t)}});function ve(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&C(e)&&"none"===$.css(e,"display")}function ye(e,t,n,i){var o,r={};for(o in t)r[o]=e.style[o],e.style[o]=t[o];for(o in n=n.apply(e,i||[]),t)e.style[o]=r[o];return n}var e=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,p=new RegExp("^(?:([+-])=|)("+e+")([a-z%]*)$","i"),f=["Top","Right","Bottom","Left"],w=x.documentElement,C=function(e){return $.contains(e.ownerDocument,e)},be={composed:!0};w.getRootNode&&(C=function(e){return $.contains(e.ownerDocument,e)||e.getRootNode(be)===e.ownerDocument});function we(e,t,n,i){var o,r,s=20,a=i?function(){return i.cur()}:function(){return $.css(e,t,"")},l=a(),c=n&&n[3]||($.cssNumber[t]?"":"px"),d=e.nodeType&&($.cssNumber[t]||"px"!==c&&+l)&&p.exec($.css(e,t));if(d&&d[3]!==c){for(c=c||d[3],d=+(l/=2)||1;s--;)$.style(e,t,d+c),(1-r)*(1-(r=a()/l||.5))<=0&&(s=0),d/=r;$.style(e,t,(d*=2)+c),n=n||[]}return n&&(d=+d||+l||0,o=n[1]?d+(n[1]+1)*n[2]:+n[2],i)&&(i.unit=c,i.start=d,i.end=o),o}var ke={};function _(e,t){for(var n,i,o,r,s,a=[],l=0,c=e.length;l<c;l++)(i=e[l]).style&&(n=i.style.display,t?("none"===n&&(a[l]=v.get(i,"display")||null,a[l]||(i.style.display="")),""===i.style.display&&ve(i)&&(a[l]=(s=r=void 0,r=(o=i).ownerDocument,(s=ke[o=o.nodeName])||(r=r.body.appendChild(r.createElement(o)),s=$.css(r,"display"),r.parentNode.removeChild(r),ke[o]=s="none"===s?"block":s),s))):"none"!==n&&(a[l]="none",v.set(i,"display",n)));for(l=0;l<c;l++)null!=a[l]&&(e[l].style.display=a[l]);return e}$.fn.extend({show:function(){return _(this,!0)},hide:function(){return _(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){ve(this)?$(this).show():$(this).hide()})}});var xe=/^(?:checkbox|radio)$/i,$e=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Te=/^$|^module$|\/(?:java|ecma)script/i,S={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function j(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&l(e,t)?$.merge([e],n):n}function Ce(e,t){for(var n=0,i=e.length;n<i;n++)v.set(e[n],"globalEval",!t||v.get(t[n],"globalEval"))}S.optgroup=S.option,S.tbody=S.tfoot=S.colgroup=S.caption=S.thead,S.th=S.td;var _e=/<|&#?\w+;/;function Se(e,t,n,i,o){for(var r,s,a,l,c,d=t.createDocumentFragment(),u=[],p=0,f=e.length;p<f;p++)if((r=e[p])||0===r)if("object"===h(r))$.merge(u,r.nodeType?[r]:r);else if(_e.test(r)){for(s=s||d.appendChild(t.createElement("div")),a=($e.exec(r)||["",""])[1].toLowerCase(),a=S[a]||S._default,s.innerHTML=a[1]+$.htmlPrefilter(r)+a[2],c=a[0];c--;)s=s.lastChild;$.merge(u,s.childNodes),(s=d.firstChild).textContent=""}else u.push(t.createTextNode(r));for(d.textContent="",p=0;r=u[p++];)if(i&&-1<$.inArray(r,i))o&&o.push(r);else if(l=C(r),s=j(d.appendChild(r),"script"),l&&Ce(s),n)for(c=0;r=s[c++];)Te.test(r.type||"")&&n.push(r);return d}t=x.createDocumentFragment().appendChild(x.createElement("div")),(O=x.createElement("input")).setAttribute("type","radio"),O.setAttribute("checked","checked"),O.setAttribute("name","t"),t.appendChild(O),m.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",m.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue;var je=/^key/,Ee=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ae=/^([^.]*)(?:\.(.+)|)/;function s(){return!0}function E(){return!1}function De(e,t){return e===(()=>{try{return x.activeElement}catch(e){}})()==("focus"===t)}function Oe(e,t,n,i,o,r){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(i=i||n,n=void 0),t)Oe(e,a,n,i,t[a],r);return e}if(null==i&&null==o?(o=n,i=n=void 0):null==o&&("string"==typeof n?(o=i,i=void 0):(o=i,i=n,n=void 0)),!1===o)o=E;else if(!o)return e;return 1===r&&(s=o,(o=function(e){return $().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=$.guid++)),e.each(function(){$.event.add(this,t,o,i,n)})}function Le(e,o,r){r?(v.set(e,o,!1),$.event.add(e,o,{namespace:!1,handler:function(e){var t,n,i=v.get(this,o);if(1&e.isTrigger&&this[o]){if(i.length)($.event.special[o]||{}).delegateType&&e.stopPropagation();else if(i=a.call(arguments),v.set(this,o,i),t=r(this,o),this[o](),i!==(n=v.get(this,o))||t?v.set(this,o,!1):n={},i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else i.length&&(v.set(this,o,{value:$.event.trigger($.extend(i[0],$.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===v.get(e,o)&&$.event.add(e,o,s)}$.event={global:{},add:function(t,e,n,i,o){var r,s,a,l,c,d,u,p,f,h=v.get(t);if(h)for(n.handler&&(n=(r=n).handler,o=r.selector),o&&$.find.matchesSelector(w,o),n.guid||(n.guid=$.guid++),a=(a=h.events)||(h.events={}),s=(s=h.handle)||(h.handle=function(e){return void 0!==$&&$.event.triggered!==e.type?$.event.dispatch.apply(t,arguments):void 0}),l=(e=(e||"").match(T)||[""]).length;l--;)u=f=(p=Ae.exec(e[l])||[])[1],p=(p[2]||"").split(".").sort(),u&&(c=$.event.special[u]||{},u=(o?c.delegateType:c.bindType)||u,c=$.event.special[u]||{},f=$.extend({type:u,origType:f,data:i,handler:n,guid:n.guid,selector:o,needsContext:o&&$.expr.match.needsContext.test(o),namespace:p.join(".")},r),(d=a[u])||((d=a[u]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,i,p,s))||t.addEventListener&&t.addEventListener(u,s),c.add&&(c.add.call(t,f),f.handler.guid||(f.handler.guid=n.guid)),o?d.splice(d.delegateCount++,0,f):d.push(f),$.event.global[u]=!0)},remove:function(e,t,n,i,o){var r,s,a,l,c,d,u,p,f,h,g,m=v.hasData(e)&&v.get(e);if(m&&(l=m.events)){for(c=(t=(t||"").match(T)||[""]).length;c--;)if(f=g=(a=Ae.exec(t[c])||[])[1],h=(a[2]||"").split(".").sort(),f){for(u=$.event.special[f]||{},p=l[f=(i?u.delegateType:u.bindType)||f]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=r=p.length;r--;)d=p[r],!o&&g!==d.origType||n&&n.guid!==d.guid||a&&!a.test(d.namespace)||i&&i!==d.selector&&("**"!==i||!d.selector)||(p.splice(r,1),d.selector&&p.delegateCount--,u.remove&&u.remove.call(e,d));s&&!p.length&&(u.teardown&&!1!==u.teardown.call(e,h,m.handle)||$.removeEvent(e,f,m.handle),delete l[f])}else for(f in l)$.event.remove(e,f+t[c],n,i,!0);$.isEmptyObject(l)&&v.remove(e,"handle events")}},dispatch:function(e){var t,n,i,o,r,s=$.event.fix(e),a=new Array(arguments.length),e=(v.get(this,"events")||{})[s.type]||[],l=$.event.special[s.type]||{};for(a[0]=s,t=1;t<arguments.length;t++)a[t]=arguments[t];if(s.delegateTarget=this,!l.preDispatch||!1!==l.preDispatch.call(this,s)){for(r=$.event.handlers.call(this,s,e),t=0;(i=r[t++])&&!s.isPropagationStopped();)for(s.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!s.isImmediatePropagationStopped();)s.rnamespace&&!1!==o.namespace&&!s.rnamespace.test(o.namespace)||(s.handleObj=o,s.data=o.data,void 0!==(o=(($.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(s.result=o)&&(s.preventDefault(),s.stopPropagation()));return l.postDispatch&&l.postDispatch.call(this,s),s.result}},handlers:function(e,t){var n,i,o,r,s,a=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&1<=e.button))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(r=[],s={},n=0;n<l;n++)void 0===s[o=(i=t[n]).selector+" "]&&(s[o]=i.needsContext?-1<$(o,this).index(c):$.find(o,this,null,[c]).length),s[o]&&r.push(i);r.length&&a.push({elem:c,handlers:r})}return c=this,l<t.length&&a.push({elem:c,handlers:t.slice(l)}),a},addProp:function(t,e){Object.defineProperty($.Event.prototype,t,{enumerable:!0,configurable:!0,get:y(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[$.expando]?e:new $.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){e=this||e;return xe.test(e.type)&&e.click&&l(e,"input")&&Le(e,"click",s),!1},trigger:function(e){e=this||e;return xe.test(e.type)&&e.click&&l(e,"input")&&Le(e,"click"),!0},_default:function(e){e=e.target;return xe.test(e.type)&&e.click&&l(e,"input")&&v.get(e,"click")||l(e,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},$.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},$.Event=function(e,t){if(!(this instanceof $.Event))return new $.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?s:E,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&$.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[$.expando]=!0},$.Event.prototype={constructor:$.Event,isDefaultPrevented:E,isPropagationStopped:E,isImmediatePropagationStopped:E,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=s,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=s,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=s,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},$.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&je.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&Ee.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},$.event.addProp),$.each({focus:"focusin",blur:"focusout"},function(e,t){$.event.special[e]={setup:function(){return Le(this,e,De),!1},trigger:function(){return Le(this,e),!0},delegateType:t}}),$.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,o){$.event.special[e]={delegateType:o,bindType:o,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||$.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=o),t}}}),$.fn.extend({on:function(e,t,n,i){return Oe(this,e,t,n,i)},one:function(e,t,n,i){return Oe(this,e,t,n,i,1)},off:function(e,t,n){var i,o;if(e&&e.preventDefault&&e.handleObj)i=e.handleObj,$(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler);else{if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=E),this.each(function(){$.event.remove(this,e,n,t)});for(o in e)this.off(o,t,e[o])}return this}});var Ne=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,Pe=/<script|<style|<link/i,He=/checked\s*(?:[^=]|=\s*.checked.)/i,Ie=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Me(e,t){return l(e,"table")&&l(11!==t.nodeType?t:t.firstChild,"tr")&&$(e).children("tbody")[0]||e}function qe(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function ze(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function We(e,t){var n,i,o,r,s,a;if(1===t.nodeType){if(v.hasData(e)&&(r=v.access(e),s=v.set(t,r),a=r.events))for(o in delete s.handle,s.events={},a)for(n=0,i=a[o].length;n<i;n++)$.event.add(t,o,a[o][n]);c.hasData(e)&&(r=c.access(e),s=$.extend({},r),c.set(t,s))}}function A(n,i,o,r){i=M.apply([],i);var e,t,s,a,l,c,d=0,u=n.length,p=u-1,f=i[0],h=y(f);if(h||1<u&&"string"==typeof f&&!m.checkClone&&He.test(f))return n.each(function(e){var t=n.eq(e);h&&(i[0]=f.call(this,e,t.html())),A(t,i,o,r)});if(u&&(t=(e=Se(i,n[0].ownerDocument,!1,n,r)).firstChild,1===e.childNodes.length&&(e=t),t||r)){for(a=(s=$.map(j(e,"script"),qe)).length;d<u;d++)l=e,d!==p&&(l=$.clone(l,!0,!0),a)&&$.merge(s,j(l,"script")),o.call(n[d],l,d);if(a)for(c=s[s.length-1].ownerDocument,$.map(s,ze),d=0;d<a;d++)l=s[d],Te.test(l.type||"")&&!v.access(l,"globalEval")&&$.contains(c,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?$._evalUrl&&!l.noModule&&$._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")}):Y(l.textContent.replace(Ie,""),l,c))}return n}function Re(e,t,n){for(var i,o=t?$.filter(t,e):e,r=0;null!=(i=o[r]);r++)n||1!==i.nodeType||$.cleanData(j(i)),i.parentNode&&(n&&C(i)&&Ce(j(i,"script")),i.parentNode.removeChild(i));return e}$.extend({htmlPrefilter:function(e){return e.replace(Ne,"<$1></$2>")},clone:function(e,t,n){var i,o,r,s,a,l,c,d=e.cloneNode(!0),u=C(e);if(!(m.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||$.isXMLDoc(e)))for(s=j(d),i=0,o=(r=j(e)).length;i<o;i++)a=r[i],l=s[i],c=void 0,"input"===(c=l.nodeName.toLowerCase())&&xe.test(a.type)?l.checked=a.checked:"input"!==c&&"textarea"!==c||(l.defaultValue=a.defaultValue);if(t)if(n)for(r=r||j(e),s=s||j(d),i=0,o=r.length;i<o;i++)We(r[i],s[i]);else We(e,d);return 0<(s=j(d,"script")).length&&Ce(s,!u&&j(e,"script")),d},cleanData:function(e){for(var t,n,i,o=$.event.special,r=0;void 0!==(n=e[r]);r++)if(fe(n)){if(t=n[v.expando]){if(t.events)for(i in t.events)o[i]?$.event.remove(n,i):$.removeEvent(n,i,t.handle);n[v.expando]=void 0}n[c.expando]&&(n[c.expando]=void 0)}}}),$.fn.extend({detach:function(e){return Re(this,e,!0)},remove:function(e){return Re(this,e)},text:function(e){return u(this,function(e){return void 0===e?$.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return A(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Me(this,e).appendChild(e)})},prepend:function(){return A(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=Me(this,e)).insertBefore(e,t.firstChild)})},before:function(){return A(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return A(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&($.cleanData(j(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return $.clone(this,e,t)})},html:function(e){return u(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Pe.test(e)&&!S[($e.exec(e)||["",""])[1].toLowerCase()]){e=$.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&($.cleanData(j(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return A(this,arguments,function(e){var t=this.parentNode;$.inArray(this,n)<0&&($.cleanData(j(this)),t)&&t.replaceChild(e,this)},n)}}),$.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){$.fn[e]=function(e){for(var t,n=[],i=$(e),o=i.length-1,r=0;r<=o;r++)t=r===o?this:this.clone(!0),$(i[r])[s](t),q.apply(n,t.get());return this.pushStack(n)}});function Fe(e){var t=e.ownerDocument.defaultView;return(t=t&&t.opener?t:k).getComputedStyle(e)}var Be,Ue,Xe,Ye,Ge,Ve,o,Qe=new RegExp("^("+e+")(?!px)[a-z%]+$","i"),Ke=new RegExp(f.join("|"),"i");function Je(){var e;o&&(Ve.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",o.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",w.appendChild(Ve).appendChild(o),e=k.getComputedStyle(o),Be="1%"!==e.top,Ge=12===Ze(e.marginLeft),o.style.right="60%",Ye=36===Ze(e.right),Ue=36===Ze(e.width),o.style.position="absolute",Xe=12===Ze(o.offsetWidth/3),w.removeChild(Ve),o=null)}function Ze(e){return Math.round(parseFloat(e))}function et(e,t,n){var i,o,r=e.style;return(n=n||Fe(e))&&(""!==(o=n.getPropertyValue(t)||n[t])||C(e)||(o=$.style(e,t)),!m.pixelBoxStyles())&&Qe.test(o)&&Ke.test(t)&&(e=r.width,t=r.minWidth,i=r.maxWidth,r.minWidth=r.maxWidth=r.width=o,o=n.width,r.width=e,r.minWidth=t,r.maxWidth=i),void 0!==o?o+"":o}function tt(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}Ve=x.createElement("div"),(o=x.createElement("div")).style&&(o.style.backgroundClip="content-box",o.cloneNode(!0).style.backgroundClip="",m.clearCloneStyle="content-box"===o.style.backgroundClip,$.extend(m,{boxSizingReliable:function(){return Je(),Ue},pixelBoxStyles:function(){return Je(),Ye},pixelPosition:function(){return Je(),Be},reliableMarginLeft:function(){return Je(),Ge},scrollboxSize:function(){return Je(),Xe}}));var nt=["Webkit","Moz","ms"],it=x.createElement("div").style,ot={};function rt(e){var t=$.cssProps[e]||ot[e];return t||(e in it?e:ot[e]=(e=>{for(var t=e[0].toUpperCase()+e.slice(1),n=nt.length;n--;)if((e=nt[n]+t)in it)return e})(e)||e)}var st=/^(none|table(?!-c[ea]).+)/,at=/^--/,lt={position:"absolute",visibility:"hidden",display:"block"},ct={letterSpacing:"0",fontWeight:"400"};function dt(e,t,n){var i=p.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function ut(e,t,n,i,o,r){var s="width"===t?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=$.css(e,n+f[s],!0,o)),i?("content"===n&&(l-=$.css(e,"padding"+f[s],!0,o)),"margin"!==n&&(l-=$.css(e,"border"+f[s]+"Width",!0,o))):(l+=$.css(e,"padding"+f[s],!0,o),"padding"!==n?l+=$.css(e,"border"+f[s]+"Width",!0,o):a+=$.css(e,"border"+f[s]+"Width",!0,o));return!i&&0<=r&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-r-l-a-.5))||0),l}function pt(e,t,n){var i=Fe(e),o=(!m.boxSizingReliable()||n)&&"border-box"===$.css(e,"boxSizing",!1,i),r=o,s=et(e,t,i),a="offset"+t[0].toUpperCase()+t.slice(1);if(Qe.test(s)){if(!n)return s;s="auto"}return(!m.boxSizingReliable()&&o||"auto"===s||!parseFloat(s)&&"inline"===$.css(e,"display",!1,i))&&e.getClientRects().length&&(o="border-box"===$.css(e,"boxSizing",!1,i),r=a in e)&&(s=e[a]),(s=parseFloat(s)||0)+ut(e,t,n||(o?"border":"content"),r,i,s)+"px"}function r(e,t,n,i,o){return new r.prototype.init(e,t,n,i,o)}$.extend({cssHooks:{opacity:{get:function(e,t){if(t)return""===(t=et(e,"opacity"))?"1":t}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,r,s,a=b(t),l=at.test(t),c=e.style;if(l||(t=rt(a)),s=$.cssHooks[t]||$.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(o=s.get(e,!1,i))?o:c[t];"string"===(r=typeof n)&&(o=p.exec(n))&&o[1]&&(n=we(e,t,o),r="number"),null==n||n!=n||("number"!==r||l||(n+=o&&o[3]||($.cssNumber[a]?"":"px")),m.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i)))||(l?c.setProperty(t,n):c[t]=n)}},css:function(e,t,n,i){var o,r=b(t);return at.test(t)||(t=rt(r)),"normal"===(o=void 0===(o=(r=$.cssHooks[t]||$.cssHooks[r])&&"get"in r?r.get(e,!0,n):o)?et(e,t,i):o)&&t in ct&&(o=ct[t]),(""===n||n)&&(r=parseFloat(o),!0===n||isFinite(r))?r||0:o}}),$.each(["height","width"],function(e,s){$.cssHooks[s]={get:function(e,t,n){if(t)return!st.test($.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?pt(e,s,n):ye(e,lt,function(){return pt(e,s,n)})},set:function(e,t,n){var i=Fe(e),o=!m.scrollboxSize()&&"absolute"===i.position,r=(o||n)&&"border-box"===$.css(e,"boxSizing",!1,i),n=n?ut(e,s,n,r,i):0;return r&&o&&(n-=Math.ceil(e["offset"+s[0].toUpperCase()+s.slice(1)]-parseFloat(i[s])-ut(e,s,"border",!1,i)-.5)),n&&(r=p.exec(t))&&"px"!==(r[3]||"px")&&(e.style[s]=t,t=$.css(e,s)),dt(0,t,n)}}}),$.cssHooks.marginLeft=tt(m.reliableMarginLeft,function(e,t){if(t)return(parseFloat(et(e,"marginLeft"))||e.getBoundingClientRect().left-ye(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),$.each({margin:"",padding:"",border:"Width"},function(o,r){$.cssHooks[o+r]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[o+f[t]+r]=i[t]||i[t-2]||i[0];return n}},"margin"!==o&&($.cssHooks[o+r].set=dt)}),$.fn.extend({css:function(e,t){return u(this,function(e,t,n){var i,o,r={},s=0;if(Array.isArray(t)){for(i=Fe(e),o=t.length;s<o;s++)r[t[s]]=$.css(e,t[s],!1,i);return r}return void 0!==n?$.style(e,t,n):$.css(e,t)},e,t,1<arguments.length)}}),(($.Tween=r).prototype={constructor:r,init:function(e,t,n,i,o,r){this.elem=e,this.prop=n,this.easing=o||$.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=r||($.cssNumber[n]?"":"px")},cur:function(){var e=r.propHooks[this.prop];return(e&&e.get?e:r.propHooks._default).get(this)},run:function(e){var t,n=r.propHooks[this.prop];return this.options.duration?this.pos=t=$.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:r.propHooks._default).set(this),this}}).init.prototype=r.prototype,(r.propHooks={_default:{get:function(e){return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(e=$.css(e.elem,e.prop,""))&&"auto"!==e?e:0},set:function(e){$.fx.step[e.prop]?$.fx.step[e.prop](e):1!==e.elem.nodeType||!$.cssHooks[e.prop]&&null==e.elem.style[rt(e.prop)]?e.elem[e.prop]=e.now:$.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=r.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},$.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},$.fx=r.prototype.init,$.fx.step={};var D,ft,O,ht=/^(?:toggle|show|hide)$/,gt=/queueHooks$/;function mt(){ft&&(!1===x.hidden&&k.requestAnimationFrame?k.requestAnimationFrame(mt):k.setTimeout(mt,$.fx.interval),$.fx.tick())}function vt(){return k.setTimeout(function(){D=void 0}),D=Date.now()}function yt(e,t){var n,i=0,o={height:e};for(t=t?1:0;i<4;i+=2-t)o["margin"+(n=f[i])]=o["padding"+n]=e;return t&&(o.opacity=o.width=e),o}function bt(e,t,n){for(var i,o=(L.tweeners[t]||[]).concat(L.tweeners["*"]),r=0,s=o.length;r<s;r++)if(i=o[r].call(n,t,e))return i}function L(o,e,t){var n,r,i,s,a,l,c,d=0,u=L.prefilters.length,p=$.Deferred().always(function(){delete f.elem}),f=function(){if(!r){for(var e=D||vt(),e=Math.max(0,h.startTime+h.duration-e),t=1-(e/h.duration||0),n=0,i=h.tweens.length;n<i;n++)h.tweens[n].run(t);if(p.notifyWith(o,[h,t,e]),t<1&&i)return e;i||p.notifyWith(o,[h,1,0]),p.resolveWith(o,[h])}return!1},h=p.promise({elem:o,props:$.extend({},e),opts:$.extend(!0,{specialEasing:{},easing:$.easing._default},t),originalProperties:e,originalOptions:t,startTime:D||vt(),duration:t.duration,tweens:[],createTween:function(e,t){t=$.Tween(o,h.opts,e,t,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(t),t},stop:function(e){var t=0,n=e?h.tweens.length:0;if(!r){for(r=!0;t<n;t++)h.tweens[t].run(1);e?(p.notifyWith(o,[h,1,0]),p.resolveWith(o,[h,e])):p.rejectWith(o,[h,e])}return this}}),g=h.props,m=g,v=h.opts.specialEasing;for(i in m)if(a=v[s=b(i)],l=m[i],Array.isArray(l)&&(a=l[1],l=m[i]=l[0]),i!==s&&(m[s]=l,delete m[i]),(c=$.cssHooks[s])&&"expand"in c)for(i in l=c.expand(l),delete m[s],l)i in m||(m[i]=l[i],v[i]=a);else v[s]=a;for(;d<u;d++)if(n=L.prefilters[d].call(h,o,g,h.opts))return y(n.stop)&&($._queueHooks(h.elem,h.opts.queue).stop=n.stop.bind(n)),n;return $.map(g,bt,h),y(h.opts.start)&&h.opts.start.call(o,h),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),$.fx.timer($.extend(f,{elem:o,anim:h,queue:h.opts.queue})),h}$.Animation=$.extend(L,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return we(n.elem,e,p.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,o=(e=y(e)?(t=e,["*"]):e.match(T)).length;i<o;i++)n=e[i],L.tweeners[n]=L.tweeners[n]||[],L.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,o,r,s,a,l,c,d="width"in t||"height"in t,u=this,p={},f=e.style,h=e.nodeType&&ve(e),g=v.get(e,"fxshow");for(i in n.queue||(null==(s=$._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,u.always(function(){u.always(function(){s.unqueued--,$.queue(e,"fx").length||s.empty.fire()})})),t)if(o=t[i],ht.test(o)){if(delete t[i],r=r||"toggle"===o,o===(h?"hide":"show")){if("show"!==o||!g||void 0===g[i])continue;h=!0}p[i]=g&&g[i]||$.style(e,i)}if((l=!$.isEmptyObject(t))||!$.isEmptyObject(p))for(i in d&&1===e.nodeType&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],null==(c=g&&g.display)&&(c=v.get(e,"display")),"none"===(d=$.css(e,"display"))&&(c?d=c:(_([e],!0),c=e.style.display||c,d=$.css(e,"display"),_([e]))),"inline"===d||"inline-block"===d&&null!=c)&&"none"===$.css(e,"float")&&(l||(u.done(function(){f.display=c}),null==c&&(d=f.display,c="none"===d?"":d)),f.display="inline-block"),n.overflow&&(f.overflow="hidden",u.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]})),l=!1,p)l||(g?"hidden"in g&&(h=g.hidden):g=v.access(e,"fxshow",{display:c}),r&&(g.hidden=!h),h&&_([e],!0),u.done(function(){for(i in h||_([e]),v.remove(e,"fxshow"),p)$.style(e,i,p[i])})),l=bt(h?g[i]:0,i,u),i in g||(g[i]=l.start,h&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?L.prefilters.unshift(e):L.prefilters.push(e)}}),$.speed=function(e,t,n){var i=e&&"object"==typeof e?$.extend({},e):{complete:n||!n&&t||y(e)&&e,duration:e,easing:n&&t||t&&!y(t)&&t};return $.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in $.fx.speeds?i.duration=$.fx.speeds[i.duration]:i.duration=$.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){y(i.old)&&i.old.call(this),i.queue&&$.dequeue(this,i.queue)},i},$.fn.extend({fadeTo:function(e,t,n,i){return this.filter(ve).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){function o(){var e=L(this,$.extend({},t),s);(r||v.get(this,"finish"))&&e.stop(!0)}var r=$.isEmptyObject(t),s=$.speed(e,n,i);return o.finish=o,r||!1===s.queue?this.each(o):this.queue(s.queue,o)},stop:function(o,e,r){function s(e){var t=e.stop;delete e.stop,t(r)}return"string"!=typeof o&&(r=e,e=o,o=void 0),e&&!1!==o&&this.queue(o||"fx",[]),this.each(function(){var e=!0,t=null!=o&&o+"queueHooks",n=$.timers,i=v.get(this);if(t)i[t]&&i[t].stop&&s(i[t]);else for(t in i)i[t]&&i[t].stop&&gt.test(t)&&s(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=o&&n[t].queue!==o||(n[t].anim.stop(r),e=!1,n.splice(t,1));!e&&r||$.dequeue(this,o)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=v.get(this),n=t[s+"queue"],i=t[s+"queueHooks"],o=$.timers,r=n?n.length:0;for(t.finish=!0,$.queue(this,s,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===s&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),$.each(["toggle","show","hide"],function(e,i){var o=$.fn[i];$.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?o.apply(this,arguments):this.animate(yt(i,!0),e,t,n)}}),$.each({slideDown:yt("show"),slideUp:yt("hide"),slideToggle:yt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){$.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),$.timers=[],$.fx.tick=function(){var e,t=0,n=$.timers;for(D=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||$.fx.stop(),D=void 0},$.fx.timer=function(e){$.timers.push(e),$.fx.start()},$.fx.interval=13,$.fx.start=function(){ft||(ft=!0,mt())},$.fx.stop=function(){ft=null},$.fx.speeds={slow:600,fast:200,_default:400},$.fn.delay=function(i,e){return i=$.fx&&$.fx.speeds[i]||i,this.queue(e=e||"fx",function(e,t){var n=k.setTimeout(e,i);t.stop=function(){k.clearTimeout(n)}})},O=x.createElement("input"),t=x.createElement("select").appendChild(x.createElement("option")),O.type="checkbox",m.checkOn=""!==O.value,m.optSelected=t.selected,(O=x.createElement("input")).value="t",O.type="radio",m.radioValue="t"===O.value;var wt,kt=$.expr.attrHandle,xt=($.fn.extend({attr:function(e,t){return u(this,$.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){$.removeAttr(this,e)})}}),$.extend({attr:function(e,t,n){var i,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===e.getAttribute?$.prop(e,t,n):(1===r&&$.isXMLDoc(e)||(o=$.attrHooks[t.toLowerCase()]||($.expr.match.bool.test(t)?wt:void 0)),void 0!==n?null===n?void $.removeAttr(e,t):o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:(e.setAttribute(t,n+""),n):!(o&&"get"in o&&null!==(i=o.get(e,t)))&&null==(i=$.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){var n;if(!m.radioValue&&"radio"===t&&l(e,"input"))return n=e.value,e.setAttribute("type",t),n&&(e.value=n),t}}},removeAttr:function(e,t){var n,i=0,o=t&&t.match(T);if(o&&1===e.nodeType)for(;n=o[i++];)e.removeAttribute(n)}}),wt={set:function(e,t,n){return!1===t?$.removeAttr(e,n):e.setAttribute(n,n),n}},$.each($.expr.match.bool.source.match(/\w+/g),function(e,t){var s=kt[t]||$.find.attr;kt[t]=function(e,t,n){var i,o,r=t.toLowerCase();return n||(o=kt[r],kt[r]=i,i=null!=s(e,t,n)?r:null,kt[r]=o),i}}),/^(?:input|select|textarea|button)$/i),$t=/^(?:a|area)$/i;function N(e){return(e.match(T)||[]).join(" ")}function P(e){return e.getAttribute&&e.getAttribute("class")||""}function Tt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(T)||[]}$.fn.extend({prop:function(e,t){return u(this,$.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[$.propFix[e]||e]})}}),$.extend({prop:function(e,t,n){var i,o,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&$.isXMLDoc(e)||(t=$.propFix[t]||t,o=$.propHooks[t]),void 0!==n?o&&"set"in o&&void 0!==(i=o.set(e,n,t))?i:e[t]=n:o&&"get"in o&&null!==(i=o.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=$.find.attr(e,"tabindex");return t?parseInt(t,10):xt.test(e.nodeName)||$t.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),m.optSelected||($.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(e){e=e.parentNode;e&&(e.selectedIndex,e.parentNode)&&e.parentNode.selectedIndex}}),$.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){$.propFix[this.toLowerCase()]=this}),$.fn.extend({addClass:function(t){var e,n,i,o,r,s,a=0;if(y(t))return this.each(function(e){$(this).addClass(t.call(this,e,P(this)))});if((e=Tt(t)).length)for(;n=this[a++];)if(s=P(n),i=1===n.nodeType&&" "+N(s)+" "){for(r=0;o=e[r++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");s!==(s=N(i))&&n.setAttribute("class",s)}return this},removeClass:function(t){var e,n,i,o,r,s,a=0;if(y(t))return this.each(function(e){$(this).removeClass(t.call(this,e,P(this)))});if(!arguments.length)return this.attr("class","");if((e=Tt(t)).length)for(;n=this[a++];)if(s=P(n),i=1===n.nodeType&&" "+N(s)+" "){for(r=0;o=e[r++];)for(;-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");s!==(s=N(i))&&n.setAttribute("class",s)}return this},toggleClass:function(o,t){var r=typeof o,s="string"==r||Array.isArray(o);return"boolean"==typeof t&&s?t?this.addClass(o):this.removeClass(o):y(o)?this.each(function(e){$(this).toggleClass(o.call(this,e,P(this),t),t)}):this.each(function(){var e,t,n,i;if(s)for(t=0,n=$(this),i=Tt(o);e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==o&&"boolean"!=r||((e=P(this))&&v.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==o&&v.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,i=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+N(P(t))+" ").indexOf(i))return!0;return!1}});function Ct(e){e.stopPropagation()}var _t=/\r/g,St=($.fn.extend({val:function(t){var n,e,i,o=this[0];return arguments.length?(i=y(t),this.each(function(e){1!==this.nodeType||(null==(e=i?t.call(this,e,$(this).val()):t)?e="":"number"==typeof e?e+="":Array.isArray(e)&&(e=$.map(e,function(e){return null==e?"":e+""})),(n=$.valHooks[this.type]||$.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value"))||(this.value=e)})):o?(n=$.valHooks[o.type]||$.valHooks[o.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(o,"value"))?e:"string"==typeof(e=o.value)?e.replace(_t,""):null==e?"":e:void 0}}),$.extend({valHooks:{option:{get:function(e){var t=$.find.attr(e,"value");return null!=t?t:N($.text(e))}},select:{get:function(e){for(var t,n=e.options,i=e.selectedIndex,o="select-one"===e.type,r=o?null:[],s=o?i+1:n.length,a=i<0?s:o?i:0;a<s;a++)if(((t=n[a]).selected||a===i)&&!t.disabled&&(!t.parentNode.disabled||!l(t.parentNode,"optgroup"))){if(t=$(t).val(),o)return t;r.push(t)}return r},set:function(e,t){for(var n,i,o=e.options,r=$.makeArray(t),s=o.length;s--;)((i=o[s]).selected=-1<$.inArray($.valHooks.option.get(i),r))&&(n=!0);return n||(e.selectedIndex=-1),r}}}}),$.each(["radio","checkbox"],function(){$.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<$.inArray($(e).val(),t)}},m.checkOn||($.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),m.focusin="onfocusin"in k,/^(?:focusinfocus|focusoutblur)$/),jt=($.extend($.event,{trigger:function(e,t,n,i){var o,r,s,a,l,c,d,u=[n||x],p=F.call(e,"type")?e.type:e,f=F.call(e,"namespace")?e.namespace.split("."):[],h=d=r=n=n||x;if(3!==n.nodeType&&8!==n.nodeType&&!St.test(p+$.event.triggered)&&(-1<p.indexOf(".")&&(p=(f=p.split(".")).shift(),f.sort()),a=p.indexOf(":")<0&&"on"+p,(e=e[$.expando]?e:new $.Event(p,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=f.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:$.makeArray(t,[e]),c=$.event.special[p]||{},i||!c.trigger||!1!==c.trigger.apply(n,t))){if(!i&&!c.noBubble&&!g(n)){for(s=c.delegateType||p,St.test(s+p)||(h=h.parentNode);h;h=h.parentNode)u.push(h),r=h;r===(n.ownerDocument||x)&&u.push(r.defaultView||r.parentWindow||k)}for(o=0;(h=u[o++])&&!e.isPropagationStopped();)d=h,e.type=1<o?s:c.bindType||p,(l=(v.get(h,"events")||{})[e.type]&&v.get(h,"handle"))&&l.apply(h,t),(l=a&&h[a])&&l.apply&&fe(h)&&(e.result=l.apply(h,t),!1===e.result)&&e.preventDefault();return e.type=p,i||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(u.pop(),t)||!fe(n)||a&&y(n[p])&&!g(n)&&((r=n[a])&&(n[a]=null),$.event.triggered=p,e.isPropagationStopped()&&d.addEventListener(p,Ct),n[p](),e.isPropagationStopped()&&d.removeEventListener(p,Ct),$.event.triggered=void 0,r)&&(n[a]=r),e.result}},simulate:function(e,t,n){n=$.extend(new $.Event,n,{type:e,isSimulated:!0});$.event.trigger(n,null,t)}}),$.fn.extend({trigger:function(e,t){return this.each(function(){$.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return $.event.trigger(e,t,n,!0)}}),m.focusin||$.each({focus:"focusin",blur:"focusout"},function(n,i){function o(e){$.event.simulate(i,e.target,$.event.fix(e))}$.event.special[i]={setup:function(){var e=this.ownerDocument||this,t=v.access(e,i);t||e.addEventListener(n,o,!0),v.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this,t=v.access(e,i)-1;t?v.access(e,i,t):(e.removeEventListener(n,o,!0),v.remove(e,i))}}}),k.location),Et=Date.now(),At=/\?/,Dt=($.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new k.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||$.error("Invalid XML: "+e),t},/\[\]$/),Ot=/\r?\n/g,Lt=/^(?:submit|button|image|reset|file)$/i,Nt=/^(?:input|select|textarea|keygen)/i;$.param=function(e,t){function n(e,t){t=y(t)?t():t,o[o.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==t?"":t)}var i,o=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!$.isPlainObject(e))$.each(e,function(){n(this.name,this.value)});else for(i in e)!function n(i,e,o,r){if(Array.isArray(e))$.each(e,function(e,t){o||Dt.test(i)?r(i,t):n(i+"["+("object"==typeof t&&null!=t?e:"")+"]",t,o,r)});else if(o||"object"!==h(e))r(i,e);else for(var t in e)n(i+"["+t+"]",e[t],o,r)}(i,e[i],t,n);return o.join("&")},$.fn.extend({serialize:function(){return $.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=$.prop(this,"elements");return e?$.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!$(this).is(":disabled")&&Nt.test(this.nodeName)&&!Lt.test(e)&&(this.checked||!xe.test(e))}).map(function(e,t){var n=$(this).val();return null==n?null:Array.isArray(n)?$.map(n,function(e){return{name:t.name,value:e.replace(Ot,"\r\n")}}):{name:t.name,value:n.replace(Ot,"\r\n")}}).get()}});var Pt=/%20/g,Ht=/#.*$/,It=/([?&])_=[^&]*/,Mt=/^(.*?):[ \t]*([^\r\n]*)$/gm,qt=/^(?:GET|HEAD)$/,zt=/^\/\//,Wt={},Rt={},Ft="*/".concat("*"),Bt=x.createElement("a");function Ut(r){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,o=e.toLowerCase().match(T)||[];if(y(t))for(;n=o[i++];)"+"===n[0]?(n=n.slice(1)||"*",(r[n]=r[n]||[]).unshift(t)):(r[n]=r[n]||[]).push(t)}}function Xt(t,i,o,r){var s={},a=t===Rt;function l(e){var n;return s[e]=!0,$.each(t[e]||[],function(e,t){t=t(i,o,r);return"string"!=typeof t||a||s[t]?a?!(n=t):void 0:(i.dataTypes.unshift(t),l(t),!1)}),n}return l(i.dataTypes[0])||!s["*"]&&l("*")}function Yt(e,t){var n,i,o=$.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((o[n]?e:i=i||{})[n]=t[n]);return i&&$.extend(!0,e,i),e}Bt.href=jt.href,$.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:jt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(jt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Ft,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":$.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Yt(Yt(e,$.ajaxSettings),t):Yt($.ajaxSettings,e)},ajaxPrefilter:Ut(Wt),ajaxTransport:Ut(Rt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var l,c,d,n,u,p,f,i,h=$.ajaxSetup({},t=t||{}),g=h.context||h,m=h.context&&(g.nodeType||g.jquery)?$(g):$.event,v=$.Deferred(),y=$.Callbacks("once memory"),b=h.statusCode||{},o={},r={},s="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(p){if(!n)for(n={};t=Mt.exec(d);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return p?d:null},setRequestHeader:function(e,t){return null==p&&(e=r[e.toLowerCase()]=r[e.toLowerCase()]||e,o[e]=t),this},overrideMimeType:function(e){return null==p&&(h.mimeType=e),this},statusCode:function(e){if(e)if(p)w.always(e[w.status]);else for(var t in e)b[t]=[b[t],e[t]];return this},abort:function(e){e=e||s;return l&&l.abort(e),a(0,e),this}};if(v.promise(w),h.url=((e||h.url||jt.href)+"").replace(zt,jt.protocol+"//"),h.type=t.method||t.type||h.method||h.type,h.dataTypes=(h.dataType||"*").toLowerCase().match(T)||[""],null==h.crossDomain){e=x.createElement("a");try{e.href=h.url,e.href=e.href,h.crossDomain=Bt.protocol+"//"+Bt.host!=e.protocol+"//"+e.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=$.param(h.data,h.traditional)),Xt(Wt,h,t,w),!p){for(i in(f=$.event&&h.global)&&0==$.active++&&$.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!qt.test(h.type),c=h.url.replace(Ht,""),h.hasContent?h.data&&h.processData&&0===(h.contentType||"").indexOf("application/x-www-form-urlencoded")&&(h.data=h.data.replace(Pt,"+")):(e=h.url.slice(c.length),h.data&&(h.processData||"string"==typeof h.data)&&(c+=(At.test(c)?"&":"?")+h.data,delete h.data),!1===h.cache&&(c=c.replace(It,"$1"),e=(At.test(c)?"&":"?")+"_="+Et+++e),h.url=c+e),h.ifModified&&($.lastModified[c]&&w.setRequestHeader("If-Modified-Since",$.lastModified[c]),$.etag[c])&&w.setRequestHeader("If-None-Match",$.etag[c]),(h.data&&h.hasContent&&!1!==h.contentType||t.contentType)&&w.setRequestHeader("Content-Type",h.contentType),w.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+Ft+"; q=0.01":""):h.accepts["*"]),h.headers)w.setRequestHeader(i,h.headers[i]);if(h.beforeSend&&(!1===h.beforeSend.call(g,w,h)||p))return w.abort();if(s="abort",y.add(h.complete),w.done(h.success),w.fail(h.error),l=Xt(Rt,h,t,w)){if(w.readyState=1,f&&m.trigger("ajaxSend",[w,h]),p)return w;h.async&&0<h.timeout&&(u=k.setTimeout(function(){w.abort("timeout")},h.timeout));try{p=!1,l.send(o,a)}catch(e){if(p)throw e;a(-1,e)}}else a(-1,"No Transport")}return w;function a(e,t,n,i){var o,r,s,a=t;p||(p=!0,u&&k.clearTimeout(u),l=void 0,d=i||"",w.readyState=0<e?4:0,i=200<=e&&e<300||304===e,n&&(s=((e,t,n)=>{for(var i,o,r,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(o in a)if(a[o]&&a[o].test(i)){l.unshift(o);break}if(l[0]in n)r=l[0];else{for(o in n){if(!l[0]||e.converters[o+" "+l[0]]){r=o;break}s=s||o}r=r||s}if(r)return r!==l[0]&&l.unshift(r),n[r]})(h,w,n)),s=((e,t,n,i)=>{var o,r,s,a,l,c={},d=e.dataTypes.slice();if(d[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(r=d.shift();r;)if(e.responseFields[r]&&(n[e.responseFields[r]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=r,r=d.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(s=c[l+" "+r]||c["* "+r]))for(o in c)if((a=o.split(" "))[1]===r&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[o]:!0!==c[o]&&(r=a[0],d.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+r}}}return{state:"success",data:t}})(h,s,w,i),i?(h.ifModified&&((n=w.getResponseHeader("Last-Modified"))&&($.lastModified[c]=n),n=w.getResponseHeader("etag"))&&($.etag[c]=n),204===e||"HEAD"===h.type?a="nocontent":304===e?a="notmodified":(a=s.state,o=s.data,i=!(r=s.error))):(r=a,!e&&a||(a="error",e<0&&(e=0))),w.status=e,w.statusText=(t||a)+"",i?v.resolveWith(g,[o,a,w]):v.rejectWith(g,[w,a,r]),w.statusCode(b),b=void 0,f&&m.trigger(i?"ajaxSuccess":"ajaxError",[w,h,i?o:r]),y.fireWith(g,[w,a]),f&&(m.trigger("ajaxComplete",[w,h]),--$.active||$.event.trigger("ajaxStop")))}},getJSON:function(e,t,n){return $.get(e,t,n,"json")},getScript:function(e,t){return $.get(e,void 0,t,"script")}}),$.each(["get","post"],function(e,o){$[o]=function(e,t,n,i){return y(t)&&(i=i||n,n=t,t=void 0),$.ajax($.extend({url:e,type:o,dataType:i,data:t,success:n},$.isPlainObject(e)&&e))}}),$._evalUrl=function(e,t){return $.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){$.globalEval(e,t)}})},$.fn.extend({wrapAll:function(e){return this[0]&&(y(e)&&(e=e.call(this[0])),e=$(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return y(n)?this.each(function(e){$(this).wrapInner(n.call(this,e))}):this.each(function(){var e=$(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=y(t);return this.each(function(e){$(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){$(this).replaceWith(this.childNodes)}),this}}),$.expr.pseudos.hidden=function(e){return!$.expr.pseudos.visible(e)},$.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},$.ajaxSettings.xhr=function(){try{return new k.XMLHttpRequest}catch(e){}};var Gt={0:200,1223:204},Vt=$.ajaxSettings.xhr(),Qt=(m.cors=!!Vt&&"withCredentials"in Vt,m.ajax=Vt=!!Vt,$.ajaxTransport(function(o){var r,s;if(m.cors||Vt&&!o.crossDomain)return{send:function(e,t){var n,i=o.xhr();if(i.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(n in o.xhrFields)i[n]=o.xhrFields[n];for(n in o.mimeType&&i.overrideMimeType&&i.overrideMimeType(o.mimeType),o.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)i.setRequestHeader(n,e[n]);r=function(e){return function(){r&&(r=s=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null,"abort"===e?i.abort():"error"===e?"number"!=typeof i.status?t(0,"error"):t(i.status,i.statusText):t(Gt[i.status]||i.status,i.statusText,"text"!==(i.responseType||"text")||"string"!=typeof i.responseText?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders()))}},i.onload=r(),s=i.onerror=i.ontimeout=r("error"),void 0!==i.onabort?i.onabort=s:i.onreadystatechange=function(){4===i.readyState&&k.setTimeout(function(){r&&s()})},r=r("abort");try{i.send(o.hasContent&&o.data||null)}catch(e){if(r)throw e}},abort:function(){r&&r()}}}),$.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),$.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return $.globalEval(e),e}}}),$.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),$.ajaxTransport("script",function(n){var i,o;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){i=$("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",o=function(e){i.remove(),o=null,e&&t("error"===e.type?404:200,e.type)}),x.head.appendChild(i[0])},abort:function(){o&&o()}}}),[]),Kt=/(=)\?(?=&|$)|\?\?/,Jt=($.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Qt.pop()||$.expando+"_"+Et++;return this[e]=!0,e}}),$.ajaxPrefilter("json jsonp",function(e,t,n){var i,o,r,s=!1!==e.jsonp&&(Kt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Kt.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=y(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Kt,"$1"+i):!1!==e.jsonp&&(e.url+=(At.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return r||$.error(i+" was not called"),r[0]},e.dataTypes[0]="json",o=k[i],k[i]=function(){r=arguments},n.always(function(){void 0===o?$(k).removeProp(i):k[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,Qt.push(i)),r&&y(o)&&o(r[0]),r=o=void 0}),"script"}),m.createHTMLDocument=((e=x.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),$.parseHTML=function(e,t,n){var i;return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(m.createHTMLDocument?((i=(t=x.implementation.createHTMLDocument("")).createElement("base")).href=x.location.href,t.head.appendChild(i)):t=x),i=!n&&[],(n=J.exec(e))?[t.createElement(n[1])]:(n=Se([e],t,i),i&&i.length&&$(i).remove(),$.merge([],n.childNodes)))},$.fn.load=function(e,t,n){var i,o,r,s=this,a=e.indexOf(" ");return-1<a&&(i=N(e.slice(a)),e=e.slice(0,a)),y(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),0<s.length&&$.ajax({url:e,type:o||"GET",dataType:"html",data:t}).done(function(e){r=arguments,s.html(i?$("<div>").append($.parseHTML(e)).find(i):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,r||[e.responseText,t,e])})}),this},$.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){$.fn[t]=function(e){return this.on(t,e)}}),$.expr.pseudos.animated=function(t){return $.grep($.timers,function(e){return t===e.elem}).length},$.offset={setOffset:function(e,t,n){var i,o,r,s,a=$.css(e,"position"),l=$(e),c={};"static"===a&&(e.style.position="relative"),r=l.offset(),i=$.css(e,"top"),s=$.css(e,"left"),a=("absolute"===a||"fixed"===a)&&-1<(i+s).indexOf("auto")?(o=(a=l.position()).top,a.left):(o=parseFloat(i)||0,parseFloat(s)||0),null!=(t=y(t)?t.call(e,n,$.extend({},r)):t).top&&(c.top=t.top-r.top+o),null!=t.left&&(c.left=t.left-r.left+a),"using"in t?t.using.call(e,c):l.css(c)}},$.fn.extend({offset:function(t){var e,n;return arguments.length?void 0===t?this:this.each(function(e){$.offset.setOffset(this,t,e)}):(n=this[0])?n.getClientRects().length?(e=n.getBoundingClientRect(),n=n.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],o={top:0,left:0};if("fixed"===$.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===$.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((o=$(e).offset()).top+=$.css(e,"borderTopWidth",!0),o.left+=$.css(e,"borderLeftWidth",!0))}return{top:t.top-o.top-$.css(i,"marginTop",!0),left:t.left-o.left-$.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===$.css(e,"position");)e=e.offsetParent;return e||w})}}),$.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,o){var r="pageYOffset"===o;$.fn[t]=function(e){return u(this,function(e,t,n){var i;if(g(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===n)return i?i[o]:e[t];i?i.scrollTo(r?i.pageXOffset:n,r?n:i.pageYOffset):e[t]=n},t,e,arguments.length)}}),$.each(["top","left"],function(e,n){$.cssHooks[n]=tt(m.pixelPosition,function(e,t){if(t)return t=et(e,n),Qe.test(t)?$(e).position()[n]+"px":t})}),$.each({Height:"height",Width:"width"},function(s,a){$.each({padding:"inner"+s,content:a,"":"outer"+s},function(i,r){$.fn[r]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),o=i||(!0===e||!0===t?"margin":"border");return u(this,function(e,t,n){var i;return g(e)?0===r.indexOf("outer")?e["inner"+s]:e.document.documentElement["client"+s]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+s],i["scroll"+s],e.body["offset"+s],i["offset"+s],i["client"+s])):void 0===n?$.css(e,t,o):$.style(e,t,n,o)},a,n?e:void 0,n)}})}),$.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){$.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),$.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),$.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}}),$.proxy=function(e,t){var n,i;if("string"==typeof t&&(i=e[t],t=e,e=i),y(e))return n=a.call(arguments,2),(i=function(){return e.apply(t||this,n.concat(a.call(arguments)))}).guid=e.guid=e.guid||$.guid++,i},$.holdReady=function(e){e?$.readyWait++:$.ready(!0)},$.isArray=Array.isArray,$.parseJSON=JSON.parse,$.nodeName=l,$.isFunction=y,$.isWindow=g,$.camelCase=b,$.type=h,$.now=Date.now,$.isNumeric=function(e){var t=$.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},"function"==typeof define&&define.amd&&define("jquery",[],function(){return $}),k.jQuery),Zt=k.$;return $.noConflict=function(e){return k.$===$&&(k.$=Zt),e&&k.jQuery===$&&(k.jQuery=Jt),$},H||(k.jQuery=k.$=$),$}),(c=>{function i(e,t){var n;if(!(this instanceof i))return(n=new i(e,t)).open(),n;this.id=i.id++,this.setup(e,t),this.chainCallbacks(i._callbackChain)}var o,r,n,s,a;void 0===c?"console"in window&&window.console.info("Too much lightness, Featherlight needs jQuery."):(o=[],r=function(t){return o=c.grep(o,function(e){return e!==t&&0<e.$instance.closest("body").length})},n={keyup:"onKeyUp",resize:"onResize"},i.prototype={constructor:i,namespace:"featherlight",targetAttr:"data-featherlight",variant:null,resetCss:!(a=function(e){var t;e!==i._globalHandlerInstalled&&(i._globalHandlerInstalled=e,t=c.map(n,function(e,t){return t+"."+i.prototype.namespace}).join(" "),c(window)[e?"on":"off"](t,s))}),background:null,openTrigger:"click",closeTrigger:"click",filter:null,root:"body",openSpeed:250,closeSpeed:250,closeOnClick:"background",closeOnEsc:!0,closeIcon:"&#10005;",loading:"",persist:!(s=function(e){c.each(i.opened().reverse(),function(){return e.isDefaultPrevented()||!1!==this[n[e.type]](e)?void 0:(e.preventDefault(),e.stopPropagation(),!1)})}),otherClose:null,beforeOpen:c.noop,beforeContent:c.noop,beforeClose:c.noop,afterOpen:c.noop,afterContent:c.noop,afterClose:c.noop,onKeyUp:c.noop,onResize:c.noop,type:null,contentFilters:["jquery","image","html","ajax","iframe","text"],setup:function(e,t){"object"!=typeof e||e instanceof c!=0||t||(t=e,e=void 0);var n=c.extend(this,t,{target:e}),t=n.resetCss?n.namespace+"-reset":n.namespace,e=c(n.background||['<div class="'+t+"-loading "+t+'">','<div class="'+t+'-content">','<span class="'+t+"-close-icon "+n.namespace+'-close">',n.closeIcon,"</span>",'<div class="'+n.namespace+'-inner">'+n.loading+"</div>","</div>","</div>"].join("")),i="."+n.namespace+"-close"+(n.otherClose?","+n.otherClose:"");return n.$instance=e.clone().addClass(n.variant),n.$instance.on(n.closeTrigger+"."+n.namespace,function(e){var t=c(e.target);("background"===n.closeOnClick&&t.is("."+n.namespace)||"anywhere"===n.closeOnClick||t.closest(i).length)&&(n.close(e),e.preventDefault())}),this},getContent:function(){if(!1!==this.persist&&this.$content)return this.$content;function e(e){return t.$currentTarget&&t.$currentTarget.attr(e)}var t=this,n=this.constructor.contentFilters,i=e(t.targetAttr),o=t.target||i||"",r=n[t.type];if(!r&&o in n&&(r=n[o],o=t.target&&i),o=o||e("href")||"",!r)for(var s in n)t[s]&&(r=n[s],o=t[s]);if(!r){var a=o,o=null;if(c.each(t.contentFilters,function(){return r=n[this],!(o=!(o=r.test?r.test(a):o)&&r.regex&&a.match&&a.match(r.regex)?a:o)}),!o)return"console"in window&&window.console.error("Featherlight: no content filter found "+(a?' for "'+a+'"':" (no target specified)")),!1}return r.process.call(t,o)},setContent:function(e){var t=this;return(e.is("iframe")||0<c("iframe",e).length)&&t.$instance.addClass(t.namespace+"-iframe"),t.$instance.removeClass(t.namespace+"-loading"),t.$instance.find("."+t.namespace+"-inner").not(e).slice(1).remove().end().replaceWith(c.contains(t.$instance[0],e[0])?"":e),t.$content=e.addClass(t.namespace+"-inner"),t},open:function(t){var n=this;if(n.$instance.hide().appendTo(n.root),!(t&&t.isDefaultPrevented()||!1===n.beforeOpen(t))){t&&t.preventDefault();var e=n.getContent();if(e)return o.push(n),a(!0),n.$instance.fadeIn(n.openSpeed),n.beforeContent(t),c.when(e).always(function(e){n.setContent(e),n.afterContent(t)}).then(n.$instance.promise()).done(function(){n.afterOpen(t)})}return n.$instance.detach(),c.Deferred().reject().promise()},close:function(e){var t=this,n=c.Deferred();return!1===t.beforeClose(e)?n.reject():(0===r(t).length&&a(!1),t.$instance.fadeOut(t.closeSpeed,function(){t.$instance.detach(),t.afterClose(e),n.resolve()})),n.promise()},chainCallbacks:function(e){for(var t in e)this[t]=c.proxy(e[t],this,c.proxy(this[t],this))}},c.extend(i,{id:0,autoBind:"[data-featherlight]",defaults:i.prototype,contentFilters:{jquery:{regex:/^[#.]\w/,test:function(e){return e instanceof c&&e},process:function(e){return!1!==this.persist?c(e):c(e).clone(!0)}},image:{regex:/\.(png|jpg|jpeg|gif|tiff|bmp|svg)(\?\S*)?$/i,process:function(e){var t=c.Deferred(),n=new Image,i=c('<img src="'+e+'" alt="" class="'+this.namespace+'-image" />');return n.onload=function(){i.naturalWidth=n.width,i.naturalHeight=n.height,t.resolve(i)},n.onerror=function(){t.reject(i)},n.src=e,t.promise()}},html:{regex:/^\s*<[\w!][^<]*>/,process:function(e){return c(e)}},ajax:{regex:/./,process:function(e){var n=c.Deferred(),i=c("<div></div>").load(e,function(e,t){"error"!==t&&n.resolve(i.contents()),n.fail()});return n.promise()}},iframe:{process:function(e){var t=new c.Deferred,n=c("<iframe/>").hide().attr("src",e).css(((e,t)=>{var n,i={},o=new RegExp("^"+t+"([A-Z])(.*)");for(n in e){var r=n.match(o);r&&(i[(r[1]+r[2].replace(/([A-Z])/g,"-$1")).toLowerCase()]=e[n])}return i})(this,"iframe")).on("load",function(){t.resolve(n.show())}).appendTo(this.$instance.find("."+this.namespace+"-content"));return t.promise()}},text:{process:function(e){return c("<div>",{text:e})}}},functionAttributes:["beforeOpen","afterOpen","beforeContent","afterContent","beforeClose","afterClose"],readElementConfig:function(e,t){var n=this,i=new RegExp("^data-"+t+"-(.*)"),o={};return e&&e.attributes&&c.each(e.attributes,function(){var e=this.name.match(i);if(e){var t=this.value,e=c.camelCase(e[1]);if(0<=c.inArray(e,n.functionAttributes))t=new Function(t);else try{t=c.parseJSON(t)}catch(e){}o[e]=t}}),o},extend:function(e,t){function n(){this.constructor=e}return n.prototype=this.prototype,e.prototype=new n,e.__super__=this.prototype,c.extend(e,this,t),e.defaults=e.prototype,e},attach:function(i,o,r){var s=this;"object"!=typeof o||o instanceof c!=0||r||(r=o,o=void 0);var a,e=(r=c.extend({},r)).namespace||s.defaults.namespace,l=c.extend({},s.defaults,s.readElementConfig(i[0],e),r);return i.on(l.openTrigger+"."+l.namespace,l.filter,function(e){var t=c.extend({$source:i,$currentTarget:c(this)},s.readElementConfig(i[0],l.namespace),s.readElementConfig(this,l.namespace),r),n=a||c(this).data("featherlight-persisted")||new s(o,t);"shared"===n.persist?a=n:!1!==n.persist&&c(this).data("featherlight-persisted",n),t.$currentTarget.blur(),n.open(e)}),i},current:function(){var e=this.opened();return e[e.length-1]||null},opened:function(){var t=this;return r(),c.grep(o,function(e){return e instanceof t})},close:function(e){var t=this.current();return t?t.close(e):void 0},_onReady:function(){var t=this;t.autoBind&&(c(t.autoBind).each(function(){t.attach(c(this))}),c(document).on("click",t.autoBind,function(e){e.isDefaultPrevented()||"featherlight"===e.namespace||(e.preventDefault(),t.attach(c(e.currentTarget)),c(e.target).trigger("click.featherlight"))}))},_callbackChain:{onKeyUp:function(e,t){return 27===t.keyCode?(this.closeOnEsc&&c.featherlight.close(t),!1):e(t)},onResize:function(e,t){var n,i,o;return this.$content.naturalWidth&&(n=this.$content.naturalWidth,i=this.$content.naturalHeight,this.$content.css("width","").css("height",""),1<(o=Math.max(n/parseInt(this.$content.parent().css("width"),10),i/parseInt(this.$content.parent().css("height"),10))))&&this.$content.css("width",n/o+"px").css("height",i/o+"px"),e(t)},afterContent:function(e,t){e=e(t);return this.onResize(t),e}}}),c.featherlight=i,c.fn.featherlight=function(e,t){return i.attach(this,e,t)},c(document).ready(function(){i._onReady()}))})(jQuery),(r=>{function e(e){window.console&&window.console.warn&&window.console.warn("FeatherlightGallery: "+e)}var t,n,i,o,s;void 0===r?e("Too much lightness, Featherlight needs jQuery."):r.featherlight?(t="ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch,n=r.event&&r.event.special.swipeleft&&r,i=window.Hammer&&function(e){e=new window.Hammer.Manager(e[0]);return e.add(new window.Hammer.Swipe),e},o=t&&(n||i),t&&!o&&e("No compatible swipe library detected; one must be included before featherlightGallery for swipe motions to navigate the galleries."),s={afterClose:function(e,t){var n=this;return n.$instance.off("next."+n.namespace+" previous."+n.namespace),n._swiper&&(n._swiper.off("swipeleft",n._swipeleft).off("swiperight",n._swiperight),n._swiper=null),e(t)},beforeOpen:function(e,t){var n=this;return n.$instance.on("next."+n.namespace+" previous."+n.namespace,function(e){e="next"===e.type?1:-1;n.navigateTo(n.currentNavigation()+e)}),o&&(n._swiper=o(n.$instance).on("swipeleft",n._swipeleft=function(){n.$instance.trigger("next")}).on("swiperight",n._swiperight=function(){n.$instance.trigger("previous")}),n.$instance.addClass(this.namespace+"-swipe-aware",o)),n.$instance.find("."+n.namespace+"-content").append(n.createNavigation("previous")).append(n.createNavigation("next")),e(t)},beforeContent:function(e,t){var n=this.currentNavigation(),i=this.slides().length;return this.$instance.toggleClass(this.namespace+"-first-slide",0===n).toggleClass(this.namespace+"-last-slide",n===i-1),e(t)},onKeyUp:function(e,t){var n={37:"previous",39:"next"}[t.keyCode];return n?(this.$instance.trigger(n),!1):e(t)}},r.featherlight.extend(a,{autoBind:"[data-featherlight-gallery]"}),r.extend(a.prototype,{previousIcon:"&#9664;",nextIcon:"&#9654;",galleryFadeIn:100,galleryFadeOut:300,slides:function(){return this.filter?this.$source.find(this.filter):this.$source},images:function(){return e("images is deprecated, please use slides instead"),this.slides()},currentNavigation:function(){return this.slides().index(this.$currentTarget)},navigateTo:function(e){var t=this,n=t.slides(),i=n.length,o=t.$instance.find("."+t.namespace+"-inner");return t.$currentTarget=n.eq(e=(e%i+i)%i),t.beforeContent(),r.when(t.getContent(),o.fadeTo(t.galleryFadeOut,.2)).always(function(e){t.setContent(e),t.afterContent(),e.fadeTo(t.galleryFadeIn,1)})},createNavigation:function(e){var t=this;return r('<span title="'+e+'" class="'+this.namespace+"-"+e+'"><span>'+this[e+"Icon"]+"</span></span>").click(function(){r(this).trigger(e+"."+t.namespace)})}}),r.featherlightGallery=a,r.fn.featherlightGallery=function(e){return a.attach(this,e),this},r(document).ready(function(){a._onReady()})):e("Load the featherlight plugin before the gallery plugin");function a(e,t){if(!(this instanceof a))return(e=new a(r.extend({$source:e,$currentTarget:e.first()},t))).open(),e;r.featherlight.apply(this,arguments),this.chainCallbacks(s)}})(jQuery),window.FollowHeight=function(e){var t=this,n=(this.destroyed=!1,this.options={selector:"[data-follow-height]",bp_selector:"[data-follow-height-break-on]",break_on:"",breakpoints:{small:640,medium:1024,large:1200}},null!==e&&"object"==typeof e&&(this.options=this.mergeOptions(this.options,e)),document.querySelector(this.options.selector),window.onload=function(){return t.destroyed?null:t.update()},null);window.onresize=function(e){clearTimeout(n),n=setTimeout(function(){return t.destroyed?null:t.update()},250)}},FollowHeight.prototype.update=function(e,t){var i=this,e=void 0===e?this.options.selector:e,t=void 0===t?this.options.bp_selector:t,o=e.replace("[","").replace("]",""),r=t.replace("[","").replace("]",""),t=document.querySelectorAll(e),s=(this.removeTransitions(this.options.selector),[]);this.forEach(t,function(e,t){var n=t.getAttribute(o);return s[n]=Math.max.apply(null,Array.prototype.map.call(document.querySelectorAll("["+o+'="'+n+'"]'),function(e){var t=e.style.height,n=(e.style.height="",e.offsetHeight);return e.style.height=t,n})),""!=i.options.break_on?i.options.break_on%1==0&&window.innerWidth<=i.options.break_on||i.options.breakpoints[i.options.break_on]&&window.innerWidth<=i.options.breakpoints[i.options.break_on]?t.style.height="":t.style.height=s[n]+"px":t.hasAttribute(r)&&(i.options.breakpoints[t.getAttribute(r)]&&window.innerWidth<=i.options.breakpoints[t.getAttribute(r)]||window.innerWidth<=t.getAttribute(r))?t.style.height="":void(t.style.height=s[n]+"px")}),this.restoreTransitions(this.options.selector)},FollowHeight.prototype.destroy=function(){var e=document.querySelectorAll(this.options.selector);return this.forEach(e,function(e,t){return t.style.height=""}),this.destroyed=!0},FollowHeight.prototype.mergeOptions=function(e,t){var n,i={};for(n in e)i[n]=e[n];for(n in t)i[n]=t[n];return i},FollowHeight.prototype.forEach=function(e,t,n){for(var i=0;i<e.length;i++)t.call(n,i,e[i])},FollowHeight.prototype.removeTransitions=function(e){e=document.querySelectorAll(e);this.forEach(e,function(e,t){t.style.webkitTransition="none",t.style.mozTransition="none",t.style.msTransition="none",t.style.oTransition="none",t.style.transition="none"})},FollowHeight.prototype.restoreTransitions=function(e){e=document.querySelectorAll(e);this.forEach(e,function(e,t){t.style.webkitTransition="",t.style.mozTransition="",t.style.msTransition="",t.style.oTransition="",t.style.transition=""})},new FollowHeight,(e=>{"function"==typeof define&&define.amd?define(["jquery"],e):jQuery&&!jQuery.fn.hoverIntent&&e(jQuery)})(function(c){var o,r,s={interval:100,sensitivity:6,timeout:0},d=0,u=function(e){o=e.pageX,r=e.pageY},p=function(e,t,n,i){if(Math.sqrt((n.pX-o)*(n.pX-o)+(n.pY-r)*(n.pY-r))<i.sensitivity)return t.off(n.event,u),delete n.timeoutId,n.isActive=!0,e.pageX=o,e.pageY=r,delete n.pX,delete n.pY,i.over.apply(t[0],[e]);n.pX=o,n.pY=r,n.timeoutId=setTimeout(function(){p(e,t,n,i)},i.interval)};c.fn.hoverIntent=function(e,t,n){function i(e){var o=c.extend({},e),r=c(this),s=((t=r.data("hoverIntent"))||r.data("hoverIntent",t={}),t[a]),t=(s||(t[a]=s={id:a}),s.timeoutId&&(s.timeoutId=clearTimeout(s.timeoutId)),s.event="mousemove.hoverIntent.hoverIntent"+a);"mouseenter"===e.type?s.isActive||(s.pX=o.pageX,s.pY=o.pageY,r.off(t,u).on(t,u),s.timeoutId=setTimeout(function(){p(o,r,s,l)},l.interval)):s.isActive&&(r.off(t,u),s.timeoutId=setTimeout(function(){var e,t,n,i;e=o,t=r,n=s,i=l.out,delete t.data("hoverIntent")[n.id],i.apply(t[0],[e])},l.timeout))}var a=d++,l=c.extend({},s);c.isPlainObject(e)?(l=c.extend(l,e),c.isFunction(l.out)||(l.out=l.over)):l=c.isFunction(t)?c.extend(l,{over:e,out:t,selector:n}):c.extend(l,{over:e,out:e,selector:t});return this.on({"mouseenter.hoverIntent":i,"mouseleave.hoverIntent":i},l.selector)}});var InstantClick=((l,o)=>{var s,t,n,r,c,a,d,u,e=navigator.userAgent,M=-1<e.indexOf(" CriOS/"),p="createTouch"in l,f={},h=!1,g=!1,m=!1,v=!1,y={},b=!1,w=!1,k=[],x={fetch:[],receive:[],wait:[],change:[],beforeChange:[]};function $(e){var t=e.indexOf("#");return t<0?e:e.substr(0,t)}function T(e){for(;e&&"A"!=e.nodeName;)e=e.parentNode;return e}function C(e){var t=o.protocol+"//"+o.host;return!(e.target||e.hasAttribute("download")||0!=e.href.indexOf(t+"/")||-1<e.href.indexOf("#")&&$(e.href)==s||(a?!(e=>{do{if(!e.hasAttribute)break;if(e.hasAttribute("data-no-instant"))return;if(e.hasAttribute("data-instant"))return 1}while(e=e.parentNode)})(e):(e=>{do{if(!e.hasAttribute)break;if(e.hasAttribute("data-instant"))return;if(e.hasAttribute("data-no-instant"))return 1}while(e=e.parentNode)})(e)))}function _(e,t,n,i){for(var o,r=!1,s=0;s<x[e].length;s++)"receive"==e?(o=x[e][s](t,n,i))&&("body"in o&&(n=o.body),"title"in o&&(i=o.title),r=o):x[e][s](t,n,i);return r}function S(e,t,n,i){if(_("beforeChange"),l.body=t,n){history.pushState(null,null,n),new_page=!0;var t=n.indexOf("#"),o=-1<t&&l.getElementById(n.substr(t+1)),r=0;if(o)for(;o.offsetParent;)r+=o.offsetTop,o=o.offsetParent;scrollTo(0,r),s=$(n)}else new_page=!1,scrollTo(0,i);M&&l.title==e?l.title=e+String.fromCharCode(160):l.title=e,X(),P.done(),_("change",!1);t=l.createEvent("HTMLEvents");t.initEvent("instantclick:newpage",!0,!0),dispatchEvent(t)}function q(){w=b=!1}function z(e){r>+new Date-500||(e=T(e.target))&&C(e)&&E(e.href)}function W(e){r>+new Date-500||(e=T(e.target))&&C(e)&&(e.addEventListener("mouseout",B),u?(t=e.href,n=setTimeout(E,u)):E(e.href))}function R(e){r=+new Date;e=T(e.target);e&&C(e)&&(d?e.removeEventListener("mousedown",z):e.removeEventListener("mouseover",W),E(e.href))}function F(e){var t=T(e.target);t&&C(t)&&(1<e.which||e.metaKey||e.ctrlKey||(e.preventDefault(),Y(t.href)))}function B(){n?(clearTimeout(n),n=!1):b&&!w&&(c.abort(),q())}function U(){if(!(c.readyState<4)&&0!=c.status){if(y.ready=+new Date-y.start,c.getResponseHeader("Content-Type").match(/\/(x|ht|xht)ml/)){for(var e,t=l.implementation.createHTMLDocument(""),n=(t.documentElement.innerHTML=c.responseText,g=t.title,_("receive",h,v=t.body,g)),n=(n&&("body"in n&&(v=n.body),"title"in n)&&(g=n.title),$(h)),i=(f[n]={body:v,title:g,scrollY:n in f?f[n].scrollY:0},t.head.children),o=0,r=i.length-1;0<=r;r--)if((e=i[r]).hasAttribute("data-instant-track"))for(var s=e.getAttribute("href")||e.getAttribute("src")||e.innerHTML,a=k.length-1;0<=a;a--)k[a]==s&&o++;o!=k.length&&(m=!0)}else m=!0;w&&(w=!1,Y(h))}}function X(e){if(l.body.addEventListener("touchstart",R,!0),d?l.body.addEventListener("mousedown",z,!0):l.body.addEventListener("mouseover",W,!0),l.body.addEventListener("click",F,!0),!e){var t,n,o,r,s=l.body.getElementsByTagName("script");for(i=0,j=s.length;i<j;i++)(t=s[i]).hasAttribute("data-no-instant")||(n=l.createElement("script"),t.src&&(n.src=t.src),t.innerHTML&&(n.innerHTML=t.innerHTML),o=t.parentNode,r=t.nextSibling,o.removeChild(t),o.insertBefore(n,r))}}function E(e){!d&&"display"in y&&+new Date-(y.start+y.display)<100||(n&&(clearTimeout(n),n=!1),e=e||t,b&&(e==h||w))||(h=e,m=v=w=!(b=!0),y={start:+new Date},_("fetch"),c.open("GET",e),c.send())}function Y(e){if("display"in y||(y.display=+new Date-y.start),n||!b)return n&&h&&h!=e?void(o.href=e):(E(e),P.start(0,!0),_("wait"),void(w=!0));w?o.href=e:m?o.href=h:v?(f[s].scrollY=pageYOffset,q(),S(g,v,h)):(P.start(0,!0),_("wait"),w=!0)}var A,D,O,L,N,P={init:function(){(A=l.createElement("div")).id="instantclick",(D=l.createElement("div")).id="instantclick-bar",D.className="instantclick-bar",A.appendChild(D);var e=["Webkit","Moz","O"];if(!((O="transform")in D.style))for(var t=0;t<3;t++)e[t]+"Transform"in D.style&&(O=e[t]+"Transform");var n="transition";if(!(n in D.style))for(t=0;t<3;t++)e[t]+"Transition"in D.style&&(n="-"+e[t].toLowerCase()+"-"+n);var i=l.createElement("style");i.innerHTML="#instantclick{position:"+(p?"absolute":"fixed")+";top:0;left:0;width:100%;pointer-events:none;z-index:2147483647;"+n+":opacity .25s .1s}.instantclick-bar{background:#29d;width:100%;margin-left:-100%;height:2px;"+n+":all .25s}",l.head.appendChild(i),p&&(I(),addEventListener("resize",I),addEventListener("scroll",I))},start:G,done:function e(){l.getElementById(A.id)?(clearTimeout(N),L=100,H(),A.style.opacity="0"):(G(100==L?0:L),setTimeout(e,0))}};function G(e,t){L=e,l.getElementById(A.id)&&l.body.removeChild(A),A.style.opacity="1",l.getElementById(A.id)&&l.body.removeChild(A),H(),t&&setTimeout(V,0),clearTimeout(N),N=setTimeout(Q,500)}function V(){L=10,H()}function Q(){98<=(L+=1+2*Math.random())?L=98:N=setTimeout(Q,500),H()}function H(){D.style[O]="translate("+L+"%)",l.getElementById(A.id)||l.body.appendChild(A)}function I(){A.style.left=pageXOffset+"px",A.style.width=innerWidth+"px",A.style.top=pageYOffset+"px";var e="orientation"in window&&90==Math.abs(orientation),e=innerWidth/screen[e?"height":"width"]*2;A.style[O]="scaleY("+e+")"}var K="pushState"in history&&(!e.match("Android")||e.match("Chrome/"))&&"file:"!=o.protocol;return{supported:K,init:function(){if(!s)if(K){for(var e=arguments.length-1;0<=e;e--){var t=arguments[e];!0===t?a=!0:"mousedown"==t?d=!0:"number"==typeof t&&(u=t)}s=$(o.href),f[s]={body:l.body,title:l.title,scrollY:pageYOffset};for(var n,i=l.head.children,e=i.length-1;0<=e;e--)(n=i[e]).hasAttribute("data-instant-track")&&(n=n.getAttribute("href")||n.getAttribute("src")||n.innerHTML,k.push(n));(c=new XMLHttpRequest).addEventListener("readystatechange",U),X(!0),P.init(),_("change",!0),addEventListener("popstate",function(){var e=$(o.href);e!=s&&(e in f?(f[s].scrollY=pageYOffset,S(f[s=e].title,f[e].body,!1,f[e].scrollY)):o.reload())})}else _("change",!0)},on:function(e,t){x[e].push(t)}}})(document,location);function oneTimeExecution(e){if(!already_executed)return e()}function flashActive(){var e=$("[data-active]:not([data-active-ignore]), [data-active-children] a:not([data-active-ignore]), [data-active-on]:not([data-active-ignore])");e.length&&e.each(function(){var e,t=$(this),n=!1;t.addClass("js_init--flashActive"),(e=t.attr("data-active-on")?t.attr("data-active-on").split("|"):[t.attr("href")])&&(e.forEach(function(e){switch(e){case"/":n=window.location.pathname===e;break;case"#":n=!1;break;default:n=0<=window.location.pathname.indexOf(e)}}),n)&&t.addClass("active")})}((n,o)=>{var i="cookielaw",r={cookieName:"eucookielaw",elementName:"cookieMessage",elementClass:"animated bounce-up-in",buttonName:"cookieAccept",buttonText:"Hide",message:"This site uses cookies. By continuing to browse the site you are agreeing to our use of cookies.",backgroundColour:"#ddd",textColour:"#666",linkColour:"#2980b9",textAlign:"center",padding:"1.5em",fontFamily:'Georgia, "Times New Roman", Times, Serif'};function t(e,t){this.element=e,this.settings=n.extend({},r,t),this._defaults=r,this._name=i,this.init()}t.prototype={init:function(){this.getCookie(this.settings.cookieName)||(n(this.element).append('<div id="'+this.settings.elementName+'" class="'+this.settings.elementClass+'">'+this.settings.message+' <a id="'+this.settings.buttonName+'"class="button" href="#">'+this.settings.buttonText+"</a></div>"),2<=parseInt(n().jquery.charAt(0))?n("#"+this.settings.buttonName).one("click",{context:this},this.hideBanner):n("#"+this.settings.buttonName).on("click",{context:this},this.hideBanner))},hideBanner:function(e){e=e.data.context;return e.createCookie(e.settings.cookieName,!0,3560),n("#"+e.settings.elementName).slideUp(),!1},createCookie:function(e,t,n){var i;n=n?((i=new Date).setTime(i.getTime()+24*n*60*60*1e3),"; expires="+i.toGMTString()):"",o.cookie=e+"="+t+n+"; path=/"},getCookie:function(e){return 0<o.cookie.length&&-1!=(c_start=o.cookie.indexOf(e+"="))?(c_start=c_start+e.length+1,-1==(c_end=o.cookie.indexOf(";",c_start))&&(c_end=o.cookie.length),unescape(o.cookie.substring(c_start,c_end))):""}},n.fn[i]=function(e){return this.each(function(){n.data(this,"plugin_"+i)||n.data(this,"plugin_"+i,new t(this,e))})}})(jQuery,(window,document)),(i=>{function c(e){return e instanceof Object&&0<Object.keys(e).length}i.fn.jsonViewer=function(t,n){return n=n||{},this.each(function(){var e=function e(t,n){var i,o="";if("string"==typeof t)t=t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),i=t,/^(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/.test(i)?o+='<a href="'+t+'" class="json-string">'+t+"</a>":o+='<span class="json-string">"'+t+'"</span>';else if("number"==typeof t)o+='<span class="json-literal">'+t+"</span>";else if("boolean"==typeof t)o+='<span class="json-literal">'+t+"</span>";else if(null===t)o+='<span class="json-literal">null</span>';else if(t instanceof Array)if(0<t.length){o+='[<ol class="json-array">';for(var r=0;r<t.length;++r)o+="<li>",c(t[r])&&(o+='<a href="#" class="json-toggle"></a>'),o+=e(t[r],n),r<t.length-1&&(o+=","),o+="</li>";o+="</ol>]"}else o+="[]";else if("object"==typeof t){var s,a=Object.keys(t).length;if(0<a){for(var l in o+='<span class="bracket">{</span><ul class="json-dict">',t)t.hasOwnProperty(l)&&(o+="<li>",s=n.withQuotes?'<span class="json-string">"'+l+'"</span>':l,c(t[l])?o+='<a href="#" class="json-toggle">'+s+"</a>":o+=s,o+=": "+e(t[l],n),0<--a&&(o+=","),o+="</li>");o+='</ul><span class="bracket">}</span>'}else o+='<span class="bracket">{}</span>'}return o}(t,n);c(t)&&(e='<a href="#" class="json-toggle"></a>'+e),i(this).html(e),i(this).off("click"),i(this).on("click","a.json-toggle",function(e){var t,n=i(this).toggleClass("collapsed").siblings("ul.json-dict, ol.json-array");return n.toggle(),n.is(":visible")?n.siblings(".json-placeholder").remove():(t=(t=n.children("li").length)+(1<t?" items":" item"),n.parent().hasClass("json-viewer")?n.after('<a href="#" class="json-placeholder">Click here to show</a>'):n.after('<a href="#" class="json-placeholder">'+t+"</a>")),!1}),i(this).on("click","a.json-placeholder",function(){return i(this).siblings("a.json-toggle").click(),!1}),1==n.collapsed&&i(this).find("a.json-toggle").click()})}})(jQuery),((s,d,C)=>{function _(e,t){return typeof e===t}function S(e){return e.replace(/([a-z])-([a-z])/g,function(e,t,n){return t+n.toUpperCase()}).replace(/^-/,"")}function j(e){return"function"!=typeof d.createElement?d.createElement(e):k?d.createElementNS.call(d,"http://www.w3.org/2000/svg",e):d.createElement.apply(d,arguments)}function o(e){return e.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}).replace(/^ms-/,"-ms-")}function r(e,t,n,i){var o,r,s,a,l="modernizr",c=j("div");(a=d.body)||((a=j(k?"svg":"body")).fake=!0);if(parseInt(n,10))for(;n--;)(r=j("div")).id=i?i[n]:l+(n+1),c.appendChild(r);return(o=j("style")).type="text/css",o.id="s"+l,(a.fake?a:c).appendChild(o),a.appendChild(c),o.styleSheet?o.styleSheet.cssText=e:o.appendChild(d.createTextNode(e)),c.id=l,a.fake&&(a.style.background="",a.style.overflow="hidden",s=w.style.overflow,w.style.overflow="hidden",w.appendChild(a)),o=t(c,e),a.fake?(a.parentNode.removeChild(a),w.style.overflow=s,w.offsetHeight):c.parentNode.removeChild(c),!!o}function E(e,t){var n=e.length;if("CSS"in s&&"supports"in s.CSS){for(;n--;)if(s.CSS.supports(o(e[n]),t))return!0;return!1}if("CSSSupportsRule"in s){for(var i=[];n--;)i.push("("+o(e[n])+":"+t+")");return r("@supports ("+(i=i.join(" or "))+") { #modernizr { position: absolute; } }",function(e){return"absolute"==(e=e,t=null,n="position","getComputedStyle"in s?(i=getComputedStyle.call(s,e,t),o=s.console,null!==i?n&&(i=i.getPropertyValue(n)):o&&o[o.error?"error":"log"].call(o,"getComputedStyle returning null, its possible modernizr test results are inaccurate")):i=!t&&e.currentStyle&&e.currentStyle[n],i);var t,n,i,o})}return C}function i(e,t,n,i,o){var r,s,a=e.charAt(0).toUpperCase()+e.slice(1),l=(e+" "+A.join(a+" ")+a).split(" ");if(_(t,"string")||void 0===t){var c=l,d=t,u=i,p=o;function f(){h&&(delete O.style,delete O.modElem)}if(p=void 0!==p&&p,void 0!==u){l=E(c,u);if(void 0!==l)return l}for(var h,g,m,v,y,b=["modernizr","tspan","samp"];!O.style&&b.length;)h=!0,O.modElem=j(b.shift()),O.style=O.modElem.style;for(m=c.length,g=0;g<m;g++)if(v=c[g],y=O.style[v],~(""+v).indexOf("-")&&(v=S(v)),O.style[v]!==C){if(p||void 0===u)return f(),"pfx"!=d||v;try{O.style[v]=u}catch(e){}if(O.style[v]!=y)return f(),"pfx"!=d||v}f()}else{var w=(e+" "+D.join(a+" ")+a).split(" "),k=t,x=n;for(s in w)if(w[s]in k)if(!1===x)return w[s];else{r=k[w[s]];if(_(r,"function")){var $=r;var T=x||k;return function(){return $.apply(T,arguments)};return}else return r}}return!1}function a(e){var t,n=prefixes.length,i=s.CSSRule;if(void 0===i)return C;if(e){if((t=(e=e.replace(/^@/,"")).replace(/-/g,"_").toUpperCase()+"_RULE")in i)return"@"+e;for(var o=0;o<n;o++){var r=prefixes[o];if(r.toUpperCase()+"_"+t in i)return"@-"+r.toLowerCase()+"-"+e}}return!1}var e,t,n,l,c,u,p,f,h,g,m=[],v=[],y={_version:"3.5.0",_config:{classPrefix:"",enableClasses:!0,enableJSClass:!0,usePrefixes:!0},_q:[],on:function(e,t){var n=this;setTimeout(function(){t(n[e])},0)},addTest:function(e,t,n){v.push({name:e,fn:t,options:n})},addAsyncTest:function(e){v.push({name:null,fn:e})}},b=function(){},w=(b.prototype=y,b=new b,d.documentElement),k="svg"===w.nodeName.toLowerCase(),x="Moz O ms Webkit",A=y._config.usePrefixes?x.split(" "):[],D=(y._cssomPrefixes=A,y.atRule=a,y._config.usePrefixes?x.toLowerCase().split(" "):[]),$=(y._domPrefixes=D,{elem:j("modernizr")}),O=(b._q.push(function(){delete $.elem}),{style:$.elem.style}),x=(b._q.unshift(function(){delete O.style}),y.testAllProps=i,y.prefixed=function(e,t,n){return 0===e.indexOf("@")?a(e):(-1!=e.indexOf("-")&&(e=S(e)),t?i(e,t,n):i(e,"pfx"))});for(p in b.addTest("objectfit",!!x("objectFit"),{aliases:["object-fit"]}),v)if(v.hasOwnProperty(p)){if(e=[],(t=v[p]).name&&(e.push(t.name.toLowerCase()),t.options)&&t.options.aliases&&t.options.aliases.length)for(n=0;n<t.options.aliases.length;n++)e.push(t.options.aliases[n].toLowerCase());for(l=_(t.fn,"function")?t.fn():t.fn,c=0;c<e.length;c++)1===(u=e[c].split(".")).length?b[u[0]]=l:(!b[u[0]]||b[u[0]]instanceof Boolean||(b[u[0]]=new Boolean(b[u[0]])),b[u[0]][u[1]]=l),m.push((l?"":"no-")+u.join("-"))}x=m,h=w.className,g=b._config.classPrefix||"",k&&(h=h.baseVal),b._config.enableJSClass&&(f=new RegExp("(^|\\s)"+g+"no-js(\\s|$)"),h=h.replace(f,"$1"+g+"js$2")),b._config.enableClasses&&(h+=" "+g+x.join(" "+g),k?w.className.baseVal=h:w.className=h),delete y.addTest,delete y.addAsyncTest;for(var T=0;T<b._q.length;T++)b._q[T]();s.Modernizr=b})(window,document),(e=>{"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof exports?module.exports=e(require("jquery")):e(jQuery)})(function(c){var i,s=window.Slick||{};i=0,(s=function(e,t){var n=this;n.defaults={accessibility:!0,adaptiveHeight:!1,appendArrows:c(e),appendDots:c(e),arrows:!0,asNavFor:null,prevArrow:'<button type="button" data-role="none" class="slick-prev" aria-label="Previous" tabindex="0" role="button">Previous</button>',nextArrow:'<button type="button" data-role="none" class="slick-next" aria-label="Next" tabindex="0" role="button">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(e,t){return c('<button type="button" data-role="none" role="button" tabindex="0" />').text(t+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,infinite:!0,initialSlide:0,lazyLoad:"ondemand",mobileFirst:!1,pauseOnHover:!0,pauseOnFocus:!0,pauseOnDotsHover:!1,respondTo:"window",responsive:null,rows:1,rtl:!1,slide:"",slidesPerRow:1,slidesToShow:1,slidesToScroll:1,speed:500,swipe:!0,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,verticalSwiping:!1,waitForAnimate:!0,zIndex:1e3},n.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,$list:null,touchObject:{},transformsEnabled:!1,unslicked:!1},c.extend(n,n.initials),n.activeBreakpoint=null,n.animType=null,n.animProp=null,n.breakpoints=[],n.breakpointSettings=[],n.cssTransitions=!1,n.focussed=!1,n.interrupted=!1,n.hidden="hidden",n.paused=!0,n.positionProp=null,n.respondTo=null,n.rowCount=1,n.shouldClick=!0,n.$slider=c(e),n.$slidesCache=null,n.transformType=null,n.transitionType=null,n.visibilityChange="visibilitychange",n.windowWidth=0,n.windowTimer=null,e=c(e).data("slick")||{},n.options=c.extend({},n.defaults,t,e),n.currentSlide=n.options.initialSlide,n.originalSettings=n.options,void 0!==document.mozHidden?(n.hidden="mozHidden",n.visibilityChange="mozvisibilitychange"):void 0!==document.webkitHidden&&(n.hidden="webkitHidden",n.visibilityChange="webkitvisibilitychange"),n.autoPlay=c.proxy(n.autoPlay,n),n.autoPlayClear=c.proxy(n.autoPlayClear,n),n.autoPlayIterator=c.proxy(n.autoPlayIterator,n),n.changeSlide=c.proxy(n.changeSlide,n),n.clickHandler=c.proxy(n.clickHandler,n),n.selectHandler=c.proxy(n.selectHandler,n),n.setPosition=c.proxy(n.setPosition,n),n.swipeHandler=c.proxy(n.swipeHandler,n),n.dragHandler=c.proxy(n.dragHandler,n),n.keyHandler=c.proxy(n.keyHandler,n),n.instanceUid=i++,n.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/,n.registerBreakpoints(),n.init(!0)}).prototype.activateADA=function(){this.$slideTrack.find(".slick-active").attr({"aria-hidden":"false"}).find("a, input, button, select").attr({tabindex:"0"})},s.prototype.addSlide=s.prototype.slickAdd=function(e,t,n){var i=this;if("boolean"==typeof t)n=t,t=null;else if(t<0||t>=i.slideCount)return!1;i.unload(),"number"==typeof t?0===t&&0===i.$slides.length?c(e).appendTo(i.$slideTrack):n?c(e).insertBefore(i.$slides.eq(t)):c(e).insertAfter(i.$slides.eq(t)):!0===n?c(e).prependTo(i.$slideTrack):c(e).appendTo(i.$slideTrack),i.$slides=i.$slideTrack.children(this.options.slide),i.$slideTrack.children(this.options.slide).detach(),i.$slideTrack.append(i.$slides),i.$slides.each(function(e,t){c(t).attr("data-slick-index",e)}),i.$slidesCache=i.$slides,i.reinit()},s.prototype.animateHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.animate({height:e},t.options.speed))},s.prototype.animateSlide=function(e,t){var n={},i=this;i.animateHeight(),!0===i.options.rtl&&!1===i.options.vertical&&(e=-e),!1===i.transformsEnabled?!1===i.options.vertical?i.$slideTrack.animate({left:e},i.options.speed,i.options.easing,t):i.$slideTrack.animate({top:e},i.options.speed,i.options.easing,t):!1===i.cssTransitions?(!0===i.options.rtl&&(i.currentLeft=-i.currentLeft),c({animStart:i.currentLeft}).animate({animStart:e},{duration:i.options.speed,easing:i.options.easing,step:function(e){e=Math.ceil(e),!1===i.options.vertical?n[i.animType]="translate("+e+"px, 0px)":n[i.animType]="translate(0px,"+e+"px)",i.$slideTrack.css(n)},complete:function(){t&&t.call()}})):(i.applyTransition(),e=Math.ceil(e),!1===i.options.vertical?n[i.animType]="translate3d("+e+"px, 0px, 0px)":n[i.animType]="translate3d(0px,"+e+"px, 0px)",i.$slideTrack.css(n),t&&setTimeout(function(){i.disableTransition(),t.call()},i.options.speed))},s.prototype.getNavTarget=function(){var e=this.options.asNavFor;return e=e&&null!==e?c(e).not(this.$slider):e},s.prototype.asNavFor=function(t){var e=this.getNavTarget();null!==e&&"object"==typeof e&&e.each(function(){var e=c(this).slick("getSlick");e.unslicked||e.slideHandler(t,!0)})},s.prototype.applyTransition=function(e){var t=this,n={};!1===t.options.fade?n[t.transitionType]=t.transformType+" "+t.options.speed+"ms "+t.options.cssEase:n[t.transitionType]="opacity "+t.options.speed+"ms "+t.options.cssEase,(!1===t.options.fade?t.$slideTrack:t.$slides.eq(e)).css(n)},s.prototype.autoPlay=function(){var e=this;e.autoPlayClear(),e.slideCount>e.options.slidesToShow&&(e.autoPlayTimer=setInterval(e.autoPlayIterator,e.options.autoplaySpeed))},s.prototype.autoPlayClear=function(){this.autoPlayTimer&&clearInterval(this.autoPlayTimer)},s.prototype.autoPlayIterator=function(){var e=this,t=e.currentSlide+e.options.slidesToScroll;e.paused||e.interrupted||e.focussed||(!1===e.options.infinite&&(1===e.direction&&e.currentSlide+1===e.slideCount-1?e.direction=0:0===e.direction&&(t=e.currentSlide-e.options.slidesToScroll,e.currentSlide-1==0)&&(e.direction=1)),e.slideHandler(t))},s.prototype.buildArrows=function(){var e=this;!0===e.options.arrows&&(e.$prevArrow=c(e.options.prevArrow).addClass("slick-arrow"),e.$nextArrow=c(e.options.nextArrow).addClass("slick-arrow"),e.slideCount>e.options.slidesToShow?(e.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"),e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.prependTo(e.options.appendArrows),e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.appendTo(e.options.appendArrows),!0!==e.options.infinite&&e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true")):e.$prevArrow.add(e.$nextArrow).addClass("slick-hidden").attr({"aria-disabled":"true",tabindex:"-1"}))},s.prototype.buildDots=function(){var e,t,n=this;if(!0===n.options.dots&&n.slideCount>n.options.slidesToShow){for(n.$slider.addClass("slick-dotted"),t=c("<ul />").addClass(n.options.dotsClass),e=0;e<=n.getDotCount();e+=1)t.append(c("<li />").append(n.options.customPaging.call(this,n,e)));n.$dots=t.appendTo(n.options.appendDots),n.$dots.find("li").first().addClass("slick-active").attr("aria-hidden","false")}},s.prototype.buildOut=function(){var e=this;e.$slides=e.$slider.children(e.options.slide+":not(.slick-cloned)").addClass("slick-slide"),e.slideCount=e.$slides.length,e.$slides.each(function(e,t){c(t).attr("data-slick-index",e).data("originalStyling",c(t).attr("style")||"")}),e.$slider.addClass("slick-slider"),e.$slideTrack=0===e.slideCount?c('<div class="slick-track"/>').appendTo(e.$slider):e.$slides.wrapAll('<div class="slick-track"/>').parent(),e.$list=e.$slideTrack.wrap('<div aria-live="polite" class="slick-list"/>').parent(),e.$slideTrack.css("opacity",0),!0!==e.options.centerMode&&!0!==e.options.swipeToSlide||(e.options.slidesToScroll=1),c("img[data-lazy]",e.$slider).not("[src]").addClass("slick-loading"),e.setupInfinite(),e.buildArrows(),e.buildDots(),e.updateDots(),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),!0===e.options.draggable&&e.$list.addClass("draggable")},s.prototype.buildRows=function(){var e,t,n,i=this,o=document.createDocumentFragment(),r=i.$slider.children();if(1<i.options.rows){for(n=i.options.slidesPerRow*i.options.rows,t=Math.ceil(r.length/n),e=0;e<t;e++){for(var s=document.createElement("div"),a=0;a<i.options.rows;a++){for(var l=document.createElement("div"),c=0;c<i.options.slidesPerRow;c++){var d=e*n+(a*i.options.slidesPerRow+c);r.get(d)&&l.appendChild(r.get(d))}s.appendChild(l)}o.appendChild(s)}i.$slider.empty().append(o),i.$slider.children().children().children().css({width:100/i.options.slidesPerRow+"%",display:"inline-block"})}},s.prototype.checkResponsive=function(e,t){var n,i,o,r=this,s=!1,a=r.$slider.width(),l=window.innerWidth||c(window).width();if("window"===r.respondTo?o=l:"slider"===r.respondTo?o=a:"min"===r.respondTo&&(o=Math.min(l,a)),r.options.responsive&&r.options.responsive.length&&null!==r.options.responsive){for(n in i=null,r.breakpoints)r.breakpoints.hasOwnProperty(n)&&(!1===r.originalSettings.mobileFirst?o<r.breakpoints[n]&&(i=r.breakpoints[n]):o>r.breakpoints[n]&&(i=r.breakpoints[n]));null!==i?null!==r.activeBreakpoint&&i===r.activeBreakpoint&&!t||(r.activeBreakpoint=i,"unslick"===r.breakpointSettings[i]?r.unslick(i):(r.options=c.extend({},r.originalSettings,r.breakpointSettings[i]),!0===e&&(r.currentSlide=r.options.initialSlide),r.refresh(e)),s=i):null!==r.activeBreakpoint&&(r.activeBreakpoint=null,r.options=r.originalSettings,!0===e&&(r.currentSlide=r.options.initialSlide),r.refresh(e),s=i),e||!1===s||r.$slider.trigger("breakpoint",[r,s])}},s.prototype.changeSlide=function(e,t){var n,i=this,o=c(e.currentTarget);switch(o.is("a")&&e.preventDefault(),o.is("li")||(o=o.closest("li")),n=i.slideCount%i.options.slidesToScroll!=0?0:(i.slideCount-i.currentSlide)%i.options.slidesToScroll,e.data.message){case"previous":r=0==n?i.options.slidesToScroll:i.options.slidesToShow-n,i.slideCount>i.options.slidesToShow&&i.slideHandler(i.currentSlide-r,!1,t);break;case"next":r=0==n?i.options.slidesToScroll:n,i.slideCount>i.options.slidesToShow&&i.slideHandler(i.currentSlide+r,!1,t);break;case"index":var r=0===e.data.index?0:e.data.index||o.index()*i.options.slidesToScroll;i.slideHandler(i.checkNavigable(r),!1,t),o.children().trigger("focus");break;default:return}},s.prototype.checkNavigable=function(e){var t=this.getNavigableIndexes(),n=0;if(e>t[t.length-1])e=t[t.length-1];else for(var i in t){if(e<t[i]){e=n;break}n=t[i]}return e},s.prototype.cleanUpEvents=function(){var e=this;e.options.dots&&null!==e.$dots&&c("li",e.$dots).off("click.slick",e.changeSlide).off("mouseenter.slick",c.proxy(e.interrupt,e,!0)).off("mouseleave.slick",c.proxy(e.interrupt,e,!1)),e.$slider.off("focus.slick blur.slick"),!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow&&e.$prevArrow.off("click.slick",e.changeSlide),e.$nextArrow)&&e.$nextArrow.off("click.slick",e.changeSlide),e.$list.off("touchstart.slick mousedown.slick",e.swipeHandler),e.$list.off("touchmove.slick mousemove.slick",e.swipeHandler),e.$list.off("touchend.slick mouseup.slick",e.swipeHandler),e.$list.off("touchcancel.slick mouseleave.slick",e.swipeHandler),e.$list.off("click.slick",e.clickHandler),c(document).off(e.visibilityChange,e.visibility),e.cleanUpSlideEvents(),!0===e.options.accessibility&&e.$list.off("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&c(e.$slideTrack).children().off("click.slick",e.selectHandler),c(window).off("orientationchange.slick.slick-"+e.instanceUid,e.orientationChange),c(window).off("resize.slick.slick-"+e.instanceUid,e.resize),c("[draggable!=true]",e.$slideTrack).off("dragstart",e.preventDefault),c(window).off("load.slick.slick-"+e.instanceUid,e.setPosition),c(document).off("ready.slick.slick-"+e.instanceUid,e.setPosition)},s.prototype.cleanUpSlideEvents=function(){var e=this;e.$list.off("mouseenter.slick",c.proxy(e.interrupt,e,!0)),e.$list.off("mouseleave.slick",c.proxy(e.interrupt,e,!1))},s.prototype.cleanUpRows=function(){var e;1<this.options.rows&&((e=this.$slides.children().children()).removeAttr("style"),this.$slider.empty().append(e))},s.prototype.clickHandler=function(e){!1===this.shouldClick&&(e.stopImmediatePropagation(),e.stopPropagation(),e.preventDefault())},s.prototype.destroy=function(e){var t=this;t.autoPlayClear(),t.touchObject={},t.cleanUpEvents(),c(".slick-cloned",t.$slider).detach(),t.$dots&&t.$dots.remove(),t.$prevArrow&&t.$prevArrow.length&&(t.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.prevArrow))&&t.$prevArrow.remove(),t.$nextArrow&&t.$nextArrow.length&&(t.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display",""),t.htmlExpr.test(t.options.nextArrow))&&t.$nextArrow.remove(),t.$slides&&(t.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function(){c(this).attr("style",c(this).data("originalStyling"))}),t.$slideTrack.children(this.options.slide).detach(),t.$slideTrack.detach(),t.$list.detach(),t.$slider.append(t.$slides)),t.cleanUpRows(),t.$slider.removeClass("slick-slider"),t.$slider.removeClass("slick-initialized"),t.$slider.removeClass("slick-dotted"),t.unslicked=!0,e||t.$slider.trigger("destroy",[t])},s.prototype.disableTransition=function(e){var t={};t[this.transitionType]="",(!1===this.options.fade?this.$slideTrack:this.$slides.eq(e)).css(t)},s.prototype.fadeSlide=function(e,t){var n=this;!1===n.cssTransitions?(n.$slides.eq(e).css({zIndex:n.options.zIndex}),n.$slides.eq(e).animate({opacity:1},n.options.speed,n.options.easing,t)):(n.applyTransition(e),n.$slides.eq(e).css({opacity:1,zIndex:n.options.zIndex}),t&&setTimeout(function(){n.disableTransition(e),t.call()},n.options.speed))},s.prototype.fadeSlideOut=function(e){var t=this;!1===t.cssTransitions?t.$slides.eq(e).animate({opacity:0,zIndex:t.options.zIndex-2},t.options.speed,t.options.easing):(t.applyTransition(e),t.$slides.eq(e).css({opacity:0,zIndex:t.options.zIndex-2}))},s.prototype.filterSlides=s.prototype.slickFilter=function(e){var t=this;null!==e&&(t.$slidesCache=t.$slides,t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.filter(e).appendTo(t.$slideTrack),t.reinit())},s.prototype.focusHandler=function(){var n=this;n.$slider.off("focus.slick blur.slick").on("focus.slick blur.slick","*:not(.slick-arrow)",function(e){e.stopImmediatePropagation();var t=c(this);setTimeout(function(){n.options.pauseOnFocus&&(n.focussed=t.is(":focus"),n.autoPlay())},0)})},s.prototype.getCurrent=s.prototype.slickCurrentSlide=function(){return this.currentSlide},s.prototype.getDotCount=function(){var e=this,t=0,n=0,i=0;if(!0===e.options.infinite)for(;t<e.slideCount;)++i,t=n+e.options.slidesToScroll,n+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else if(!0===e.options.centerMode)i=e.slideCount;else if(e.options.asNavFor)for(;t<e.slideCount;)++i,t=n+e.options.slidesToScroll,n+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;else i=1+Math.ceil((e.slideCount-e.options.slidesToShow)/e.options.slidesToScroll);return i-1},s.prototype.getLeft=function(e){var t,n=this,i=0;return n.slideOffset=0,t=n.$slides.first().outerHeight(!0),!0===n.options.infinite?(n.slideCount>n.options.slidesToShow&&(n.slideOffset=n.slideWidth*n.options.slidesToShow*-1,i=t*n.options.slidesToShow*-1),n.slideCount%n.options.slidesToScroll!=0&&e+n.options.slidesToScroll>n.slideCount&&n.slideCount>n.options.slidesToShow&&(i=e>n.slideCount?(n.slideOffset=(n.options.slidesToShow-(e-n.slideCount))*n.slideWidth*-1,(n.options.slidesToShow-(e-n.slideCount))*t*-1):(n.slideOffset=n.slideCount%n.options.slidesToScroll*n.slideWidth*-1,n.slideCount%n.options.slidesToScroll*t*-1))):e+n.options.slidesToShow>n.slideCount&&(n.slideOffset=(e+n.options.slidesToShow-n.slideCount)*n.slideWidth,i=(e+n.options.slidesToShow-n.slideCount)*t),n.slideCount<=n.options.slidesToShow&&(i=n.slideOffset=0),!0===n.options.centerMode&&!0===n.options.infinite?n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)-n.slideWidth:!0===n.options.centerMode&&(n.slideOffset=0,n.slideOffset+=n.slideWidth*Math.floor(n.options.slidesToShow/2)),t=!1===n.options.vertical?e*n.slideWidth*-1+n.slideOffset:e*t*-1+i,!0===n.options.variableWidth&&(i=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(e):n.$slideTrack.children(".slick-slide").eq(e+n.options.slidesToShow),t=!0===n.options.rtl?i[0]?-1*(n.$slideTrack.width()-i[0].offsetLeft-i.width()):0:i[0]?-1*i[0].offsetLeft:0,!0===n.options.centerMode)&&(i=n.slideCount<=n.options.slidesToShow||!1===n.options.infinite?n.$slideTrack.children(".slick-slide").eq(e):n.$slideTrack.children(".slick-slide").eq(e+n.options.slidesToShow+1),t=!0===n.options.rtl?i[0]?-1*(n.$slideTrack.width()-i[0].offsetLeft-i.width()):0:i[0]?-1*i[0].offsetLeft:0,t+=(n.$list.width()-i.outerWidth())/2),t},s.prototype.getOption=s.prototype.slickGetOption=function(e){return this.options[e]},s.prototype.getNavigableIndexes=function(){for(var e=this,t=0,n=0,i=[],o=!1===e.options.infinite?e.slideCount:(t=-1*e.options.slidesToScroll,n=-1*e.options.slidesToScroll,2*e.slideCount);t<o;)i.push(t),t=n+e.options.slidesToScroll,n+=e.options.slidesToScroll<=e.options.slidesToShow?e.options.slidesToScroll:e.options.slidesToShow;return i},s.prototype.getSlick=function(){return this},s.prototype.getSlideCount=function(){var n,i=this,o=!0===i.options.centerMode?i.slideWidth*Math.floor(i.options.slidesToShow/2):0;return!0===i.options.swipeToSlide?(i.$slideTrack.find(".slick-slide").each(function(e,t){return t.offsetLeft-o+c(t).outerWidth()/2>-1*i.swipeLeft?(n=t,!1):void 0}),Math.abs(c(n).attr("data-slick-index")-i.currentSlide)||1):i.options.slidesToScroll},s.prototype.goTo=s.prototype.slickGoTo=function(e,t){this.changeSlide({data:{message:"index",index:parseInt(e)}},t)},s.prototype.init=function(e){var t=this;c(t.$slider).hasClass("slick-initialized")||(c(t.$slider).addClass("slick-initialized"),t.buildRows(),t.buildOut(),t.setProps(),t.startLoad(),t.loadSlider(),t.initializeEvents(),t.updateArrows(),t.updateDots(),t.checkResponsive(!0),t.focusHandler()),e&&t.$slider.trigger("init",[t]),!0===t.options.accessibility&&t.initADA(),t.options.autoplay&&(t.paused=!1,t.autoPlay())},s.prototype.initADA=function(){var t=this;t.$slides.add(t.$slideTrack.find(".slick-cloned")).attr({"aria-hidden":"true",tabindex:"-1"}).find("a, input, button, select").attr({tabindex:"-1"}),t.$slideTrack.attr("role","listbox"),t.$slides.not(t.$slideTrack.find(".slick-cloned")).each(function(e){c(this).attr({role:"option","aria-describedby":"slick-slide"+t.instanceUid+e})}),null!==t.$dots&&t.$dots.attr("role","tablist").find("li").each(function(e){c(this).attr({role:"presentation","aria-selected":"false","aria-controls":"navigation"+t.instanceUid+e,id:"slick-slide"+t.instanceUid+e})}).first().attr("aria-selected","true").end().find("button").attr("role","button").end().closest("div").attr("role","toolbar"),t.activateADA()},s.prototype.initArrowEvents=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.off("click.slick").on("click.slick",{message:"previous"},e.changeSlide),e.$nextArrow.off("click.slick").on("click.slick",{message:"next"},e.changeSlide))},s.prototype.initDotEvents=function(){var e=this;!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&c("li",e.$dots).on("click.slick",{message:"index"},e.changeSlide),!0===e.options.dots&&!0===e.options.pauseOnDotsHover&&c("li",e.$dots).on("mouseenter.slick",c.proxy(e.interrupt,e,!0)).on("mouseleave.slick",c.proxy(e.interrupt,e,!1))},s.prototype.initSlideEvents=function(){var e=this;e.options.pauseOnHover&&(e.$list.on("mouseenter.slick",c.proxy(e.interrupt,e,!0)),e.$list.on("mouseleave.slick",c.proxy(e.interrupt,e,!1)))},s.prototype.initializeEvents=function(){var e=this;e.initArrowEvents(),e.initDotEvents(),e.initSlideEvents(),e.$list.on("touchstart.slick mousedown.slick",{action:"start"},e.swipeHandler),e.$list.on("touchmove.slick mousemove.slick",{action:"move"},e.swipeHandler),e.$list.on("touchend.slick mouseup.slick",{action:"end"},e.swipeHandler),e.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},e.swipeHandler),e.$list.on("click.slick",e.clickHandler),c(document).on(e.visibilityChange,c.proxy(e.visibility,e)),!0===e.options.accessibility&&e.$list.on("keydown.slick",e.keyHandler),!0===e.options.focusOnSelect&&c(e.$slideTrack).children().on("click.slick",e.selectHandler),c(window).on("orientationchange.slick.slick-"+e.instanceUid,c.proxy(e.orientationChange,e)),c(window).on("resize.slick.slick-"+e.instanceUid,c.proxy(e.resize,e)),c("[draggable!=true]",e.$slideTrack).on("dragstart",e.preventDefault),c(window).on("load.slick.slick-"+e.instanceUid,e.setPosition),c(document).on("ready.slick.slick-"+e.instanceUid,e.setPosition)},s.prototype.initUI=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.show(),e.$nextArrow.show()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.show()},s.prototype.keyHandler=function(e){var t=this;e.target.tagName.match("TEXTAREA|INPUT|SELECT")||(37===e.keyCode&&!0===t.options.accessibility?t.changeSlide({data:{message:!0===t.options.rtl?"next":"previous"}}):39===e.keyCode&&!0===t.options.accessibility&&t.changeSlide({data:{message:!0===t.options.rtl?"previous":"next"}}))},s.prototype.lazyLoad=function(){function e(e){c("img[data-lazy]",e).each(function(){var e=c(this),t=c(this).attr("data-lazy"),n=document.createElement("img");n.onload=function(){e.animate({opacity:0},100,function(){e.attr("src",t).animate({opacity:1},200,function(){e.removeAttr("data-lazy").removeClass("slick-loading")}),i.$slider.trigger("lazyLoaded",[i,e,t])})},n.onerror=function(){e.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),i.$slider.trigger("lazyLoadError",[i,e,t])},n.src=t})}var t,n,i=this;!0===i.options.centerMode?n=!0===i.options.infinite?(t=i.currentSlide+(i.options.slidesToShow/2+1))+i.options.slidesToShow+2:(t=Math.max(0,i.currentSlide-(i.options.slidesToShow/2+1)),i.options.slidesToShow/2+1+2+i.currentSlide):(t=i.options.infinite?i.options.slidesToShow+i.currentSlide:i.currentSlide,n=Math.ceil(t+i.options.slidesToShow),!0===i.options.fade&&(0<t&&t--,n<=i.slideCount)&&n++),e(i.$slider.find(".slick-slide").slice(t,n)),i.slideCount<=i.options.slidesToShow?e(i.$slider.find(".slick-slide")):i.currentSlide>=i.slideCount-i.options.slidesToShow?e(i.$slider.find(".slick-cloned").slice(0,i.options.slidesToShow)):0===i.currentSlide&&e(i.$slider.find(".slick-cloned").slice(-1*i.options.slidesToShow))},s.prototype.loadSlider=function(){var e=this;e.setPosition(),e.$slideTrack.css({opacity:1}),e.$slider.removeClass("slick-loading"),e.initUI(),"progressive"===e.options.lazyLoad&&e.progressiveLazyLoad()},s.prototype.next=s.prototype.slickNext=function(){this.changeSlide({data:{message:"next"}})},s.prototype.orientationChange=function(){this.checkResponsive(),this.setPosition()},s.prototype.pause=s.prototype.slickPause=function(){this.autoPlayClear(),this.paused=!0},s.prototype.play=s.prototype.slickPlay=function(){var e=this;e.autoPlay(),e.options.autoplay=!0,e.paused=!1,e.focussed=!1,e.interrupted=!1},s.prototype.postSlide=function(e){var t=this;t.unslicked||(t.$slider.trigger("afterChange",[t,e]),t.animating=!1,t.setPosition(),t.swipeLeft=null,t.options.autoplay&&t.autoPlay(),!0===t.options.accessibility&&t.initADA())},s.prototype.prev=s.prototype.slickPrev=function(){this.changeSlide({data:{message:"previous"}})},s.prototype.preventDefault=function(e){e.preventDefault()},s.prototype.progressiveLazyLoad=function(e){e=e||1;var t,n,i=this,o=c("img[data-lazy]",i.$slider);o.length?(t=o.first(),n=t.attr("data-lazy"),(o=document.createElement("img")).onload=function(){t.attr("src",n).removeAttr("data-lazy").removeClass("slick-loading"),!0===i.options.adaptiveHeight&&i.setPosition(),i.$slider.trigger("lazyLoaded",[i,t,n]),i.progressiveLazyLoad()},o.onerror=function(){e<3?setTimeout(function(){i.progressiveLazyLoad(e+1)},500):(t.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"),i.$slider.trigger("lazyLoadError",[i,t,n]),i.progressiveLazyLoad())},o.src=n):i.$slider.trigger("allImagesLoaded",[i])},s.prototype.refresh=function(e){var t=this,n=t.slideCount-t.options.slidesToShow;!t.options.infinite&&t.currentSlide>n&&(t.currentSlide=n),t.slideCount<=t.options.slidesToShow&&(t.currentSlide=0),n=t.currentSlide,t.destroy(!0),c.extend(t,t.initials,{currentSlide:n}),t.init(),e||t.changeSlide({data:{message:"index",index:n}},!1)},s.prototype.registerBreakpoints=function(){var e,t,n,i=this,o=i.options.responsive||null;if("array"===c.type(o)&&o.length){for(e in i.respondTo=i.options.respondTo||"window",o)if(n=i.breakpoints.length-1,t=o[e].breakpoint,o.hasOwnProperty(e)){for(;0<=n;)i.breakpoints[n]&&i.breakpoints[n]===t&&i.breakpoints.splice(n,1),n--;i.breakpoints.push(t),i.breakpointSettings[t]=o[e].settings}i.breakpoints.sort(function(e,t){return i.options.mobileFirst?e-t:t-e})}},s.prototype.reinit=function(){var e=this;e.$slides=e.$slideTrack.children(e.options.slide).addClass("slick-slide"),e.slideCount=e.$slides.length,e.currentSlide>=e.slideCount&&0!==e.currentSlide&&(e.currentSlide=e.currentSlide-e.options.slidesToScroll),e.slideCount<=e.options.slidesToShow&&(e.currentSlide=0),e.registerBreakpoints(),e.setProps(),e.setupInfinite(),e.buildArrows(),e.updateArrows(),e.initArrowEvents(),e.buildDots(),e.updateDots(),e.initDotEvents(),e.cleanUpSlideEvents(),e.initSlideEvents(),e.checkResponsive(!1,!0),!0===e.options.focusOnSelect&&c(e.$slideTrack).children().on("click.slick",e.selectHandler),e.setSlideClasses("number"==typeof e.currentSlide?e.currentSlide:0),e.setPosition(),e.focusHandler(),e.paused=!e.options.autoplay,e.autoPlay(),e.$slider.trigger("reInit",[e])},s.prototype.resize=function(){var e=this;c(window).width()!==e.windowWidth&&(clearTimeout(e.windowDelay),e.windowDelay=window.setTimeout(function(){e.windowWidth=c(window).width(),e.checkResponsive(),e.unslicked||e.setPosition()},50))},s.prototype.removeSlide=s.prototype.slickRemove=function(e,t,n){var i=this;return e="boolean"==typeof e?!0===(t=e)?0:i.slideCount-1:!0===t?--e:e,!(i.slideCount<1||e<0||e>i.slideCount-1)&&(i.unload(),(!0===n?i.$slideTrack.children():i.$slideTrack.children(this.options.slide).eq(e)).remove(),i.$slides=i.$slideTrack.children(this.options.slide),i.$slideTrack.children(this.options.slide).detach(),i.$slideTrack.append(i.$slides),i.$slidesCache=i.$slides,void i.reinit())},s.prototype.setCSS=function(e){var t,n,i=this,o={};!0===i.options.rtl&&(e=-e),t="left"==i.positionProp?Math.ceil(e)+"px":"0px",n="top"==i.positionProp?Math.ceil(e)+"px":"0px",o[i.positionProp]=e,!1!==i.transformsEnabled&&(!(o={})===i.cssTransitions?o[i.animType]="translate("+t+", "+n+")":o[i.animType]="translate3d("+t+", "+n+", 0px)"),i.$slideTrack.css(o)},s.prototype.setDimensions=function(){var e=this,t=(!1===e.options.vertical?!0===e.options.centerMode&&e.$list.css({padding:"0px "+e.options.centerPadding}):(e.$list.height(e.$slides.first().outerHeight(!0)*e.options.slidesToShow),!0===e.options.centerMode&&e.$list.css({padding:e.options.centerPadding+" 0px"})),e.listWidth=e.$list.width(),e.listHeight=e.$list.height(),!1===e.options.vertical&&!1===e.options.variableWidth?(e.slideWidth=Math.ceil(e.listWidth/e.options.slidesToShow),e.$slideTrack.width(Math.ceil(e.slideWidth*e.$slideTrack.children(".slick-slide").length))):!0===e.options.variableWidth?e.$slideTrack.width(5e3*e.slideCount):(e.slideWidth=Math.ceil(e.listWidth),e.$slideTrack.height(Math.ceil(e.$slides.first().outerHeight(!0)*e.$slideTrack.children(".slick-slide").length))),e.$slides.first().outerWidth(!0)-e.$slides.first().width());!1===e.options.variableWidth&&e.$slideTrack.children(".slick-slide").width(e.slideWidth-t)},s.prototype.setFade=function(){var n,i=this;i.$slides.each(function(e,t){n=i.slideWidth*e*-1,!0===i.options.rtl?c(t).css({position:"relative",right:n,top:0,zIndex:i.options.zIndex-2,opacity:0}):c(t).css({position:"relative",left:n,top:0,zIndex:i.options.zIndex-2,opacity:0})}),i.$slides.eq(i.currentSlide).css({zIndex:i.options.zIndex-1,opacity:1})},s.prototype.setHeight=function(){var e,t=this;1===t.options.slidesToShow&&!0===t.options.adaptiveHeight&&!1===t.options.vertical&&(e=t.$slides.eq(t.currentSlide).outerHeight(!0),t.$list.css("height",e))},s.prototype.setOption=s.prototype.slickSetOption=function(){var e,t,n,i,o,r=this,s=!1;if("object"===c.type(arguments[0])?(n=arguments[0],s=arguments[1],o="multiple"):"string"===c.type(arguments[0])&&(n=arguments[0],i=arguments[1],s=arguments[2],"responsive"===arguments[0]&&"array"===c.type(arguments[1])?o="responsive":void 0!==arguments[1]&&(o="single")),"single"===o)r.options[n]=i;else if("multiple"===o)c.each(n,function(e,t){r.options[e]=t});else if("responsive"===o)for(t in i)if("array"!==c.type(r.options.responsive))r.options.responsive=[i[t]];else{for(e=r.options.responsive.length-1;0<=e;)r.options.responsive[e].breakpoint===i[t].breakpoint&&r.options.responsive.splice(e,1),e--;r.options.responsive.push(i[t])}s&&(r.unload(),r.reinit())},s.prototype.setPosition=function(){var e=this;e.setDimensions(),e.setHeight(),!1===e.options.fade?e.setCSS(e.getLeft(e.currentSlide)):e.setFade(),e.$slider.trigger("setPosition",[e])},s.prototype.setProps=function(){var e=this,t=document.body.style;e.positionProp=!0===e.options.vertical?"top":"left","top"===e.positionProp?e.$slider.addClass("slick-vertical"):e.$slider.removeClass("slick-vertical"),void 0===t.WebkitTransition&&void 0===t.MozTransition&&void 0===t.msTransition||!0!==e.options.useCSS||(e.cssTransitions=!0),e.options.fade&&("number"==typeof e.options.zIndex?e.options.zIndex<3&&(e.options.zIndex=3):e.options.zIndex=e.defaults.zIndex),void 0!==t.OTransform&&(e.animType="OTransform",e.transformType="-o-transform",e.transitionType="OTransition",void 0===t.perspectiveProperty)&&void 0===t.webkitPerspective&&(e.animType=!1),void 0!==t.MozTransform&&(e.animType="MozTransform",e.transformType="-moz-transform",e.transitionType="MozTransition",void 0===t.perspectiveProperty)&&void 0===t.MozPerspective&&(e.animType=!1),void 0!==t.webkitTransform&&(e.animType="webkitTransform",e.transformType="-webkit-transform",e.transitionType="webkitTransition",void 0===t.perspectiveProperty)&&void 0===t.webkitPerspective&&(e.animType=!1),void 0!==t.msTransform&&(e.animType="msTransform",e.transformType="-ms-transform",e.transitionType="msTransition",void 0===t.msTransform)&&(e.animType=!1),void 0!==t.transform&&!1!==e.animType&&(e.animType="transform",e.transformType="transform",e.transitionType="transition"),e.transformsEnabled=e.options.useTransform&&null!==e.animType&&!1!==e.animType},s.prototype.setSlideClasses=function(e){var t,n,i=this,o=i.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden","true");i.$slides.eq(e).addClass("slick-current"),!0===i.options.centerMode?(n=Math.floor(i.options.slidesToShow/2),!0===i.options.infinite&&((n<=e&&e<=i.slideCount-1-n?i.$slides.slice(e-n,e+n+1):(t=i.options.slidesToShow+e,o.slice(t-n+1,t+n+2))).addClass("slick-active").attr("aria-hidden","false"),0===e?o.eq(o.length-1-i.options.slidesToShow).addClass("slick-center"):e===i.slideCount-1&&o.eq(i.options.slidesToShow).addClass("slick-center")),i.$slides.eq(e).addClass("slick-center")):(0<=e&&e<=i.slideCount-i.options.slidesToShow?i.$slides.slice(e,e+i.options.slidesToShow):o.length<=i.options.slidesToShow?o:(n=i.slideCount%i.options.slidesToShow,t=!0===i.options.infinite?i.options.slidesToShow+e:e,i.options.slidesToShow==i.options.slidesToScroll&&i.slideCount-e<i.options.slidesToShow?o.slice(t-(i.options.slidesToShow-n),t+n):o.slice(t,t+i.options.slidesToShow))).addClass("slick-active").attr("aria-hidden","false"),"ondemand"===i.options.lazyLoad&&i.lazyLoad()},s.prototype.setupInfinite=function(){var e,t,n,i=this;if(!0===i.options.fade&&(i.options.centerMode=!1),!0===i.options.infinite&&!1===i.options.fade&&(t=null,i.slideCount>i.options.slidesToShow)){for(n=!0===i.options.centerMode?i.options.slidesToShow+1:i.options.slidesToShow,e=i.slideCount;e>i.slideCount-n;--e)c(i.$slides[t=e-1]).clone(!0).attr("id","").attr("data-slick-index",t-i.slideCount).prependTo(i.$slideTrack).addClass("slick-cloned");for(e=0;e<n;e+=1)t=e,c(i.$slides[t]).clone(!0).attr("id","").attr("data-slick-index",t+i.slideCount).appendTo(i.$slideTrack).addClass("slick-cloned");i.$slideTrack.find(".slick-cloned").find("[id]").each(function(){c(this).attr("id","")})}},s.prototype.interrupt=function(e){e||this.autoPlay(),this.interrupted=e},s.prototype.selectHandler=function(e){var t=this,e=c(e.target).is(".slick-slide")?c(e.target):c(e.target).parents(".slick-slide"),e=(e=parseInt(e.attr("data-slick-index")))||0;return t.slideCount<=t.options.slidesToShow?(t.setSlideClasses(e),void t.asNavFor(e)):void t.slideHandler(e)},s.prototype.slideHandler=function(e,t,n){var i,o,r,s=this;return t=t||!1,!0===s.animating&&!0===s.options.waitForAnimate||!0===s.options.fade&&s.currentSlide===e||s.slideCount<=s.options.slidesToShow?void 0:(!1===t&&s.asNavFor(e),i=e,t=s.getLeft(i),r=s.getLeft(s.currentSlide),s.currentLeft=null===s.swipeLeft?r:s.swipeLeft,!1===s.options.infinite&&!1===s.options.centerMode&&(e<0||e>s.getDotCount()*s.options.slidesToScroll)||!1===s.options.infinite&&!0===s.options.centerMode&&(e<0||e>s.slideCount-s.options.slidesToScroll)?void(!1===s.options.fade&&(i=s.currentSlide,!0!==n?s.animateSlide(r,function(){s.postSlide(i)}):s.postSlide(i))):(s.options.autoplay&&clearInterval(s.autoPlayTimer),o=i<0?s.slideCount%s.options.slidesToScroll!=0?s.slideCount-s.slideCount%s.options.slidesToScroll:s.slideCount+i:i>=s.slideCount?s.slideCount%s.options.slidesToScroll!=0?0:i-s.slideCount:i,s.animating=!0,s.$slider.trigger("beforeChange",[s,s.currentSlide,o]),e=s.currentSlide,s.currentSlide=o,s.setSlideClasses(s.currentSlide),s.options.asNavFor&&(r=(r=s.getNavTarget()).slick("getSlick")).slideCount<=r.options.slidesToShow&&r.setSlideClasses(s.currentSlide),s.updateDots(),s.updateArrows(),!0===s.options.fade?(!0!==n?(s.fadeSlideOut(e),s.fadeSlide(o,function(){s.postSlide(o)})):s.postSlide(o),void s.animateHeight()):void(!0!==n?s.animateSlide(t,function(){s.postSlide(o)}):s.postSlide(o))))},s.prototype.startLoad=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.hide(),e.$nextArrow.hide()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.hide(),e.$slider.addClass("slick-loading")},s.prototype.swipeDirection=function(){var e=this,t=e.touchObject.startX-e.touchObject.curX,n=e.touchObject.startY-e.touchObject.curY,n=Math.atan2(n,t),t=Math.round(180*n/Math.PI);return(t=t<0?360-Math.abs(t):t)<=45&&0<=t||t<=360&&315<=t?!1===e.options.rtl?"left":"right":135<=t&&t<=225?!1===e.options.rtl?"right":"left":!0===e.options.verticalSwiping?35<=t&&t<=135?"down":"up":"vertical"},s.prototype.swipeEnd=function(e){var t,n,i=this;if(i.dragging=!1,i.interrupted=!1,i.shouldClick=!(10<i.touchObject.swipeLength),void 0===i.touchObject.curX)return!1;if(!0===i.touchObject.edgeHit&&i.$slider.trigger("edge",[i,i.swipeDirection()]),i.touchObject.swipeLength>=i.touchObject.minSwipe){switch(n=i.swipeDirection()){case"left":case"down":t=i.options.swipeToSlide?i.checkNavigable(i.currentSlide+i.getSlideCount()):i.currentSlide+i.getSlideCount(),i.currentDirection=0;break;case"right":case"up":t=i.options.swipeToSlide?i.checkNavigable(i.currentSlide-i.getSlideCount()):i.currentSlide-i.getSlideCount(),i.currentDirection=1}"vertical"!=n&&(i.slideHandler(t),i.touchObject={},i.$slider.trigger("swipe",[i,n]))}else i.touchObject.startX!==i.touchObject.curX&&(i.slideHandler(i.currentSlide),i.touchObject={})},s.prototype.swipeHandler=function(e){var t=this;if(!(!1===t.options.swipe||"ontouchend"in document&&!1===t.options.swipe||!1===t.options.draggable&&-1!==e.type.indexOf("mouse")))switch(t.touchObject.fingerCount=e.originalEvent&&void 0!==e.originalEvent.touches?e.originalEvent.touches.length:1,t.touchObject.minSwipe=t.listWidth/t.options.touchThreshold,!0===t.options.verticalSwiping&&(t.touchObject.minSwipe=t.listHeight/t.options.touchThreshold),e.data.action){case"start":t.swipeStart(e);break;case"move":t.swipeMove(e);break;case"end":t.swipeEnd(e)}},s.prototype.swipeMove=function(e){var t,n,i=this,o=void 0!==e.originalEvent?e.originalEvent.touches:null;return!(!i.dragging||o&&1!==o.length)&&(t=i.getLeft(i.currentSlide),i.touchObject.curX=void 0!==o?o[0].pageX:e.clientX,i.touchObject.curY=void 0!==o?o[0].pageY:e.clientY,i.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(i.touchObject.curX-i.touchObject.startX,2))),!0===i.options.verticalSwiping&&(i.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(i.touchObject.curY-i.touchObject.startY,2)))),"vertical"!==(o=i.swipeDirection())?(void 0!==e.originalEvent&&4<i.touchObject.swipeLength&&e.preventDefault(),e=(!1===i.options.rtl?1:-1)*(i.touchObject.curX>i.touchObject.startX?1:-1),!0===i.options.verticalSwiping&&(e=i.touchObject.curY>i.touchObject.startY?1:-1),n=i.touchObject.swipeLength,(i.touchObject.edgeHit=!1)===i.options.infinite&&(0===i.currentSlide&&"right"===o||i.currentSlide>=i.getDotCount()&&"left"===o)&&(n=i.touchObject.swipeLength*i.options.edgeFriction,i.touchObject.edgeHit=!0),!1===i.options.vertical?i.swipeLeft=t+n*e:i.swipeLeft=t+n*(i.$list.height()/i.listWidth)*e,!0===i.options.verticalSwiping&&(i.swipeLeft=t+n*e),!0!==i.options.fade&&!1!==i.options.touchMove&&(!0===i.animating?(i.swipeLeft=null,!1):void i.setCSS(i.swipeLeft))):void 0)},s.prototype.swipeStart=function(e){var t,n=this;return n.interrupted=!0,1!==n.touchObject.fingerCount||n.slideCount<=n.options.slidesToShow?!(n.touchObject={}):(void 0!==e.originalEvent&&void 0!==e.originalEvent.touches&&(t=e.originalEvent.touches[0]),n.touchObject.startX=n.touchObject.curX=void 0!==t?t.pageX:e.clientX,n.touchObject.startY=n.touchObject.curY=void 0!==t?t.pageY:e.clientY,void(n.dragging=!0))},s.prototype.unfilterSlides=s.prototype.slickUnfilter=function(){var e=this;null!==e.$slidesCache&&(e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.appendTo(e.$slideTrack),e.reinit())},s.prototype.unload=function(){var e=this;c(".slick-cloned",e.$slider).remove(),e.$dots&&e.$dots.remove(),e.$prevArrow&&e.htmlExpr.test(e.options.prevArrow)&&e.$prevArrow.remove(),e.$nextArrow&&e.htmlExpr.test(e.options.nextArrow)&&e.$nextArrow.remove(),e.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden","true").css("width","")},s.prototype.unslick=function(e){this.$slider.trigger("unslick",[this,e]),this.destroy()},s.prototype.updateArrows=function(){var e=this;Math.floor(e.options.slidesToShow/2);!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&!e.options.infinite&&(e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false"),0===e.currentSlide?(e.$prevArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled","false")):(e.currentSlide>=e.slideCount-e.options.slidesToShow&&!1===e.options.centerMode||e.currentSlide>=e.slideCount-1&&!0===e.options.centerMode)&&(e.$nextArrow.addClass("slick-disabled").attr("aria-disabled","true"),e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled","false")))},s.prototype.updateDots=function(){var e=this;null!==e.$dots&&(e.$dots.find("li").removeClass("slick-active").attr("aria-hidden","true"),e.$dots.find("li").eq(Math.floor(e.currentSlide/e.options.slidesToScroll)).addClass("slick-active").attr("aria-hidden","false"))},s.prototype.visibility=function(){this.options.autoplay&&(document[this.hidden]?this.interrupted=!0:this.interrupted=!1)},c.fn.slick=function(){for(var e,t=this,n=arguments[0],i=Array.prototype.slice.call(arguments,1),o=t.length,r=0;r<o;r++)if("object"==typeof n||void 0===n?t[r].slick=new s(t[r],n):e=t[r].slick[n].apply(t[r].slick,i),void 0!==e)return e;return t}}),(()=>{var i={d:function(e,t){for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r:function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},s={};function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}i.r(s),i.d(s,{CREATED:function(){return c},DESTROYED:function(){return te},IDLE:function(){return d},MOUNTED:function(){return ee},MOVING:function(){return O}});var W=Object.keys;function y(n,i){W(n).some(function(e,t){return i(n[e],e,t)})}function R(t){return W(t).map(function(e){return t[e]})}function p(e){return"object"==typeof e}function a(e,t){var n=r({},e);return y(t,function(e,t){p(e)?(p(n[t])||(n[t]={}),n[t]=a(n[t],e)):n[t]=e}),n}function o(e){return Array.isArray(e)?e:[e]}function b(e,t,n){return Math.min(Math.max(e,n<t?n:t),n<t?t:n)}function F(e,t){var n=0;return e.replace(/%s/g,function(){return o(t)[n++]})}function w(e){var t=typeof e;return"number"==t&&0<e?parseFloat(e)+"px":"string"==t?e:""}function B(e){return e<10?"0"+e:e}function k(e,t){var n;return"string"==typeof t&&(x(n=f("div",{}),{position:"absolute",width:t}),m(e,n),t=n.clientWidth,g(n)),+t||0}function l(e,t){return e?e.querySelector(t.split(" ")[0]):null}function h(e,t){return U(e,t)[0]}function U(e,t){return e?R(e.children).filter(function(e){return G(e,t.split(" ")[0])||e.tagName===t}):[]}function f(e,t){var n=document.createElement(e);return y(t,function(e,t){return T(n,t,e)}),n}function X(e){var t=f("div",{});return t.innerHTML=e,t.firstChild}function g(e){o(e).forEach(function(e){var t;e&&(t=e.parentElement)&&t.removeChild(e)})}function m(e,t){e&&e.appendChild(t)}function Y(e,t){var n;e&&t&&(n=t.parentElement)&&n.insertBefore(e,t)}function x(n,e){n&&y(e,function(e,t){null!==e&&(n.style[t]=e)})}function n(t,e,n){t&&o(e).forEach(function(e){e&&t.classList[n?"remove":"add"](e)})}function v(e,t){n(e,t,!1)}function $(e,t){n(e,t,!0)}function G(e,t){return!!e&&e.classList.contains(t)}function T(e,t,n){e&&e.setAttribute(t,n)}function C(e,t){return e?e.getAttribute(t):""}function _(e,t){o(t).forEach(function(t){o(e).forEach(function(e){return e&&e.removeAttribute(t)})})}function S(e){return e.getBoundingClientRect()}function V(l,c){var d,u;return{mount:function(){d=c.Elements.list,l.on("transitionend",function(e){e.target===d&&u&&u()},d)},start:function(e,t,n,i,o){var r=l.options,s=c.Controller.edgeIndex,a=r.speed;u=o,l.is(j)&&(0===n&&s<=t||s<=n&&0===t)&&(a=r.rewindSpeed||a),x(d,{transition:"transform "+a+"ms "+r.easing,transform:"translate("+i.x+"px,"+i.y+"px)"})}}}function Q(n,s){function a(e){var t=n.options;x(s.Elements.slides[e],{transition:"opacity "+t.speed+"ms "+t.easing})}return{mount:function(){a(n.index)},start:function(e,t,n,i,o){var r=s.Elements.track;x(r,{height:w(r.clientHeight)}),a(t),setTimeout(function(){o(),x(r,{height:""})})}}}var j="slide",E="loop",A="fade";function K(e){console.error("[SPLIDE] "+e)}function J(e,t){if(!e)throw new Error(t)}var D={active:"is-active",visible:"is-visible",loading:"is-loading"},Z={type:"slide",rewind:!1,speed:400,rewindSpeed:0,waitForTransition:!0,width:0,height:0,fixedWidth:0,fixedHeight:0,heightRatio:0,autoWidth:!1,autoHeight:!1,perPage:1,perMove:0,clones:0,start:0,focus:!1,gap:0,padding:0,arrows:!0,arrowPath:"",pagination:!0,autoplay:!1,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,lazyLoad:!1,preloadPages:1,easing:"cubic-bezier(.42,.65,.27,.99)",keyboard:"global",drag:!0,dragAngleThreshold:30,swipeDistanceThreshold:150,flickVelocityThreshold:.6,flickPower:600,flickMaxPages:1,direction:"ltr",cover:!1,accessibility:!0,slideFocus:!0,isNavigation:!1,trimSpace:!0,updateOnMove:!1,throttle:100,destroy:!1,breakpoints:!1,classes:{root:e="splide",slider:e+"__slider",track:e+"__track",list:e+"__list",slide:e+"__slide",container:e+"__slide__container",arrows:e+"__arrows",arrow:e+"__arrow",prev:e+"__arrow--prev",next:e+"__arrow--next",pagination:e+"__pagination",page:e+"__pagination__page",clone:e+"__slide--clone",progress:e+"__progress",bar:e+"__progress__bar",autoplay:e+"__autoplay",play:e+"__play",pause:e+"__pause",spinner:e+"__spinner",sr:e+"__sr"},i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay"}},c=1,ee=2,d=3,O=4,te=5;function t(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(e=oe.prototype).mount=function(e,t){var n,i,o,r=this;void 0===e&&(e=this._e),void 0===t&&(t=this._t),this.State.set(c),this._e=e,this._t=t,this.Components=(i=a((n=this)._c,e),t=t,o={},y(i,function(e,t){o[t]=e(n,o,t.toLowerCase())}),t=t||(n.is(A)?Q:V),o.Transition=t(n,o),o);try{y(this.Components,function(e,t){var n=e.required;void 0===n||n?e.mount&&e.mount():delete r.Components[t]})}catch(e){return void K(e.message)}var s=this.State;return s.set(ee),y(this.Components,function(e){e.mounted&&e.mounted()}),this.emit("mounted"),s.set(d),this.emit("ready"),x(this.root,{visibility:"visible"}),this.on("move drag",function(){return s.set(O)}).on("moved dragged",function(){return s.set(d)}),this},e.sync=function(e){return this.sibling=e,this},e.on=function(e,t,n,i){return this.Event.on(e,t,n=void 0===n?null:n,i=void 0===i?{}:i),this},e.off=function(e,t){return this.Event.off(e,t=void 0===t?null:t),this},e.emit=function(e){for(var t,n=arguments.length,i=new Array(1<n?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return(t=this.Event).emit.apply(t,[e].concat(i)),this},e.go=function(e,t){return void 0===t&&(t=this.options.waitForTransition),(this.State.is(d)||this.State.is(O)&&!t)&&this.Components.Controller.go(e,!1),this},e.is=function(e){return e===this._o.type},e.add=function(e,t){return this.Components.Elements.add(e,t=void 0===t?-1:t,this.refresh.bind(this)),this},e.remove=function(e){return this.Components.Elements.remove(e),this.refresh(),this},e.refresh=function(){return this.emit("refresh:before").emit("refresh").emit("resize"),this},e.destroy=function(t){var e=this;if(void 0===t&&(t=!0),!this.State.is(c))return R(this.Components).reverse().forEach(function(e){e.destroy&&e.destroy(t)}),this.emit("destroy",t),this.Event.destroy(),this.State.set(te),this;this.on("ready",function(){return e.destroy(t)})},t(oe.prototype,[{key:"index",get:function(){return this._i},set:function(e){this._i=parseInt(e)}},{key:"length",get:function(){return this.Components.Elements.length}},{key:"options",get:function(){return this._o},set:function(e){var t=this.State.is(c);t||this.emit("update"),this._o=a(this._o,e),t||this.emit("updated",this._o)}},{key:"classes",get:function(){return this._o.classes}},{key:"i18n",get:function(){return this._o.i18n}}]);var L="rtl",N="ttb",ne="update.slide",ie=Math.floor,P=Math.abs;function oe(e,t,n){var i,o;function r(e){e.elm&&e.elm.removeEventListener(e.event,e.handler,e.options)}void 0===t&&(t={}),void 0===n&&(n={}),this.root=e instanceof Element?e:document.querySelector(e),J(this.root,"An invalid element/selector was given."),this.Components=null,this.Event=(o=[],{on:function(e,t,n,i){void 0===n&&(n=null),void 0===i&&(i={}),e.split(" ").forEach(function(e){n&&n.addEventListener(e,t,i),o.push({event:e,handler:t,elm:n,options:i})})},off:function(e,n){void 0===n&&(n=null),e.split(" ").forEach(function(t){o=o.filter(function(e){return!e||e.event!==t||e.elm!==n||(r(e),!1)})})},emit:function(t){for(var e=arguments.length,n=new Array(1<e?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];o.forEach(function(e){e.elm||e.event.split(".")[0]!==t||e.handler.apply(e,n)})},destroy:function(){o.forEach(r),o=[]}}),this.State=(i=c,{set:function(e){i=e},is:function(e){return e===i}}),this.STATES=s,this._o=a(Z,t),this._i=0,this._c=n,this._e={},this._t=null}function re(e,t){var n;return function(){n=n||setTimeout(function(){e(),n=null},t)}}var se,ae,H=Math.abs,le="move.page",ce="updated.page refresh.page",de="data-splide-lazy-srcset",I="aria-current",M="aria-controls",q="aria-label",ue="aria-hidden",z="tabindex",pe={ltr:{ArrowLeft:"<",ArrowRight:">",Left:"<",Right:">"},rtl:{ArrowLeft:">",ArrowRight:"<",Left:">",Right:"<"},ttb:{ArrowUp:"<",ArrowDown:">",Up:"<",Down:">"}},u="move.sync",fe="mouseup touchend",he=[" ","Enter","Spacebar"],ge={Options:function(e){var t=C(e.root,"data-splide");if(t)try{e.options=JSON.parse(t)}catch(e){K(e.message)}return{mount:function(){e.State.is(c)&&(e.index=e.options.start)}}},Breakpoints:function(i){var o,r,s=i.options.breakpoints,t=re(e,50),a=[];function e(){var e,t,n=(n=a.filter(function(e){return e.mql.matches})[0])?n.point:-1;n!==r&&(r=n,e=i.State,(t=(n=s[n]||o).destroy)?(i.options=o,i.destroy("completely"===t)):(e.is(te)&&i.mount(),i.options=n))}return{required:s&&matchMedia,mount:function(){a=Object.keys(s).sort(function(e,t){return+e-+t}).map(function(e){return{point:e,mql:matchMedia("(max-width:"+e+"px)")}}),this.destroy(!0),addEventListener("resize",t),o=i.options,e()},destroy:function(e){e&&removeEventListener("resize",t)}}},Controller:function(l,n){var c,i,d={mount:function(){c=l.options,i=l.is(E),l.on("move",function(e){l.index=e}).on("updated refresh",function(e){c=e||c,l.index=b(l.index,0,d.edgeIndex)})},go:function(e,t){e=this.trim(this.parse(e));n.Track.go(e,this.rewind(e),t)},parse:function(e){var t,n,i,o=l.index,r=String(e).match(/([+\-<>]+)(\d+)?/),s=r?r[1]:"",a=r?parseInt(r[2]):0;switch(s){case"+":o+=a||1;break;case"-":o-=a||1;break;case">":case"<":t=o,n="<"===s,o=-1<a?d.toIndex(a):(n=n?-1:1,(i=c.perMove)?t+i*n:d.toIndex(d.toPage(t)+n));break;default:o=parseInt(e)}return o},toIndex:function(e){var t,n;return o()?e:(t=l.length,n=e*(e=c.perPage),t-e<=(n-=(this.pageLength*e-t)*ie(n/t))&&n<t?t-e:n)},toPage:function(e){var t,n;return o()?e:(t=l.length,n=c.perPage,ie(t-n<=e&&e<t?(t-1)/n:e/n))},trim:function(e){return e=i?e:c.rewind?this.rewind(e):b(e,0,this.edgeIndex)},rewind:function(e){var t=this.edgeIndex;if(i){for(;t<e;)e-=t+1;for(;e<0;)e+=t+1}else t<e?e=0:e<0&&(e=t);return e},isRtl:function(){return c.direction===L},get pageLength(){var e=l.length;return o()?e:Math.ceil(e/c.perPage)},get edgeIndex(){var e=l.length;return e?o()||c.isNavigation||i?e-1:e-c.perPage:0},get prevIndex(){var e=l.index-1;return-1<(e=i||c.rewind?this.rewind(e):e)?e:-1},get nextIndex(){var e=l.index+1;return(i||c.rewind)&&(e=this.rewind(e)),l.index<e&&e<=this.edgeIndex||0===e?e:-1}};function o(){return!1!==c.focus}return d},Elements:function(p,i){var e,t=p.root,o=p.classes,f=[],r=(t.id||(window.splide=window.splide||{},e=window.splide.uid||0,window.splide.uid=++e,t.id="splide"+B(e)),{mount:function(){var e=this;this.init(),p.on("refresh",function(){e.destroy(),e.init()}).on("updated",function(){$(t,s()),v(t,s())})},destroy:function(){f.forEach(function(e){e.destroy()}),f=[],$(t,s())},init:function(){var e,n=this;r.slider=h(t,o.slider),r.track=l(t,"."+o.track),r.list=h(r.track,o.list),J(r.track&&r.list,"Track or list was not found."),r.slides=U(r.list,o.slide),e=a(o.arrows),r.arrows={prev:l(e,"."+o.prev),next:l(e,"."+o.next)},e=a(o.autoplay),r.bar=l(a(o.progress),"."+o.bar),r.play=l(e,"."+o.play),r.pause=l(e,"."+o.pause),r.track.id=r.track.id||t.id+"-track",r.list.id=r.list.id||t.id+"-list",v(t,s()),this.slides.forEach(function(e,t){n.register(e,t,-1)})},register:function(e,t,n){i=t,r=n,s=e,a=(o=p).options.updateOnMove,l="ready.slide updated.slide resized.slide moved.slide"+(a?" move.slide":"");var o,i,r,s,a,l,c,t=c={slide:s,index:i,realIndex:r,container:h(s,o.classes.container),isClone:-1<r,mount:function(){var e=this;this.isClone||(s.id=o.root.id+"-slide"+B(i+1)),o.on(l,function(){return e.update()}).on(ne,u).on("click",function(){return o.emit("click",e)},s),a&&o.on("move.slide",function(e){e===r&&d(!0,!1)}),x(s,{display:""}),this.styles=C(s,"style")||""},destroy:function(){o.off(l).off(ne).off("click",s),$(s,R(D)),u(),_(this.container,"style")},update:function(){d(this.isActive(),!1),d(this.isVisible(),!0)},isActive:function(){return o.index===i},isVisible:function(){var e,t,n=this.isActive();return o.is(A)||n?n:(n=Math.ceil,e=S(o.Components.Elements.track),t=S(s),o.options.direction===N?e.top<=t.top&&t.bottom<=n(e.bottom):e.left<=t.left&&t.right<=n(e.right))},isWithin:function(e,t){e=Math.abs(e-i);return(e=o.is(j)||this.isClone?e:Math.min(e,o.length-e))<t}};function d(e,t){var n=t?"visible":"active",i=D[n];e?(v(s,i),o.emit(n,c)):G(s,i)&&($(s,i),o.emit(t?"hidden":"inactive",c))}function u(){T(s,"style",c.styles)}t.mount(),f.push(t)},getSlide:function(t){return f.filter(function(e){return e.index===t})[0]},getSlides:function(e){return e?f:f.filter(function(e){return!e.isClone})},getSlidesByPage:function(e){var t=i.Controller.toIndex(e),e=p.options,n=!1!==e.focus?1:e.perPage;return f.filter(function(e){e=e.index;return t<=e&&e<t+n})},add:function(e,t,n){var i,o,r,s;(e="string"==typeof e?X(e):e)instanceof Element&&(r=this.slides[t],x(e,{display:"none"}),r?(Y(e,r),this.slides.splice(t,0,e)):(m(this.list,e),this.slides.push(e)),i=function(){n&&n(e)},r=e.querySelectorAll("img"),(s=r.length)?(o=0,y(r,function(e){e.onload=e.onerror=function(){++o===s&&i()}})):i())},remove:function(e){g(this.slides.splice(e,1)[0])},each:function(e){f.forEach(e)},get length(){return this.slides.length},get total(){return f.length}});function s(){var e=o.root,t=p.options;return[e+"--"+t.type,e+"--"+t.direction,t.drag?e+"--draggable":"",t.isNavigation?e+"--nav":"",D.active]}function a(e){return h(t,e)||h(r.slider,e)}return r},Track:function(r,s){var n,t,o,i=r.options.direction===N,a=r.is(A),l=r.options.direction===L,c=!1,d=l?1:-1,u={sign:d,mount:function(){t=s.Elements,n=s.Layout,o=t.list},mounted:function(){var e=this;a||(this.jump(0),r.on("mounted resize updated",function(){e.jump(r.index)}))},go:function(e,t,n){var i=f(e),o=r.index;r.State.is(O)&&c||(c=e!==t,n||r.emit("move",t,o,e),1<=Math.abs(i-this.position)||a?s.Transition.start(e,t,o,this.toCoord(i),function(){p(e,t,o,n)}):e!==o&&"move"===r.options.trimSpace?s.Controller.go(e+e-o,n):p(e,t,o,n))},jump:function(e){this.translate(f(e))},translate:function(e){x(o,{transform:"translate"+(i?"Y":"X")+"("+e+"px)"})},cancel:function(){r.is(E)?this.shift():this.translate(this.position),x(o,{transition:""})},shift:function(){var e=P(this.position),t=P(this.toPosition(0)),n=P(this.toPosition(r.length)),i=n-t;e<t?e+=i:n<e&&(e-=i),this.translate(d*e)},trim:function(e){return!r.options.trimSpace||r.is(E)?e:b(e,d*(n.totalSize()-n.size-n.gap),0)},toIndex:function(n){var i=this,o=0,r=1/0;return t.getSlides(!0).forEach(function(e){var e=e.index,t=P(i.toPosition(e)-n);t<r&&(r=t,o=e)}),o},toCoord:function(e){return{x:i?0:e,y:i?e:0}},toPosition:function(e){var t=n.totalSize(e)-n.slideSize(e)-n.gap;return d*(t+this.offset(e))},offset:function(e){var t=r.options.focus,e=n.slideSize(e);return"center"===t?-(n.size-e)/2:-(parseInt(t)||0)*(e+n.gap)},get position(){var e=i?"top":l?"right":"left";return S(o)[e]-(S(t.track)[e]-n.padding[e]*d)}};function p(e,t,n,i){x(o,{transition:""}),c=!1,a||u.jump(t),i||r.emit("moved",t,n,e)}function f(e){return u.trim(u.toPosition(e))}return u},Clones:function(o,e){var s=[],t=0,a=e.Elements,l={mount:function(){var e=this;o.is(E)&&(n(),o.on("refresh:before",function(){e.destroy()}).on("refresh",n).on("resize",function(){t!==c()&&(e.destroy(),o.refresh())}))},destroy:function(){g(s),s=[]},get clones(){return s},get length(){return s.length}};function n(){l.destroy();var n=t=c(),i=a.length,o=a.register;if(i){for(var r=a.slides;r.length<n;)r=r.concat(r);r.slice(0,n).forEach(function(e,t){e=d(e);m(a.list,e),s.push(e),o(e,t+i,t%i)}),r.slice(-n).forEach(function(e,t){e=d(e);Y(e,r[0]),s.push(e),o(e,t-n,(i+t-n%i)%i)})}}function c(){var e,t,n,i=o.options;return i.clones||(e=i.autoWidth||i.autoHeight?a.length:i.perPage,(e=(n=k(o.root,i["fixed"+(t=i.direction===N?"Height":"Width")]))?Math.ceil(a.track["client"+t]/n):e)*(i.drag?i.flickMaxPages+1:1))}function d(e){e=e.cloneNode(!0);return v(e,o.classes.clone),_(e,"id"),e}return l},Layout:function(e,t){var n,i,o,r,s,a,l,c,d,u,p,f,h=t.Elements,g=e.options.direction===N,m=(n={mount:function(){e.on("resize load",re(function(){e.emit("resize")},e.options.throttle),window).on("resize",y).on("updated refresh",v),v(),this.totalSize=g?this.totalHeight:this.totalWidth,this.slideSize=g?this.slideHeight:this.slideWidth},destroy:function(){_([h.list,h.track],"style")},get size(){return g?this.height:this.width}},i=g?(c=e,p=t.Elements,f=c.root,{margin:"marginBottom",init:function(){this.resize()},resize:function(){u=c.options,d=p.track,this.gap=k(f,u.gap);var e=u.padding,t=k(f,e.top||e),e=k(f,e.bottom||e);this.padding={top:t,bottom:e},x(d,{paddingTop:w(t),paddingBottom:w(e)})},totalHeight:function(e){void 0===e&&(e=c.length-1);e=p.getSlide(e);return e?S(e.slide).bottom-S(p.list).top+this.gap:0},slideWidth:function(){return k(f,u.fixedWidth||this.width)},slideHeight:function(e){return u.autoHeight?(e=p.getSlide(e))?e.slide.offsetHeight:0:(e=u.fixedHeight||(this.height+this.gap)/u.perPage-this.gap,k(f,e))},get width(){return d.clientWidth},get height(){var e=u.height||this.width*u.heightRatio;return J(e,'"height" or "heightRatio" is missing.'),k(f,e)-this.padding.top-this.padding.bottom}}):(o=e,s=t.Elements,a=o.root,{margin:"margin"+((l=o.options).direction===L?"Left":"Right"),height:0,init:function(){this.resize()},resize:function(){l=o.options,r=s.track,this.gap=k(a,l.gap);var e=l.padding,t=k(a,e.left||e),e=k(a,e.right||e);this.padding={left:t,right:e},x(r,{paddingLeft:w(t),paddingRight:w(e)})},totalWidth:function(e){void 0===e&&(e=o.length-1);var t,e=s.getSlide(e),n=0;return e&&(e=S(e.slide),t=S(s.list),n=l.direction===L?t.right-e.left:e.right-t.left,n+=this.gap),n},slideWidth:function(e){return l.autoWidth?(e=s.getSlide(e))?e.slide.offsetWidth:0:(e=l.fixedWidth||(this.width+this.gap)/l.perPage-this.gap,k(a,e))},slideHeight:function(){var e=l.height||l.fixedHeight||this.width*l.heightRatio;return k(a,e)},get width(){return r.clientWidth-this.padding.left-this.padding.right}}),W(i).forEach(function(e){n[e]||Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(i,e))}),n);function v(){m.init(),x(e.root,{maxWidth:w(e.options.width)}),h.each(function(e){e.slide.style[m.margin]=w(m.gap)}),y()}function y(){var t=e.options,n=(m.resize(),x(h.track,{height:w(m.height)}),t.autoHeight?null:w(m.slideHeight()));h.each(function(e){x(e.container,{height:n}),x(e.slide,{width:t.autoWidth?null:w(m.slideWidth(e.index)),height:e.container?null:n})}),e.emit("resized")}return m},Drag:function(a,l){var r,c,d,u,p=l.Track,f=l.Controller,s=a.options.direction===N,h=s?"y":"x",t={disabled:!1,mount:function(){var e=this,t=l.Elements,n=t.track;a.on("touchstart mousedown",i,n).on("touchmove mousemove",o,n,{passive:!1}).on("touchend touchcancel mouseleave mouseup dragend",m,n).on("mounted refresh",function(){y(t.list.querySelectorAll("img, a"),function(e){a.off("dragstart",e).on("dragstart",function(e){e.preventDefault()},e,{passive:!1})})}).on("mounted updated",function(){e.disabled=!a.options.drag})}};function i(e){t.disabled||u||g(e)}function g(e){r=p.toCoord(p.position),c=v(e,{}),d=c}function o(e){var t,n,i,o;c&&(d=v(e,c),u?(e.cancelable&&e.preventDefault(),a.is(A)||(t=r[h]+d.offset[h],p.translate((t=t,a.is(j)&&(i=(n=p.sign)*p.trim(p.toPosition(0)),o=n*p.trim(p.toPosition(f.edgeIndex)),(t*=n)<i?t=i-7*Math.log(i-t):o<t&&(t=o+7*Math.log(t-o)),t*=n),t)))):(()=>{var e=d.offset;if(!a.State.is(O)||!a.options.waitForTransition)return e=180*Math.atan(H(e.y)/H(e.x))/Math.PI,(e=s?90-e:e)<a.options.dragAngleThreshold})()&&(a.emit("drag",c),u=!0,p.cancel(),g(e)))}function m(){var e,t,n,i,o,r,s;c=null,u&&(a.emit("dragged",d),r=(e=d).velocity[h],0<(s=H(r))&&(t=a.options,r=r<0?-1:1,i=n=a.index,a.is(A)||(o=p.position,s>t.flickVelocityThreshold&&H(e.offset[h])<t.swipeDistanceThreshold&&(o+=r*Math.min(s*t.flickPower,l.Layout.size*(t.flickMaxPages||1))),i=p.toIndex(o)),i===n&&.1<s&&(i=n+r*p.sign),a.is(j)&&(i=b(i,0,f.edgeIndex)),f.go(i,t.isNavigation)),u=!1)}function v(e,t){var n=e.timeStamp,i=e.touches,i=i?i[0]:e,e=i.clientX,i=i.clientY,o=t.to||{},r=o.x,o=o.y,r={x:e-(void 0===r?e:r),y:i-(void 0===o?i:o)},o=n-(t.time||0);return{to:{x:e,y:i},offset:r,time:n,velocity:{x:r.x/o,y:r.y/o}}}return t},Click:function(e,t){var n=!1;function i(e){n&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation())}return{required:e.options.drag,mount:function(){e.on("click",i,t.Elements.track,{capture:!0}).on("drag",function(){n=!0}).on("dragged",function(){setTimeout(function(){n=!1})})}}},Autoplay:function(u,e,p){var f,n=[],h=e.Elements,g={required:u.options.autoplay,mount:function(){var e,t,n,i,o,r,s,a,l,c=u.options;function d(e){l||(o||(o=e,s&&s<1&&(o-=s*n)),s=(r=e-o)/n,n<=r&&(o=0,s=1,t()),i&&i(s),a(d))}h.slides.length>c.perPage&&(t=function(){u.go(">")},n=c.interval,i=function(e){u.emit(p+":playing",e),h.bar&&x(h.bar,{width:100*e+"%"})},a=window.requestAnimationFrame,l=!0,f={pause:function(){l=!0,o=0},play:function(e){o=0,e&&(s=0),l&&(l=!1,a(d))}},c=u.options,e=[u.root,(e=u.sibling)?e.root:null],c.pauseOnHover&&(m(e,"mouseleave",1,!0),m(e,"mouseenter",1,!1)),c.pauseOnFocus&&(m(e,"focusout",2,!0),m(e,"focusin",2,!1)),h.play&&u.on("click",function(){g.play(2),g.play(3)},h.play),h.pause&&m([h.pause],"click",3,!1),u.on("move refresh",function(){g.play()}).on("destroy",function(){g.pause()}),this.play())},play:function(t){void 0===t&&(t=0),(n=n.filter(function(e){return e!==t})).length||(u.emit(p+":play"),f.play(u.options.resetProgress))},pause:function(e){void 0===e&&(e=0),f.pause(),-1===n.indexOf(e)&&n.push(e),1===n.length&&u.emit(p+":pause")}};function m(e,t,n,i){e.forEach(function(e){u.on(t,function(){g[i?"play":"pause"](n)},e)})}return g},Cover:function(e,n){function t(t){n.Elements.each(function(e){e=h(e.slide,"IMG")||h(e.container,"IMG");e&&e.src&&i(e,t)})}function i(e,t){x(e.parentElement,{background:t?"":'center/cover no-repeat url("'+e.src+'")'}),x(e,{display:t?"":"none"})}return{required:e.options.cover,mount:function(){e.on("lazyload:loaded",function(e){i(e,!1)}),e.on("mounted updated refresh",function(){return t(!1)})},destroy:function(){t(!0)}}},Arrows:function(i,o,r){var s,a,n,l=i.classes,c=i.root,d=o.Elements;function u(){var e=o.Controller,t=e.prevIndex,e=e.nextIndex,n=i.length>i.options.perPage||i.is(E);s.disabled=t<0||!n,a.disabled=e<0||!n,i.emit(r+":updated",s,a,t,e)}function p(e){return X('<button class="'+l.arrow+" "+(e?l.prev:l.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg"\tviewBox="0 0 40 40"\twidth="40"\theight="40"><path d="'+(i.options.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}return{required:i.options.arrows,mount:function(){var e,t;s=d.arrows.prev,a=d.arrows.next,s&&a||!i.options.arrows||(s=p(!0),a=p(!1),n=!0,m(e=f("div",{class:l.arrows}),s),m(e,a),t=d.slider,Y(e,("slider"===i.options.arrows&&t?t:c).firstElementChild)),s&&a&&i.on("click",function(){i.go("<")},s).on("click",function(){i.go(">")},a).on("mounted move updated refresh",u),this.arrows={prev:s,next:a}},mounted:function(){i.emit(r+":mounted",s,a)},destroy:function(){_([s,a],"disabled"),n&&g(s.parentElement)}}},Pagination:function(s,t,i){var a={},l=t.Elements,c={mount:function(){var t,o,r,e,n=s.options.pagination;n&&(t=s.options,r=f("ul",{class:(o=s.classes).pagination}),e=l.getSlides(!1).filter(function(e){return!1!==t.focus||e.index%t.perPage==0}).map(function(e,t){var n=f("li",{}),i=f("button",{class:o.page,type:"button"});return m(n,i),m(r,n),s.on("click",function(){s.go(">"+t)},i),{li:n,button:i,page:t,Slides:l.getSlidesByPage(t)}}),a={list:r,items:e},e=l.slider,m("slider"===n&&e?e:s.root,a.list),s.on(le,d)),s.off(ce).on(ce,function(){c.destroy(),s.options.pagination&&(c.mount(),c.mounted())})},mounted:function(){var e;s.options.pagination&&(e=s.index,s.emit(i+":mounted",a,this.getItem(e)),d(e,-1))},destroy:function(){g(a.list),a.items&&a.items.forEach(function(e){s.off("click",e.button)}),s.off(le),a={}},getItem:function(e){return a.items[t.Controller.toPage(e)]},get data(){return a}};function d(e,t){var t=c.getItem(t),e=c.getItem(e),n=D.active;t&&$(t.button,n),e&&v(e.button,n),s.emit(i+":updated",a,t,e)}return c},LazyLoad:function(o,e,r){var t,n,i=o.options,s="sequential"===i.lazyLoad;function a(){n=[],t=0}function l(t){t=isNaN(t)?o.index:t,(n=n.filter(function(e){return!e.Slide.isWithin(t,i.perPage*(i.preloadPages+1))||(c(e.img,e.Slide),!1)}))[0]||o.off("moved."+r)}function c(e,t){v(t.slide,D.loading);var n=f("span",{class:o.classes.spinner});m(e.parentElement,n),e.onload=function(){u(e,n,t,!1)},e.onerror=function(){u(e,n,t,!0)},T(e,"srcset",C(e,de)||""),T(e,"src",C(e,"data-splide-lazy")||"")}function d(){var e;t<n.length&&c((e=n[t]).img,e.Slide),t++}function u(e,t,n,i){$(n.slide,D.loading),i||(g(t),x(e,{display:""}),o.emit(r+":loaded",e).emit("resize")),s&&d()}return{required:i.lazyLoad,mount:function(){o.on("mounted refresh",function(){a(),e.Elements.each(function(t){y(t.slide.querySelectorAll("[data-splide-lazy], ["+de+"]"),function(e){e.src||e.srcset||(n.push({img:e,Slide:t}),x(e,{display:"none"}))})}),s&&d()}),s||o.on("mounted refresh moved."+r,l)},destroy:a}},Keyboard:function(i){var o;return{mount:function(){i.on("mounted updated",function(){var e=i.options,t=i.root,n=pe[e.direction],e=e.keyboard;o&&(i.off("keydown",o),_(t,z)),e&&("focused"===e?T(o=t,z,0):o=document,i.on("keydown",function(e){n[e.key]&&i.go(n[e.key])},o))})}}},Sync:function(i){var o=i.sibling,e=o&&o.options.isNavigation;function r(){i.on(u,function(e,t,n){o.off(u).go(o.is(E)?n:e,!1),s()})}function s(){o.on(u,function(e,t,n){i.off(u).go(i.is(E)?n:e,!1),r()})}function t(){o.Components.Elements.each(function(e){var t=e.slide,n=e.index;i.off(fe,t).on(fe,function(e){e.button&&0!==e.button||a(n)},t),i.off("keyup",t).on("keyup",function(e){-1<he.indexOf(e.key)&&(e.preventDefault(),a(n))},t,{passive:!1})})}function a(e){i.State.is(d)&&o.go(e)}return{required:!!o,mount:function(){r(),s(),e&&(t(),i.on("refresh",function(){setTimeout(function(){t(),o.emit("navigation:updated",i)})}))},mounted:function(){e&&o.emit("navigation:mounted",i)}}},A11y:function(r,t){var s=r.i18n,o=t.Elements,n=[ue,z,M,q,I,"role"];function i(e,t){T(e,ue,!t),r.options.slideFocus&&T(e,z,t?0:-1)}function e(e,t){var n=o.track.id;T(e,M,n),T(t,M,n)}function a(e,t,n,i){var o=r.index,n=-1<n&&o<n?s.last:s.prev,i=-1<i&&i<o?s.first:s.next;T(e,q,n),T(t,q,i)}function l(e,t){t&&T(t.button,I,!0),e.items.forEach(function(e){var t=r.options,t=F(!1===t.focus&&1<t.perPage?s.pageX:s.slideX,e.page+1),n=e.button,e=e.Slides.map(function(e){return e.slide.id});T(n,M,e.join(" ")),T(n,q,t)})}function c(e,t,n){t&&_(t.button,I),n&&T(n.button,I,!0)}function d(i){o.each(function(e){var t=e.slide,n=e.realIndex,n=(p(t)||T(t,"role","button"),-1<n?n:e.index),e=F(s.slideX,n+1),n=i.Components.Elements.getSlide(n);T(t,q,e),n&&T(t,M,n.slide.id)})}function u(e,t){e=e.slide;t?T(e,I,!0):_(e,I)}function p(e){return"BUTTON"===e.tagName}return{required:r.options.accessibility,mount:function(){r.on("visible",function(e){i(e.slide,!0)}).on("hidden",function(e){i(e.slide,!1)}).on("arrows:mounted",e).on("arrows:updated",a).on("pagination:mounted",l).on("pagination:updated",c).on("refresh",function(){_(t.Clones.clones,n)}),r.options.isNavigation&&r.on("navigation:mounted navigation:updated",d).on("active",function(e){u(e,!0)}).on("inactive",function(e){u(e,!1)}),["play","pause"].forEach(function(e){var t=o[e];t&&(p(t)||T(t,"role","button"),T(t,M,o.track.id),T(t,q,s[e]))})},destroy:function(){var e=t.Arrows,e=e?e.arrows:{};_(o.slides.concat([e.prev,e.next,o.play,o.pause]),n)}}}},e=(se=oe,(e=me).prototype=Object.create((ae=se).prototype),(e.prototype.constructor=e).__proto__=ae,me);function me(e,t){return se.call(this,e,t,ge)||this}window.Splide=e})(),already_executed="undefined"!=typeof already_executed,$(document).ready(function(){flashActive()});var flash_current_form=null;function flashForm(e){var t=this;e&&(t.$form=$(e),t.$form.hasClass("js_init--flash-form")||(t.getSettings(),t.initialise(),t.fieldsEvents()))}function formCallback(e){e.submission_status?self.afterSubmissionActions("success"):self.afterSubmissionActions("error")}function initFlashForms(){$("form:not(.js_form--no_flash):not(.js_form--just_in_lightbox), .js-lightbox form.js_form--just_in_lightbox").each(function(e,t){new flashForm(t)})}function createCookie(e,t,n){var i;n=n?((i=new Date).setTime(i.getTime()+24*n*60*60*1e3),"; expires="+i.toGMTString()):"",document.cookie=e+"="+t+n+"; path=/"}function readCookie(e){for(var t=e+"=",n=document.cookie.split(";"),i=0;i<n.length;i++){for(var o=n[i];" "==o.charAt(0);)o=o.substring(1,o.length);if(0==o.indexOf(t))return o.substring(t.length,o.length)}return null}function eraseCookie(e){createCookie(e,"",-1)}function getUserCountry(t){if(readCookie("user_country"))return t(readCookie("user_country"));$.getJSON("http://ip-api.com/json/?callback=?",function(e){return createCookie("user_country",e.countryCode),t(e.countryCode)})}flashForm.prototype.flash_submissions_handler="https://463z3kbl0f.execute-api.eu-west-1.amazonaws.com/default/flash-forms-handler-multipart-v2",flashForm.prototype.isEnabled=function(){return this.settings.enabled},flashForm.prototype.initialise=function(){var t=this;t.$form.addClass("js_init--flash-form"),t.settings.ajax_submit?t.$form.submit(function(e){(flash_current_form=t).preSubmissionActions(),e.preventDefault(),t.isEnabled()?(e=[],$('[name="cnf"]',t.$form).length&&e.push(t.flashHandler()),$(t.$form).is("[action]")&&e.push(t.otherHandler()),$.when.apply($,e).then(function(e){t.afterSubmissionActions("success")},function(e){console.log(e),t.afterSubmissionActions("error")})):t.afterSubmissionActions("error")}):t.$form.is("[action]")&&""!=t.$form.attr("action")&&(t.$form.addClass("js-prevent-submit"),t.$form.submit(function(e){(flash_current_form=t).$form.hasClass("js-prevent-submit")&&(t.preSubmissionActions(),e.preventDefault(),e=[],$('[name="cnf"]',t.$form).length&&e.push(t.flashHandler()),$.when.apply($,e).then(function(e){t.$form.removeClass("js-prevent-submit"),t.$form.submit()},function(e){t.afterSubmissionActions("error")}))}))},flashForm.prototype.fieldsEvents=function(){var i,o=this;$("option[data-filter]",o.$form).length&&$("option[data-filter]",o.$form).each(function(e,t){var n=$(t),i=n.attr("data-filter").split("|");$('[name="'+i[0]+'"]',o.$form).on("change",function(){!$('[name="'+i[0]+'"]',o.$form)||$('[name="'+i[0]+'"]',o.$form).val()!=i[1]&&""!=$('[name="'+i[0]+'"]',o.$form).val()&&$('[name="'+i[0]+'"]',o.$form).val()?n.hide():n.show()}).change()}),$('[name="cnf"][data-conditional]',o.$form).length&&(i=[],$('[name="cnf"][data-conditional]',o.$form).each(function(){var e,t,n=$(this);-1==i.indexOf(n.attr("data-conditional"))&&(e=n.attr("data-conditional"),(t=$('[name="'+e+'"]',o.$form)).on("change",function(){$('[name="cnf"]').removeAttr("checked"),$('[name="cnf"][data-conditional-value="'+t.val()+'"]',o.$form).attr("checked","checked")}).change(),i.push(n.attr("data-conditional")))}))},flashForm.prototype.getSettings=function(){if(this.settings={ajax_submit:!0,enabled:!0,prevent_thank_you_message:!1},this.$form.is("[data-flash-form]"))try{Object.assign(this.settings,JSON.parse("{"+this.$form.attr("data-flash-form")+"}"))}catch(e){}},flashForm.prototype.preSubmissionActions=function(){var self=this;self.$form.css("opacity","0.6"),self.settings.pre_submission_callback&&(self.settings.enabled=eval(self.settings.pre_submission_callback))},flashForm.prototype.afterSubmissionActions=function(event){var self=this;switch(event){case"success":self.settings.success_callback?eval(self.settings.success_callback):self.settings.thank_you_url&&(window.location.href=self.settings.thank_you_url),self.settings.prevent_thank_you_message||self.settings.thank_you_url||(self.$form.css("opacity","1"),$(".form__container",self.$form).hide(),$(".form__error",self.$form).hide(),$(".form__thank_you_message",self.$form).fadeIn());break;case"error":self.$form.css("opacity","1"),$(".form__error",self.$form).fadeIn()}},flashForm.prototype.flashHandler=function(){var t=$.Deferred(),e={url:this.flash_submissions_handler,type:"POST",data:""};return"multipart/form-data"==this.$form.attr("enctype")?(e.data=new FormData(this.$form[0]),e.cache=!1,e.contentType=!1,e.processData=!1):e.data=this.$form.serialize(),$.ajax(e).done(function(e){t.resolve("yay")}).fail(function(){t.reject("no")}),t.promise()},flashForm.prototype.otherHandler=function(){var t=$.Deferred(),e={url:this.$form.attr("action"),type:"POST",data:""};return"multipart/form-data"==this.$form.attr("enctype")?(e.data=new FormData(this.$form[0]),e.cache=!1,e.contentType=!1,e.processData=!1):e.data=this.$form.serialize(),e.dataType="jsonp",e.crossDomain=!0,$.ajax(e).done(function(e){t.resolve("yay")}).fail(function(){t.reject("no")}),t.promise()},$(document).ready(function(){initFlashForms()}),$(document).ready(function(){var n="",t=!1,i="",o="";$.openDropdown=function(e){e=$(e);i=e.attr("data-dropdown-target"),!t&&e.is(":not(.open)")&&(t=!0,$("[data-dropdown-target].open").not(e).click(),t=!1),$('[data-dropdown="'+i+'"]').slideDown(400,function(){i=""}),$(".icon",e).removeClass("icon-chevron-down2").addClass("icon-chevron-up2"),e.addClass("open")},$.closeDropdown=function(e){var e=$(e).attr("data-dropdown")?$(e).attr("data-dropdown"):$(e).attr("data-dropdown-target"),t=$('[data-dropdown-target="'+e+'"]');e!=i&&($('[data-dropdown="'+t.attr("data-dropdown-target")+'"]').slideUp(),$(".icon",t).addClass("icon-chevron-down2").removeClass("icon-chevron-up2"),t.removeClass("open"))},$("[data-dropdown]").hide(),$("[data-dropdown-target]").off("click"),$("[data-dropdown-target]").on("click",function(e){e.preventDefault(),$(this).hasClass("open")?$.closeDropdown(this):$.openDropdown(this)}),$("[data-dropdown-hover]").each(function(){var e=$(this).attr("data-dropdown-target"),t=[];e&&$('[data-dropdown="'+e+'"]')&&(t.push('[data-dropdown="'+e+'"]'),$('[data-dropdown="'+e+'"]').attr("data-dropdown-hover","")),o=t.join(",")}),o&&($("[data-dropdown-hover]").hoverIntent(function(){$.openDropdown(this)},function(){var e=this,t=$(this).attr("data-dropdown-target");setTimeout(function(){n!==t&&$.closeDropdown(e)},400)}),$(o).on("mouseover",function(){var e=$(this).attr("data-dropdown");n=e}),$(o).on("mouseout",function(){$(this).attr("data-dropdown");n=""}),$("[data-dropdown]").on("mouseout",function(){var e=this,t=$(e).attr("data-dropdown");setTimeout(function(){n!=t&&$.closeDropdown(e)},400)}))}),$(document).ready(function(){function e(){$("[data-follow-navigation]").each(function(){var e=!!$(this).hasClass("follow-instance"),t=$(this).attr("data-transition")?$(this).attr("data-transition"):".4s",n=$(this),i=$(this).outerHeight(),e=(e||(n.css("top","-"+2*i+"px"),n.addClass("follow-instance")),$($(this).attr("data-start-follow-after")));if(!e.length)return console.log("Cannot enable follow navigation.");var o=$(window).scrollTop(),e=e.position().top+e.outerHeight(),r=!!n.hasClass("follow-enabled");e<=o?r||(n.css("opacity",1),n.show(),n.css({WebkitTransition:"all "+t,MozTransition:"all "+t,MsTransition:"all "+t,OTransition:"all "+t,transition:"all "+t}),n.css("top","0px"),n.addClass("follow-enabled")):o<=e-e/2&&r&&(n.css({WebkitTransition:"none",MozTransition:"none",MsTransition:"none",OTransition:"none",transition:"none"}),n.css("opacity",0),n.css("top","-"+2*i+"px"),n.removeClass("follow-enabled"))})}e(),$(window).scroll(function(){e()})}),(()=>{var t={attribute:"data-image-switcher",target:{}};$("["+t.attribute+"]").each(function(){return(new Image).src=$(this).attr(t.attribute)}),$("["+t.attribute+"]").hover(function(){var e=$(this).attr(t.attribute);t.target=$($(this).attr(t.attribute+"-target")),e&&t.target.length&&(t.target.original_image_src="IMG"==t.target.prop("tagName")?t.target.attr("src"):t.target.css("background-image"),"IMG"==t.target.prop("tagName")?t.target.attr("src",e):t.target.css("background-image","url("+e+")"))},function(){"IMG"==t.target.prop("tagName")?t.target.attr("src",t.target.original_image_src):t.target.css("background-image",t.target.original_image_src)})})(),$(document).ready(function(){$.fn.isInViewport=function(){var e=$(this).offset().top,t=e+$(this).outerHeight(),n=$(window).scrollTop(),i=n+$(window).height();return n<t&&e<i},$.fn.removeTransitions=function(){$(this)[0].style.webkitTransition="none",$(this)[0].style.mozTransition="none",$(this)[0].style.msTransition="none",$(this)[0].style.oTransition="none",$(this)[0].style.transition="none"},$.fn.restoreTransitions=function(){$(this)[0].style.webkitTransition="",$(this)[0].style.mozTransition="",$(this)[0].style.msTransition="",$(this)[0].style.oTransition="",$(this)[0].style.transition=""};function e(){$("[data-lazy]").each(function(){var e,t,n;$(this).isInViewport()&&(e=$(this),t=e.prop("tagName"),n=e.attr("data-lazy"),$("<img/>").attr("src",n).on("load",function(){"IMG"==t?e.attr("src",n):e.css("background-image","url("+n+")"),e.removeTransitions(),e.css("opacity",0),e.animate({opacity:1},1e3,function(){e.restoreTransitions()}),e.removeAttr("data-lazy")}))})}e(),oneTimeExecution(function(){$(window).scroll(function(){e()})})});var $featherLight=$.featherlight,lightbox_settings=($.featherlight.autoBind=!1,{namespace:"js-lightbox",targetAttr:"data-lightbox",closeIcon:'<i class="icon icon-close"></i>',persist:!0}),lightbox_gallery_settings={namespace:"js-lightbox",targetAttr:"data-lightbox-gallery",closeIcon:'<i class="icon icon-close"></i>',previousIcon:'<div class="lightbox__arrow lightbox__arrow--previous"></div>',nextIcon:'<div class="lightbox__arrow lightbox__arrow--next"></div>',variant:"js-lightbox-gallery"};function getVideoType(e){return 0<=e.indexOf("yout")?"youtube":0<=e.indexOf("vimeo")?"vimeo":void 0}function getVideoID(e){var t,n,i=getVideoType(e);return"youtube"==i&&(t=2 in(n=e.match(/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/))?n[2]:""),"vimeo"==i&&(-1<e.indexOf("clip_id")?n=e.match(/(clip_id=(\d+))/):-1<e.indexOf("player")?(n=e.match(/player\.vimeo\.com\/video\/([0-9]*)/))[2]=n[1]:n=e.match(/(?:http|https):\/\/(www\.)?vimeo.com\/(\d+)($|\/)/),t=2 in n?n[2]:""),t}$(document).ready(function(){$("[data-lightbox]").featherlight(lightbox_settings),$("[data-lightbox-gallery]").featherlightGallery(lightbox_gallery_settings),$("[data-lightbox-ajax]").click(function(e){e.preventDefault();e=$(this).attr("data-lightbox-ajax");$featherLight('<div class="lightbox"><div data-ajax-call="'+e+'" data-ajax-auto-delete><div class="loading-spinner"></div></div></div>',lightbox_settings)}),$("[data-lightbox-video]").click(function(e){e.preventDefault();var t,e=$(this).attr("data-lightbox-video"),n=getVideoType(e),e=getVideoID(e);"youtube"==n?t='<div class="video-container"><iframe src="//www.youtube.com/embed/'+e+'?autoplay=1&showinfo=0" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>':"vimeo"==n&&(t='<div class="video-container"><iframe src="//player.vimeo.com/video/'+e+"?title=0&amp;byline=0&amp;portrait=0&amp;color=96c159&amp;api=1&amp;autoplay=1&amp;player_id=video_"+e+'" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe></div>'),$featherLight(t,{namespace:"js-lightbox-video",targetAttr:"data-lightbox",closeIcon:'<i class="icon icon-close"></i>'})}),oneTimeExecution(function(){var o=[],r="";$(document).on("click","[data-lightbox-group]",function(e){e.preventDefault();e=$(this).attr("data-lightbox-group");o=[],$('[data-lightbox-group="'+e+'"]').each(function(){o.push($($(this).attr("data-lightbox")))}),r=$($(this).attr("data-lightbox"))}),$(document).on("click","[data-lightbox-next], [data-lightbox-previous]",function(e){e.preventDefault();var n=o[o.length-1],i=o[0];$.each(o,function(e,t){t.is(r)&&(void 0!==o[e-1]&&(n=o[e-1]),void 0!==o[e+1])&&(i=o[e+1])}),r=$(this).is("[data-lightbox-previous]")?($.featherlight.current().setContent(n.clone()),n):($.featherlight.current().setContent(i.clone()),i)})})});var jqueryReady=!0,active_menu=($(document).ready(function(){navigator.userAgent.match("MSIE 10.0;")&&$('<div class="browser_message" style="position: fixed; width: 100%; height: 100%; z-index: 1000000; top: 0; background: #142435; text-align: center; font-size: 0.8em; padding-top: 50px;"><div class="wrapper"><img style="width:195px;margin-bottom: 40px;" src="/assets/images/design/logo-white.svg" alt="{site_name} Logo" /><h1 style="color: white;">Uh Oh! Your browser is too old to view our site.</h1><h2 style="color: white;">Here is what you need to do</h2><p style="color: white;">Please <a style="color:#D11368;" href="http://browsehappy.com/" target="_blank" rel="noopener">upgrade your browser</a> or install <a style="color:#D11368;" href="https://www.google.com/intl/en/chrome/browser/" target="_blank" rel="noopener">Google Chrome</a> to experience this website.</p></div></div><div class="overlay"></div>').appendTo("body"),Modernizr.objectfit&&$(".cover_image, .contain_image").each(function(){$(this).html($(this).html()+"")}),readCookie("status_lightbox")&&$(".alert_status").hide(),$(".alert_status__close").click(function(){$(".alert_status").hide(),readCookie("status_lightbox")||createCookie("status_lightbox",!0,.0125)})}),(()=>{var e=window.navigator.userAgent,t=e.indexOf("MSIE ");0<t&&(parseInt(e.substring(t+5,e.indexOf(".",t)),10),document.querySelector("body").className+=" IE"),0<e.indexOf("Trident/")&&(t=e.indexOf("rv:"),parseInt(e.substring(t+3,e.indexOf(".",t)),10),document.querySelector("body").className+=" IE11"),0<(t=e.indexOf("Edge/"))&&(parseInt(e.substring(t+5,e.indexOf(".",t)),10),document.querySelector("body").className+=" EDGE")})(),!1),$responsive_menu=$("[data-responsive-menu]");function openResponsiveMenu(){$responsive_menu.prependTo("body").fadeIn(),$("body").css({overflow:"hidden",position:"fixed",height:$(window).height()}),setTimeout(function(){active_menu=!!$responsive_menu.is(":visible")},100)}function closeResponsiveMenu(){active_menu&&($("body").css({overflow:"auto",position:"initial",height:"auto"}),$responsive_menu.fadeOut(),active_menu=!1)}$("[data-responsive-menu-trigger]").click(function(e){e.preventDefault(),openResponsiveMenu()}),$("div.responsive_menu").click(function(e){$(e.target).hasClass("responsive_menu")&&closeResponsiveMenu()}),$("div.responsive_menu a.close-icon").click(function(e){closeResponsiveMenu()});var keys={37:1,38:1,39:1,40:1};function preventDefault(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.returnValue=!1}function preventDefaultForScrollKeys(e){if(keys[e.keyCode])return preventDefault(e),!1}function disableScroll(){window.addEventListener&&window.addEventListener("DOMMouseScroll",preventDefault,!1),window.onwheel=preventDefault,window.onmousewheel=document.onmousewheel=preventDefault,window.ontouchmove=preventDefault,document.onkeydown=preventDefaultForScrollKeys}function enableScroll(){window.removeEventListener&&window.removeEventListener("DOMMouseScroll",preventDefault,!1),window.onmousewheel=document.onmousewheel=null,window.onwheel=null,document.onkeydown=window.ontouchmove=null}function SB_Sliders(){$("[data-slider]").each(function(){var o,e,t,n,i,r=$(this);r.is("[data-slidered]")||(o=r.attr("data-slider"),i=r.is("[data-slider-items]")?parseInt(r.attr("data-slider-items")):1,e=r.is("[data-slider-items-on-xlarge]")?parseInt(r.attr("data-slider-items-on-xlarge")):i,t=r.is("[data-slider-items-on-large]")?parseInt(r.attr("data-slider-items-on-large")):i,n=r.is("[data-slider-items-on-medium]")?parseInt(r.attr("data-slider-items-on-medium")):i,i=r.is("[data-slider-items-on-small]")?parseInt(r.attr("data-slider-items-on-small")):i,r.slick({infinite:!0,arrows:!!r.is("[data-slider-arrows]"),slidesToShow:e,slidesToScroll:1,autoplay:!!r.is("[data-slider-autoplay]"),autoplaySpeed:r.is("[data-slider-autoplay]")&&r.attr("data-slider-autoplay")?parseInt(r.attr("data-slider-autoplay")):3e3,centerMode:!!r.is("[data-slider-center-mode]"),adaptiveHeight:!0,swipe:!1,variableWidth:!!r.is("[data-variable-width]"),dots:!!r.is("[data-slider-dots]"),responsive:[{breakpoint:1200,settings:{slidesToShow:t}},{breakpoint:1024,settings:{slidesToShow:n}},{breakpoint:640,settings:{slidesToShow:i,variableWidth:!1}}]}),r.attr("data-slidered",""),o&&($('[data-slider-for="'+o+'"][data-slider-slide]').removeClass("active"),$('[data-slider-for="'+o+'"][data-slider-slide=1]').addClass("active"),r.on("beforeChange",function(e,t,n,i){i+=1;$('[data-slider-for="'+o+'"][data-slider-slide]').removeClass("active"),$('[data-slider-for="'+o+'"][data-slider-slide='+i+"]").addClass("active")})))}),$('[data-slider-slide="prev"]').each(function(){var t=$('[data-slider="'+$(this).attr("data-slider-for")+'"]');t.length<0||$(this).is("[data-slidered]")||($(this).attr("data-slidered",""),$(this).click(function(e){e.preventDefault(),t.slick("slickPrev")}))}),$('[data-slider-slide="next"]').each(function(){var t=$('[data-slider="'+$(this).attr("data-slider-for")+'"]');t.length<=0||$(this).is("[data-slidered]")||($(this).attr("data-slidered",""),$(this).click(function(e){e.preventDefault(),t.slick("slickNext")}))}),$("[data-slider-slide]").each(function(){var t=$(this).attr("data-slider-slide"),n=$('[data-slider="'+$(this).attr("data-slider-for")+'"]');"next"==t||"prev"==t||n.length<=0||$(this).is("[data-slidered]")||($(this).attr("data-slidered",""),$(this).click(function(e){e.preventDefault(),n.slick("slickGoTo",parseInt(t)-1)}))})}function initTabs(){$("[data-tab]").hide(),$("[data-tab-target]").each(function(){$(this).hasClass("active")&&$('[data-tab="'+$(this).attr("data-tab-target")+'"]').show()})}$(document).ready(function(){$(document).on("click","[data-scrollto]",function(e){e.preventDefault(),($scrollTo=$($(this).attr("data-scrollto"))).length&&$("html, body").animate({scrollTop:$scrollTo.offset().top},800)})}),$(document).on("submit","[data-search-form]",function(e){e.preventDefault();$(this);var n,e=$(this).find("input[type=search]"),t=$(this).parent().find("[data-no-search-results]"),i=$(this).parent().find("[data-search-results]"),o=$(this).parent().find("[data-search-results-template]")[0].outerHTML,r=e.val();return r.replace(/\s/g,"").length?(n=[],$.each(search_data,function(e,t){0<=t.title.toLowerCase().indexOf(r)&&n.push(t)}),t.hide(),i.hide(),i.find("> *:not([data-search-results-template])").remove(),n.length?($.each(n,function(e,t){i.append(o.replace(/\[\[\sentry.url\s\]\]/g,t.url).replace(/\[\[\sentry.title\s\]\]/g,t.title).replace(/\[\[\sentry.date\s\]\]/g,t.date).replace(/\[\[\sentry.type\s\]\]/g,t.component.replace(" Module","")).replace(/data-search-results-template/g,""))}),void i.fadeIn()):t.fadeIn()):e.val("")}),$(document).ready(function(){var o;$("[data-sync-select]").length&&(o=!1,$("[data-sync-select]").each(function(e,t){var n=$(t).attr("data-sync-select").split("|"),i=$(n[0]);2==n.length&&0!=i.length&&0!=$("["+n[1]+"]").length&&($(t).off("change"),$(t).on("change",function(){var e;o||(e=$(this).val(),$("["+n[1]+'="'+e+'"]').click())}),$("["+n[1]+"]",i).off("click"),$("["+n[1]+"]",i).on("click",function(){var e=$(this).attr(n[1]);o=!0,$(t).val(e),o=!1}))}))}),$(document).ready(function(){$("[data-slide-toggle]").each(function(){var e=$($(this).attr("data-slide-toggle"));e.length&&e.hide()});var n=!1;$("[data-slide-toggle]").click(function(e){var t;e.preventDefault(),n||(n=!0,$("[data-slide-toggle]").removeClass("active"),$(this).addClass("active"),(e=$($(this).attr("data-slide-toggle"))).length&&(t=(t=$(this).attr("data-speed"))||400,e.hasClass("js-deployed-slide-toggle")?(e.slideToggle(t,"swing",function(){enableScroll(),n=!1}).removeClass("js-deployed-slide-toggle"),$(this).removeClass("active")):(disableScroll(),e.slideToggle(t,"swing",function(){enableScroll(),n=!1}).addClass("js-deployed-slide-toggle"))))})}),$(document).ready(function(){SB_Sliders()}),$.fn.updateTabs=function(){for(var e=$('[data-tab="'+$(this).attr("data-tab-target")+'"]'),t=$(this).parent();1==$("[data-tab-target]",t).length&&!t.is("body");)t=t.parent();$("[data-tab-target]",t).each(function(e,t){$('[data-tab="'+$(t).attr("data-tab-target")+'"]').hide()}),$("[data-tab-target]",t).removeClass("active"),$('[data-tab-target="'+$(this).attr("data-tab-target")+'"]',t).addClass("active"),e.fadeIn()},$(document).ready(function(){initTabs(),$(document).on("click","[data-tab-target]",function(e){e.preventDefault(),$(this).parent().parent().find("[data-tab-target]").removeClass("active"),$(this).addClass("active"),$(this).updateTabs()}),window.location.hash&&$('[data-tab-target="'+window.location.hash.replace("#","")+'"]').click()}),$(document).ready(function(){$(".sidebar_subnav").length}),$(document).ready(function(){$(".sidebar_news_categories").length}),$(document).ready(function(){$(".sidebar_latest_news").length}),$(document).ready(function(){$(".sidebar_information").length&&!$("body").hasClass(".js_init--sidebar_information")&&$("body").addClass("js_init--sidebar_information")}),$(document).ready(function(){$(".sidebar_event").length&&!$("body").hasClass("js_init--sidebar_event")&&$("body").addClass("js_init--sidebar_event")}),$(document).ready(function(){$(".sidebar_donation").length&&!$("body").hasClass("js_init--sidebar_donation")&&$("body").addClass("js_init--sidebar_donation")}),$(document).ready(function(){$(".video").length&&$(".video--cover, video__icon").each(function(e,t){var n,t=$(t),i=$("iframe",t);i.length&&(n=i.attr("src"),t.attr("data-src",n),i.remove(),t.on("click",function(e,t){var n=$(this);$(".video__image").hide(),$(".video__icon").hide(),$(".video--cover").append('<iframe src="'+n.attr("data-src")+'"></iframe>').removeClass("video--cover"),n.off("click")}))})}),$(document).ready(function(){$(".layer_tweet_embed").length}),$(document).ready(function(){$(".layer_table").length}),$(document).ready(function(){$(".layer_paragraph").length}),$(document).ready(function(){$(".layer_list_item").length}),$(document).ready(function(){$(".layer_list").length}),$(document).ready(function(){$(".layer_link").length}),$(document).ready(function(){$(".content_image").length}),$(document).ready(function(){$(".layer_heading").length}),$(document).ready(function(){$(".core_header").length}),$(document).ready(function(){$(".core_footer").length}),$(document).ready(function(){$(".core_404").length}),$(document).ready(function(){$(".fieldtype_textarea").length}),$(document).ready(function(){$(".fieldtype_input_text").length}),$(document).ready(function(){$(".fieldtype_dropdown_option").length}),$(document).ready(function(){$(".fieldtype_dropdown").length}),$(document).ready(function(){$(".block_testimonials_listing").length}),$(document).ready(function(){$(".block_text_image_left").length&&!$("body").hasClass("js_init--block_text_image_left")&&$("body").addClass("js_init--block_text_image_left")}),$(document).ready(function(){$(".block_style_tile").length}),$(document).ready(function(){$(".block_stories_listing").length&&!$("body").hasClass(".js_init--block_stories_listing")&&$("body").addClass("js_init--block_stories_listing")}),$(document).ready(function(){$(".block_stewarts_stories").length&&!$("body").hasClass(".js_init--block_stewarts_stories")&&$("body").addClass("js_init--block_stewarts_stories")}),$(document).ready(function(){$(".block_stories_detail").length&&!$("body").hasClass("js_init--block_stories_detail")&&$("body").addClass("js_init--block_stories_detail")}),$(document).ready(function(){$(".block_sports_centre_cta").length&&!$("body").hasClass("js_init--block_sports_centre_cta")&&$("body").addClass("js_init--block_sports_centre_cta")}),$(document).ready(function(){$(".block_split").length&&!$("body").hasClass(".js_init--block_split")&&$("body").addClass("js_init--block_split")}),$(document).ready(function(){$(".block_services_split").length&&!$("body").hasClass(".js_init--block_services_split")&&$("body").addClass("js_init--block_services_split")}),$(document).ready(function(){$(".block_sitemap").length}),$(document).ready(function(){$(".block_services_detail").length&&!$("body").hasClass(".js_init--block_services_detail")&&$("body").addClass("js_init--block_services_detail")}),$(document).ready(function(){$(".block_services_intro").length&&!$("body").hasClass(".js_init--block_services_intro")&&$("body").addClass("js_init--block_services_intro")}),$(document).ready(function(){$(".block_service_toc").length&&!$("body").hasClass(".js_init--block_service_toc")&&$("body").addClass("js_init--block_service_toc")}),$(document).ready(function(){$(".block_search").length}),$(document).ready(function(){$(".block_relationship").length&&new_page}),$(document).ready(function(){$(".block_resources_listing").length&&!$("body").hasClass(".js_init--block_resources_listing")&&$("body").addClass("js_init--block_resources_listing")}),$(document).ready(function(){$(".block_past_events").length&&!$("body").hasClass("js_init--block_past_events")&&($("body").addClass("js_init--block_past_events"),$(".block_past_events__list").slick({infinite:!1,slidesToShow:2,slidesToScroll:2,arrows:!1,dots:!0,dotsClass:"unstyled slick_dots",responsive:[{breakpoint:640,settings:{slidesToShow:1,slidesToScroll:1}}]}))}),$(document).ready(function(){var e=$(".block_our_vision_slider");e.length&&!e.data("initialized")&&(e.data("initialized",!0),(e=e.find(".splide")).length)&&new Splide(e[0],{type:"loop",gap:"25px",updateOnMove:!0,fixedWidth:"885px",fixedHeight:"653.95px",drag:!1,speed:800,isNavigation:!0,slideFocus:!0,arrows:!0,classes:{pagination:"unstyled splide__bullets"},autoplay:!1,perPage:1,interval:7e3,breakpoints:{1846:{gap:"20px",padding:{}},1023:{fixedHeight:"auto",fixedWidth:"calc(100vw - 3rem)",gap:"20px",padding:{right:"2rem",left:"1rem"}},940:{fixedWidth:"calc(100vw - 3rem)",gap:"20px",fixedHeight:"auto",padding:{right:"1rem",left:"1rem"}},600:{fixedWidth:"calc(100vw - 3rem)",fixedHeight:"auto",gap:"10px",padding:{right:"3rem",left:"0.5rem"}}}}).mount()}),$(document).ready(function(){$(".block_our_people").length&&!$("body").hasClass(".js_init--block_our_people")&&$("body").addClass("js_init--block_our_people")}),$(document).ready(function(){$(".block_our_programmes").length&&!$("body").hasClass(".js_init--block_our_programmes")&&$("body").addClass("js_init--block_our_programmes")}),$(document).ready(function(){$(".block_news_listing").length}),$(document).ready(function(){$(".block_opportunities").length&&!$("body").hasClass(".js_init--block_opportunities")&&$("body").addClass("js_init--block_opportunities")}),$(document).ready(function(){$(".block_mission_statement").length&&!$("body").hasClass(".js_init--block_mission_statement")&&$("body").addClass("js_init--block_mission_statement")}),$(document).ready(function(){$(".block_news_detail").length}),$(document).ready(function(){$(".block_layers").length}),$(document).ready(function(){$(".block_map").length&&!$("body").hasClass(".js_init--block_map")&&$("body").addClass("js_init--block_map")}),$(document).ready(function(){var e,t=$(".block_image_text");t.length&&!Modernizr.objectfit&&(e=t.find("img").prop("src"),t.find("img").hide(),e)&&t.css("backgroundImage","url("+e+")").css("backgroundSize","cover").css("backgroundPosition","center center")}),$(document).ready(function(){$(".block_large_image_text").length}),$(document).ready(function(){$(".block_home_what_we_do").length&&!$("body").hasClass(".js_init--block_home_what_we_do")&&$("body").addClass("js_init--block_home_what_we_do")}),$(document).ready(function(){$(".block_home_ways_to_help").length&&!$("body").hasClass(".js_init--block_home_ways_to_help")&&$("body").addClass("js_init--block_home_ways_to_help")}),$(document).ready(function(){$(".block_home_latest").length&&!$("body").hasClass(".js_init--block_home_latest")&&$("body").addClass("js_init--block_home_latest")}),$(document).ready(function(){$(".block_home_banner").length&&!$("body").hasClass(".js_init--block_home_banner")&&$("body").addClass("js_init--block_home_banner")}),$(document).ready(function(){$(".block_fundraising_events").length&&!$("body").hasClass("js_init--block_resources")&&($("body").addClass("js_init--block_resources"),$(".block_fundraising_events__events_list").slick({infinite:!0,slidesToShow:2,slidesToScroll:2,arrows:!1,dots:!0,dotsClass:"unstyled slick-dots",responsive:[{breakpoint:1024,settings:{slidesToShow:1,slidesToScroll:1}},{breakpoint:640,settings:{slidesToShow:1,slidesToScroll:1}}]})),$(".block_fundraising_events__events_list").on("setPosition",function(e,t,n){$(".block_fundraising_events__events_list [data-lightbox]").featherlight(lightbox_settings)})}),$(document).ready(function(){$(".block_form").length}),$(document).ready(function(){$(".block_fundraising_aims").length&&!$("body").hasClass(".js_init--block_fundraising_aims")&&$("body").addClass("js_init--block_fundraising_aims")}),$(document).ready(function(){$(".block_featured_info").length&&!$("body").hasClass(".js_init--block_featured_info")&&$("body").addClass("js_init--block_featured_info")}),$(document).ready(function(){$(".block_featured").length&&!$("body").hasClass(".js_init--block_featured")&&$("body").addClass("js_init--block_featured")}),$(document).ready(function(){$(".block_events_listing").length&&$(".upcoming_events__list").slick({infinite:!1,slidesToShow:3,slidesToScroll:3,arrows:!1,dots:!0,dotsClass:"unstyled slick_dots",responsive:[{breakpoint:1024,settings:{slidesToShow:2,slidesToScroll:2}},{breakpoint:640,settings:{slidesToShow:1,slidesToScroll:1}}]})}),$(document).ready(function(){$(".block_family_support").length&&!$("body").hasClass(".js_init--block_family_support")&&$("body").addClass("js_init--block_family_support")}),$(document).ready(function(){$(".block_news_detail").length}),$(document).ready(function(){$(".block_enterprises").length&&!$("body").hasClass("js_init--block_enterprises")&&$("body").addClass("js_init--block_enterprises")}),$(document).ready(function(){$(".block_careers_listing").length}),$(document).ready(function(){$(".block_call_to_action").length}),$(document).ready(function(){$(".block_careers_detail").length}),$(document).ready(function(){$(".block_alternating_text").length&&!$("body").hasClass(".js_init--block_alternating_text")&&$("body").addClass("js_init--block_alternating_text")});