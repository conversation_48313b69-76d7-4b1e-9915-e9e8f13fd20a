<config>
    "url": "/sitemap-news.xml"
</config><?xml version="1.0" encoding="UTF-8"?>
<urlset
	xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
	
	<% plugins.stories({ where: page => page.data && (site.config.sitemap_news.include_modules_common.includes(page.data.component) || site.config.sitemap_news.include_modules_xml.includes(page.data.component)) && !(site.config.sitemap_news.exclude_pages).includes(page.slug) && !page.url.includes(site.config.sitemap_news.exclude_folders) && !page.data.prevent_indexing && (!page.data.hasOwnProperty('enable_detail_page') || (page.data.hasOwnProperty('enable_detail_page') && page.data.enable_detail_page)), limit: 10000, context: 'sitemapxmlblog' }, (story) => { %>
        <url>
			<loc><%= site.config.url + story.url %></loc>
			<priority>1.00</priority>
		</url>
    <% }) %>
	
</urlset>
