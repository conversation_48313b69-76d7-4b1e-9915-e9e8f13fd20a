<config>
    "url": "/sitemap.xml"
</config><?xml version="1.0" encoding="UTF-8"?>
<urlset
	xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
	<url>
		<loc><%= site.config.url %></loc>
		<priority>1.00</priority>
	</url>
	<% plugins.stories({ component: 'Page', where: page => !(site.config.sitemap.exclude_pages).includes(page.slug) && !page.url.includes(site.config.sitemap.exclude_folders) && !page.data.prevent_indexing, context: 'sitemapxml' }, (story) => { %>
        <url>
			<loc><%= site.config.url + story.url %></loc>
			<priority>1.00</priority>
		</url>
    <% }) %>
</urlset>
