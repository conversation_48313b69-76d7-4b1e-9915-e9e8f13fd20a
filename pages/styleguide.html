<config>
    "layout": "default",
    "title": "Styleguide",
    "strapline": "Lorem ipsum dolor sit amet.",
    "url": "/styleguide",
    "data": {
    	"banner": "",
    	"banner_hide": "",
    	"banner_image": "//a.storyblok.com/f/44792/16f01ae732/63a83918993f73a6808fd7162b0a97e8aa81cd97.png",
		"sidebar": [
			{
				"_uid": "5e443032-13a7-4935-87e1-7710e58aedb0",
				"heading": "Subnavigation",
				"component": "[Sidebar] Subnav",
				"start_from": "",
				"_editable": "<!--#storyblok#{\"name\": \"[Sidebar] Subnav\", \"space\": \"44792\", \"uid\": \"5e443032-13a7-4935-87e1-7710e58aedb0\", \"id\": \"123923\"}-->"
			},
			{
				"_uid": "135773ca-2e1e-4b1a-80bd-7eed321f2249",
				"link": {
					"id": "63e07be5-a9c9-4ac2-a395-f5281e2d47e4",
					"url": "",
					"linktype": "story",
					"fieldtype": "multilink",
					"cached_url": "about/"
				},
				"image": "//a.storyblok.com/f/44792/16f01ae732/63a83918993f73a6808fd7162b0a97e8aa81cd97.png",
				"heading": "Information",
				"component": "[Sidebar] Information",
				"description": "Hasellus vestibulum est ex, ut cipit nulla elementum vitae.",
				"_editable": "<!--#storyblok#{\"name\": \"[Sidebar] Information\", \"space\": \"44792\", \"uid\": \"135773ca-2e1e-4b1a-80bd-7eed321f2249\", \"id\": \"123923\"}-->"
			},
			{
				"_uid": "8e7dd70e-c1b5-42c3-8bca-0d4937999235",
				"image": "//a.storyblok.com/f/44792/501e4d491e/d27c6a30bb63e96854b59e81cf1b19158df03064.png",
				"heading": "Request A Demo",
				"component": "[Sidebar] Request A Demo",
				"description": "Hasellus vestibulum est ex, ut cipit nulla elementum vitae.",
				"_editable": "<!--#storyblok#{\"name\": \"[Sidebar] Request A Demo\", \"space\": \"44792\", \"uid\": \"8e7dd70e-c1b5-42c3-8bca-0d4937999235\", \"id\": \"123923\"}-->"
			},
			{
				"_uid": "3a0103af-9160-4244-a88c-a1bcfe79a05a",
				"link": [
					{
						"url": "",
						"_uid": "d5601f29-5bfe-4397-925b-c5c49c142771",
						"file": "",
						"form": [],
						"link": {
							"id": "a702407b-c04d-4537-9a9e-12094643de11",
							"url": "",
							"linktype": "story",
							"fieldtype": "multilink",
							"cached_url": "about/careers/"
						},
						"open": "url",
						"text": "Visit Our Careers Page",
						"type": "link link--alternative",
						"new_tab": false,
						"component": "[Layer] Link",
						"url_settings": "",
						"file_settings": "",
						"form_settings": "",
						"_editable": "<!--#storyblok#{\"name\": \"[Layer] Link\", \"space\": \"44792\", \"uid\": \"d5601f29-5bfe-4397-925b-c5c49c142771\", \"id\": \"123923\"}-->"
					}
				],
				"heading": "Join Our Team",
				"component": "[Sidebar] Join Our Team",
				"description": "Hasellus vestibulum est ex, ut cipit nulla elementum vitae.",
				"_editable": "<!--#storyblok#{\"name\": \"[Sidebar] Join Our Team\", \"space\": \"44792\", \"uid\": \"3a0103af-9160-4244-a88c-a1bcfe79a05a\", \"id\": \"123923\"}-->"
			},
			{
				"_uid": "df806005-b700-499f-9509-fbd3e3402748",
				"link": {
					"id": "bca273a2-5997-416c-bd7f-2d44d4888763",
					"url": "",
					"linktype": "story",
					"fieldtype": "multilink",
					"cached_url": "contact-us"
				},
				"email": "<EMAIL> ",
				"address": "Suite 7, The Cubes Offices\nBeacon South Quarter, Sandyford Dublin 18, Ireland",
				"component": "[Sidebar] Contact Us",
				"phone_number": "+353-1-4400600",
				"_editable": "<!--#storyblok#{\"name\": \"[Sidebar] Contact Us\", \"space\": \"44792\", \"uid\": \"df806005-b700-499f-9509-fbd3e3402748\", \"id\": \"123923\"}-->"
			},
			{
				"_uid": "ca383011-db4b-4d01-8073-7c4847d46f98",
				"heading": "Latest News",
				"component": "[Sidebar] Latest News",
				"_editable": "<!--#storyblok#{\"name\": \"[Sidebar] Latest News\", \"space\": \"44792\", \"uid\": \"ca383011-db4b-4d01-8073-7c4847d46f98\", \"id\": \"123923\"}-->"
			}
		]
	}
</config>

<article class="row">
	<div class="columns small-12">
		<h1> Styleguide - Heading 1</h1>
		<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. <a href="">Vestibulum aliquam nunc nec eros mattis,</a>  ut vestibulum libero porta. Proin id risus magna. Duis malesuada nibh velit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum vitae ipsum leo. Sed in sapien quis ante consequat scelerisque at in dui.</p>
		<h2>Heading 2</h2>
		<p>Sed in sapien quis ffante consequat scelerisque at in dui. Vestibulum auctor cursus posuere. Morbi efficitur ff quam sit amet ante auctor, condimentum tempus nibh congue.</p>	
		<a href="#" class="button">Primary Button</a>
		<a href="#" class="button button--secondary">Secondary Button</a>
		<a href="#" class="button button--tertiary">Tertiary Button</a>
		<h3>Heading 3</h3>
		<p>Vestibulum auctorcursus posuere. Morbi efficitur quam sit amet ante auctor, condimentum tempus nibh congue. Nunc finibus mi ut vehicula aliquam. quam sit amet ante auctor, condimentum tempus nibh congue. Nunc finibus mi ut vehicula aliquam. </p>
		<p>
			<a href="#" class="link__alternative">Alternative link</a>
		</p>
		<h4>Heading 4</h4>
		<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum aliquam nunc nec eros mattis, ut vestibulum libero porta. Proin id risus magna. Duis malesuada nibh velit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum vitae ipsum leo. Sed in sapien quis ante consequat scelerisque at in dui. Vestibulum auctor cursus posuere. Morbi efficitur quam sit amet ante auctor, condimentum tempus nibh congue. Nunc finibus mi ut vehicula aliquam.</p>
		<p><strong>Maecenas in magna orci. Nam molestie ipsum consequat turpis laoreet lobortis. Vestibulum metus lacus, commodo scelerisque dolor id, lacinia venenatis lectus.</strong></p>	
		<h5>Heading 5</h5>
		<p>Maecenas in magna orci. Nam molestie ipsum consequat turpis laoreet lobortis. Vestibulum metus lacus, commodo scelerisque dolor id, lacinia venenatis lectus.</p>
		<ul> 
			<li>Aenean suscipit est turpis, a dignissim lectus mollis quis. Donec ut libero at metus porttitor faucibus. Nam velit nibh lorem ipsum consecuter gretes</li>			
			<li>Nunc finibus mi ut vehicula aliquam. Cras congue commodo erat ac interdum.</li>
			<li>Mauris a nibh quis felis commodo lobortis vel in purus</li>
		</ul>
		<p>Sed in sapien quis ante consequat scelerisque at in dui.commodo erat ac interdum. Sed in sapien quis ante consequat</p>
		<article class="video">
			<div class="video__container video--cover">
				<div class="cover_image video__image" data-lightbox-video="https://www.youtube.com/watch?v=NKEVYLB5m2A">
					<img src="/assets/images/design/418aca2e32f075527fb61e0b20f3ca260370caa8.png">
				</div>
			</div>
		</article>	
		<h6>Heading 6</h6>
		<p>Maecenas in magna orci. Nam molestie ipsum consequat turpis laoreet lobortis. Vestibulum metus lacus, commodo scelerisque dolor id, lacinia venenatis lectus.</p>
		<blockquote>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Saepe harum accusantium perferendis sed assumenda, inventore, eos ad ullam fuga fugit, natus. Asperiores minus hic alias repellendus, aliquid illum illo odit.
			<cite>Sidney Crosby, Captain</cite>
		</blockquote>
		<table>
			<thead>
				<tr>
					<th>Heading 1</th>
					<th>Heading 2</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td data-name="Heading 1">Morbi efficitur quam sit amet</td>
					<td data-name="Heading 2">Condimentum tempus nibh congue lorem ipsum dolor consecuter momentum</td>
				</tr>
				<tr>
					<td data-name="Heading 1">Morbi efficitur quam sit amet</td>
					<td data-name="Heading 2">Condimentum tempus nibh congue lorem ipsum dolor consecuter momentum</td>
				</tr>
				<tr>
					<td data-name="Heading 1">Morbi efficitur quam sit amet</td>
					<td data-name="Heading 2">Condimentum tempus nibh congue lorem ipsum dolor consecuter momentum</td>
				</tr>
				<tr>
					<td data-name="Heading 1">Morbi efficitur quam sit amet</td>
					<td data-name="Heading 2">Condimentum tempus nibh congue lorem ipsum dolor consecuter momentum</td>
				</tr>
				<tr>
					<td data-name="Heading 1">Morbi efficitur quam sit amet</td>
					<td data-name="Heading 2">Condimentum tempus nibh congue lorem ipsum dolor consecuter momentum</td>
				</tr>
				<tr>
					<td data-name="Heading 1">Morbi efficitur quam sit amet</td>
					<td data-name="Heading 2">Condimentum tempus nibh congue lorem ipsum dolor consecuter momentum</td>
				</tr>
			</tbody>
		</table>
	</div>
</article>