name: Test Preview Build

on:
  pull_request:
    types: [opened, synchronize]
    branches:
      - dev

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    container:
      image: europe-west2-docker.pkg.dev/flash-2024-builder-8001/flash-2024-builder/flash-2024-builder:latest

    env:
      NODE_ENV: development
      ENVIRONMENT: development
      DEBUG: 1

    steps:
      - name: Verify Running Inside Container
        run: |
          echo "Node.js version:"
          node --version
          echo "Current user:"
          whoami

      - name: Checkout code
        uses: actions/checkout@v4


      - name: Install Node.js dependencies
        run: npm install --ignore-scripts --unsafe-perm=true --allow-root --loglevel error --no-audit --maxsockets 50 --no-fund --no-update-notifier && chmod -R 755 node_modules && npm install --unsafe-perm=true --allow-root --loglevel error --no-audit --maxsockets 50 --no-fund --no-update-notifier

      - name: Flash Assets
        run: flash assets

      - name: Flash Fetch
        run: flash fetch

      - name: Flash Build
        run: flash build