name: Build and Deploy to Production

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  check-secrets-and-vars:
    runs-on: ubuntu-latest
    steps:
      - name: Check secrets and variables
        run: |
          missing_vars=()
          check_var() {
            if [ -z "${!1}" ]; then
              missing_vars+=("$1")
            elif [ ${#1} -lt 5 ]; then
              echo "Error: $1 is less than 5 characters long"
              exit 1
            fi
          }

          check_var "AWS_KEY"
          check_var "AWS_SECRET"
          check_var "AWS_BUCKET_NAME"
          check_var "AWS_S3_ENDPOINT"
          check_var "CLOUDFRONT_DISTRIBUTION_ID"

          if [ ${#missing_vars[@]} -ne 0 ]; then
            echo "Error: The following variables are missing:"
            printf '%s\n' "${missing_vars[@]}"
            exit 1
          fi

          echo "All variables and secrets in GH are set and valid"
        env:
          AWS_KEY: ${{ vars.AWS_KEY }}
          AWS_SECRET: ${{ secrets.AWS_SECRET }}
          AWS_BUCKET_NAME: ${{ vars.AWS_BUCKET_NAME }}
          AWS_S3_ENDPOINT: ${{ vars.AWS_S3_ENDPOINT }}
          CLOUDFRONT_DISTRIBUTION_ID: ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }}

  build-and-deploy:
    runs-on: ubuntu-latest
    container:
      image: europe-west2-docker.pkg.dev/flash-2024-builder-8001/flash-2024-builder/flash-2024-builder:latest

    env:
      AWS_KEY: ${{ vars.AWS_KEY }}
      AWS_SECRET: ${{ secrets.AWS_SECRET }}
      AWS_BUCKET_NAME: ${{ vars.AWS_BUCKET_NAME }}
      AWS_S3_ENDPOINT: ${{ vars.AWS_S3_ENDPOINT }}
      CLOUDFRONT_DISTRIBUTION_ID: ${{ vars.CLOUDFRONT_DISTRIBUTION_ID }}
      NODE_ENV: production
      ENVIRONMENT: production

    steps:
      - name: Verify Running Inside Container
        run: |
          echo "Node.js version:"
          node --version
          echo "Current user:"
          whoami

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: "main"
      - name: Install Node.js dependencies
        run: npm ci

      - name: Flash Production Deploy
        run: flash production-deploy

      - name: Deploy to S3
        run: s3_website push

      - name: setup redirects in S3
        run: s3_website cfg apply
