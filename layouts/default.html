<% var settings = plugins.readJSONFile('data/settings.json') %>
<!DOCTYPE html>
<html>
    <head>
        <!-- Google Tag Manager -->
        <!-- ================== -->
        <% if(site.config.google_tag_manager) { %>
            <script>
                if(navigator.userAgent.indexOf("Speed Insights") == -1) { 
                    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                    })(window,document,'script','dataLayer','<%- site.config.google_tag_manager %>');
                }
            </script>
        <% } %>
        <meta name="google-site-verification" content="dCXVwprI7DTd6dNdwJGRyYJqD-euSd7WZHBYX6kg5RQ" />

        <!-- SEO -->
        <!-- === -->
        <%- plugins.include('snippets/seo.html') %>

        <!-- Smartphone Tab Color & Other Meta Stuff -->
        <!-- ======================================= -->
        <meta name="theme-color" content="<%- site.config.tab_color %>" />
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />

        <!-- Favicon -->
        <!-- ======= -->
        <%- plugins.include('snippets/favicon.html', { cache: true }) %>

        <!-- Open Graph -->
        <!-- ========== -->
        <%- plugins.include('snippets/opengraph.html'); %>

        <!-- Google Fonts via WebFont Loader -->
        <!-- =============================== -->
        <%- plugins.include('snippets/webfonts.html'); %>

        <!-- Critical CSS (Production Only) -->
        <!-- ============================== -->
        <% if(environment == 'production') { %>
            <%- plugins.include('snippets/criticalcss.html') %>
        <% } else { %>
            <link rel="stylesheet" href="/assets/css/bundle.min.css" />
        <% } %>
        
        <!-- GFonts preconnect -->
        <!-- ================= -->
        <link rel="preconnect" href="https://fonts.google.com" />

        <!-- Canonical meta -->
        <!-- ============== -->
        <link rel="canonical" href="<%- site.config.url + page.url.replace(/\/$/, '')  %>/" />

        <!-- Cookie Script -->
        <!-- ============= -->
        <script type="text/javascript" charset="UTF-8" src="//cdn.cookie-script.com/s/2e20156e5a6e31548cd6cbb7f5d87182.js"></script>

    </head>
    <body class="<%- page.data.component ? plugins.slugify(page.data.component) : '' %> <%- plugins.segment(1) ? plugins.segment(1) : 'homepage' %>">

        <!-- Header -->
        <!-- ====== -->
        <!-- Here you can add some if statements if you want to use specific header/footer for some pages (ex: homepage) -->
        <% if(page.data.banner_hide != 'yes') { %>
            <%- plugins.include('blocks/core/header/view.html') %>
        <% } %>

        <!-- Page Content -->
        <!-- ============ -->
        <section class="main_container <% if(page.data.sidebar && page.data.sidebar.length || page.data.component == 'News Module' || page.data.component == 'Event Module' || page.data.component == 'Stories Module') { %>main_container--with-sidebar<% } %>">
            <div <% if(page.data.page_type == 'panels' || page.data.component == 'Services Module' || page.data.component == 'Services Module' || page.title === '404') { %><% } else if(page.data.component == 'Event Module') { %><% if(page.data.page_type == 'blocks') { %><% } else { %>class="main_container__inner container"<% } %><% } else { %>class="main_container__inner container"<% } %>>

                <section class="main_container__content" role="main">

                    <% if(page.content) { %>

                        <!-- Raw Page Content -->
                        <!-- ================ -->
                        <%- page.content %>

                    <% } else { %>

                        <!-- Blocks Page -->
                        <!-- =========== -->
                        <%- plugins.pageContent(page.data) %>

                    <% } %>
                </section>

                <% if(page.data.component == 'News Module') { %>
                    <% var news_listing_sidebar = plugins.getEntry({slug: 'news'}); %>
                    
                    <aside class="main_container__sidebar">
                        <%- plugins.blocks(news_listing_sidebar.data.sidebar) %>
                    </aside>

                 <% } else if(page.data.component == 'Stories Module') { %>
                    <% var stories_listing_sidebar = plugins.getEntry({slug: 'stewarts-stories'}); %>

                    <aside class="main_container__sidebar">
                        <%- plugins.blocks(stories_listing_sidebar.data.sidebar) %>
                    </aside>

                <% } else if(page.data.component == 'Event Module') { %>
                    <% var events_listing_sidebar = plugins.getEntry({slug: 'events'}); %>

                    <% if(page.data.page_type == 'blocks') { %>

                    <% } else { %>
                        
                        <aside class="main_container__sidebar">

                            <a href="/events/" class="sidebar_location__back_link link__alternative link__alternative--left">back to events</a>
                            
                            <article class="sidebar_location">
                                <h4 class="sidebar_location__heading">Event location</h4>
                                <% if (page.data.lat && page.data.lng) { %>
                                    <% if(page.data.location_address && (!page.data.lat && !page.data.lng)) { %>
                                        <%# plugins.include('snippets/gmap.html',{latitude: block.latitude,longitude: block.longitude, zoom: 13 }) %>
                                        <iframe src="https://maps.google.com/maps?q=<%- page.data.location_address %>&amp;hl=en;z=14&amp;output=embed" height="129" width="600"></iframe>
                                    <% } else { %>
                                        <iframe src="https://maps.google.com/maps?q=<%- page.data.lat %>,<%- page.data.lng %>&amp;hl=en;z=14&amp;output=embed" height="129" width="600"></iframe>
                                    <% } %>
                                    <div>           
                                        <ul class="unstyled">
                                            <li><%- page.data.location_name %></li>
                                            <li><%- page.data.location_address %></li>
                                        </ul>
                                        <a href="https://www.google.com/maps/dir//<%- page.data.location_name %>,+<%- page.data.location_address.replace(' ','+') %>,+<%- page.data.location_country %>/@<%- page.data.lat %>,<%- page.data.lng %>,14z/" target="_blank" class="link__alternative">Get Directions</a>               
                                    </div>
                                <% } %>
                            </article>
                            
                            <% if(events_listing_sidebar.data.sidebar && events_listing_sidebar.data.sidebar.length) { %>
                                <%- plugins.blocks(events_listing_sidebar.data.sidebar) %> 
                            <% } %>

                        </aside>
                    <% } %>

                    

                <% } %>

                <!-- Sidebar -->
                <% if(page.data.sidebar && page.data.sidebar.length) { %>

                    <% if(plugins.segment(1) == 'news' && plugins.segment(2) == '' || plugins.segment(1) == 'news' && page.data.component !== 'News Module' || plugins.segment(1) == 'events' && plugins.segment(2) == '' || plugins.segment(2) == 'stewarts-stories' && plugins.segment(3) == '' || plugins.segment(2) == 'stewarts-stories' && page.data.component !== 'Stories Module') { %>
                    
                    <% } else { %>
                        <aside class="main_container__sidebar">
                            <%- plugins.blocks(page.data.sidebar) %>
                        </aside>
                    <% } %>
                    
                <% } %>

            </div>
        </section>

        <% if(page.data.component == 'News Module') { %>
            <% var news_cta = plugins.getEntry({slug: 'news'}); %>
            
            <section class="main_container__content main_container__content--bottom">         

                <%- plugins.blocks(news_cta.data.body_bottom) %>

            </section>

        <% } else if(page.data.component == 'Event Module') { %>
            
            <% var events_cta = plugins.getEntry({slug: 'events'}); %>

            <% if(page.data.page_type == 'blocks') { %>

            <% } else { %>
            
                <section class="main_container__content main_container__content--bottom">         

                    <%- plugins.blocks(events_cta.data.body_bottom) %>

                </section>

            <% } %>

        <% } %>

        <% if(page.data.body_bottom && page.data.body_bottom.length) { %>
            <section class="main_container__content main_container__content--bottom">         

                <%- plugins.blocks(page.data.body_bottom) %>

            </section>
        <% } %>
        
        <!-- <%- plugins.include('snippets/status.html', { cache: false }) %> -->

        <!-- Footer -->
        <!-- ====== -->
        <% if(page.data.footer_hide != 'yes') { %>
            <%- plugins.include('blocks/core/footer/view.html', { cache: true }) %>
        <% } %>

        <!-- JS Bundle -->
        <!-- ========= -->
        <script src="/assets/js/bundle.min.js"></script>

        <!-- Initialize InstantClick -->
        <!-- ======================= -->
        <script data-no-instant>
            InstantClick.init();
            InstantClick.on('change', function() {
                // Modernizr Duplicated Classes Fix
                $('html').removeClass('objectfit object-fit');
                window.onresize();
                <% if(site.config.google_tag_manager) { %>
                    ga('send', 'pageview', location.pathname + location.search);
                <% } %>
            });
        </script>

        <!-- Storyblok -->
        <!-- ========= -->
        <% if(environment != 'production') { %>
            <%- plugins.include('snippets/storyblok.html', { cache: true }) %>
        <% } %>

        <!-- Google Tag Manager -->
        <% if(site.config.google_tag_manager) { %>
            <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=<%- site.config.google_tag_manager %>"
            height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <% } %>
        <!-- ================== -->

        <!-- Schema -->
        <!-- ====== -->
        <% if(page.data.component == 'Event Module') { %>
            <%- // plugins.include('snippets/schemas/event.html') %>
        <% } %>
        <% if(page.data.component == 'News Module') { %>
            <%- // plugins.include('snippets/schemas/news.html') %>
        <% } %>
        <% if(settings.show_business_schema == true) { %>
            <%- // plugins.include('snippets/schemas/local_business.html', { cache: true }) %>
        <% } %>

    </body>
</html>