<% var cssClass = (options && options.class) ? options.class : 'navigation' %>
<nav class="<%= cssClass %>" data-active-children>
	<a href="/" class="<%= cssClass %>__item">Home</a>
	<a href="#" class="<%= cssClass %>__item <%= cssClass %>__item--arrow" data-active-on="/about/" data-dropdown-target="about">About Us</a>	
	<!-- FOR MOBILE -->
	<% if(cssClass == 'responsive_navigation') { %>
		<div class="<%= cssClass %>__dropdown" data-dropdown="about">
			<a href="/about/history/" class="<%= cssClass %>__item">History</a>
			<a href="/about/our-people/the-board/" class="<%= cssClass %>__item">Our people</a>
			<a href="/about/stewarts-stories/" class="<%= cssClass %>__item">Stewarts stories</a>
			<a href="/about/partnerships/" class="<%= cssClass %>__item">Partnerships</a>
			<a href="https://www.stewartscarejobs.com/" class="<%= cssClass %>__item" target="_blank">Careers</a>
			<a href="/about/governance/" class="<%= cssClass %>__item">Governance</a>
			<a href="/about/stewarts-foundation/" class="<%= cssClass %>__item">Stewarts Foundation</a>
			<a href="/strategy/" class="<%= cssClass %>__item">Strategy</a>
			<a href="/about/annual-report/" class="<%= cssClass %>__item">Annual Report</a>
		</div>
	<% } %>
	<a href="#" class="<%= cssClass %>__item <%= cssClass %>__item--arrow <%= cssClass %>__item--arrow <% if(plugins.segment(2) == 'overview' || plugins.segment(2) == 'children-s-services' || plugins.segment(2) == 'adult-services' || plugins.segment(2) == 'clinical-services' || plugins.segment(2) == 'social-enterprises') { %>active<% } %>" data-dropdown-target="our-services">Our Services</a>
	<!-- FOR MOBILE -->
	<% if(cssClass == 'responsive_navigation') { %>
	    <div class="<%= cssClass %>__dropdown" data-dropdown="our-services">
			<a href="/our-services/overview/" class="<%= cssClass %>__item">Our services</a>
			<a href="/our-services/children-s-services/" class="<%= cssClass %>__item">Children service</a>
			<a href="/our-services/adult-services/" class="<%= cssClass %>__item">Adult service</a>
			<a href="/our-services/clinical-services/" class="<%= cssClass %>__item">Clinical services</a>
			<a href="/our-services/equine-therapy/" class="<%= cssClass %>__item">Regional Equine Therapy Hub</a>
		</div>
	<% } %>
	<a href="#" class="<%= cssClass %>__item <%= cssClass %>__item--arrow <% if(plugins.segment(2) == 'fundraising' || plugins.segment(2) == 'corporate-engagement' || plugins.segment(2) == 'donations') { %>active<% } %>" data-dropdown-target="support-us">Support us</a>
	<!-- FOR MOBILE -->
	<% if(cssClass == 'responsive_navigation') { %>
		<div class="<%= cssClass %>__dropdown" data-dropdown="support-us">
			<a href="/fundraising/" class="<%= cssClass %>__item">Fundraising</a>
			<a href="/fundraising/corporate-engagement/" class="<%= cssClass %>__item">Corporate Engagement</a>
			<a href="https://www.paypal.com/donate?hosted_button_id=4RMVGWF8Z7Q3Y&source=url" target="_blank" class="<%= cssClass %>__item">Donations</a>
		</div>
	<% } %>
	<a href="/contact/" class="<%= cssClass %>__item">Contact Us</a>
	<a target="_blank" href="https://www.paypal.com/donate?hosted_button_id=4RMVGWF8Z7Q3Y&source=url" class="<%= cssClass %>__item--button button button--secondary">Donate Now</a>
	<a href="https://www.stewartscarejobs.com/" class="<%= cssClass %>__item <%= cssClass %>__item--mobile" target="_blank">Careers</a>
	<a href="/news/" class="<%= cssClass %>__item <%= cssClass %>__item--mobile">News</a>
	<a href="/resource-library/" class="<%= cssClass %>__item <%= cssClass %>__item--mobile">Resources</a>
	<a href="http://staff.stewartscare.ie/" class="<%= cssClass %>__item <%= cssClass %>__item--mobile">Staff</a>
	<a href="/sports-centre/" class="<%= cssClass %>__item <%= cssClass %>__item--mobile">Sports Centre</a>
	</nav>