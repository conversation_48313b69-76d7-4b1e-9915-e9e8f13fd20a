<% if (site.config.google_fonts.length) { %>

    <%
        var fonts = [];
        site.config.google_fonts.forEach(function(font){ 
            fonts.push(`${font.name.replace(' ', '+')}:${font.sizes}`);
        });
    %>

    <style>
        body { opacity: 0; }
        .wf-active body {
            opacity: 1;
            -webkit-transition: opacity 0.1s ease-out;  
            -moz-transition: opacity 0.1s ease-out; 
            -o-transition: opacity 0.1s ease-out;  
            transition: opacity 0.1s ease-out;  
        }
    </style>
    <script type="text/javascript">
        WebFontConfig = {
            google: { families: [
                '<%- fonts.join('\',\'');  %>'
            ] }
        };
        (function() {
            var wf = document.createElement('script');
            wf.src = ('https:' == document.location.protocol ? 'https' : 'http') +
            '://ajax.googleapis.com/ajax/libs/webfont/1.6.26/webfont.js';
            wf.type = 'text/javascript';
            wf.async = 'true';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(wf, s);
        })();
        setTimeout(function(){
            document.documentElement.classList.add('wf-active');
        }, 1000);
    </script>
<% } %>