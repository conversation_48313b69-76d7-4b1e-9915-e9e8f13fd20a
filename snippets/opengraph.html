<% 
    var settings = plugins.readJSONFile('data/settings.json') 
    var page_title = page.data && (page.data.opengraph_text || page.data.seo_title) || page.title.replace('Homepage', 'Home')
    var site_name = site.config.name
    var og_description = page.data.seo_description || settings.seo_default_description.replace('[page]', page_title).replace('[site]', site_name)
    var image = page.data.opengraph_image || site.settings.seo_default_image || site.settings.opengraph_default_background
%>

<meta property="og:title" content="<%- page_title %>">
<meta property="og:description" content="<%- og_description %>" />
<meta property="og:type" content="website">
<meta property="og:url" content="<%- site.config.url + page.url %>">
<meta property="og:site_name" content="<%- site.config.name %>">
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />

<meta name="twitter:card" content="summary_large_image">
<meta property="twitter:domain" content="<%- site.config.domain %>">
<meta property="twitter:url" content="<%- site.config.url + page.url %>">
<meta name="twitter:title" content="<%- page_title %>" />
<meta name="twitter:description" content="<%- og_description %>" />

<meta property="og:image" content="<%- plugins.img(image, { q: 60, lossless: 1, fit: 'crop', w: 1200, h: 630, auto: 'format' }); %>" />
<meta name="twitter:image" content="<%- plugins.img(image, { q: 60, lossless: 1, fit: 'crop', w: 1200, h: 630, auto: 'format' }); %>" />