<% const pages = (options.pages) ? options.pages : plugins.readJSONFile('data/sitemap.json'); %>
<% const start_from = options.start_from %>
<% const strip = html => html.replace(/(<([^>]+)>)/ig, ''); %>
<% pages.forEach((entry, index) => { %>
	<% if(entry.url.startsWith(start_from) && entry.module != 'Folder') { %>
		<li><a href="<%= entry.url %>" <% if(entry.url === page.url) { %>class="active"<% } %>><%= entry.title %></a></li>
	<% } %>
	<% if(entry.sub_stories) { %>
		<% const result = plugins.include('snippets/navigation-start-from.html', { pages: entry.sub_stories, start_from: start_from }); %>
		<% if(strip(result).trim()) { %><li><ul><%- result %></ul></li><% } %>
	<% } %>
<% }); %>