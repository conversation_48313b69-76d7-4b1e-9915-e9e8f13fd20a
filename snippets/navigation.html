<!-- ====================================================================================
 Explode exisiting site pages or specific pages ?
==================================================================================== -->
<%
	var sitemap = plugins.readJSONFile('data/sitemap.json');
	if(options.pages) { 
		pages = options.pages;
	} else {
		pages = sitemap;
	}
%>

<!-- ====================================================================================
 Only look for specific folder sub pages ?
==================================================================================== -->
<%
	if(options.start_from) { 
		context = `/${options.start_from.replace(/\//g,'')}/`;
		if(context != '' && context != '/') {
			context = options.start_from.replace(/\//g,'') != '' ? `/${options.start_from.replace(/^\//g,'').replace(/\/$/g,'')}/` : '/';
		} else {
			context = '/'
		}
	} else {
		context = '/';
	}
%>

<!-- ====================================================================================
 Do we want a <ul> or a <li> ?
==================================================================================== -->
<%
	if(!options.hasOwnProperty('ul') || options.ul) { 
		ul = true;
	} else {
		ul = false;
	}
	inner_ul = false;
%>

<!-- ====================================================================================
 Do we want to exclude some urls ?
==================================================================================== -->
<%
	if(options.exclude) { 
		exclude = options.exclude;
	} else {
		exclude = '';
	}
%>

<!-- ====================================================================================
 Do we want to exclude some urls ?
==================================================================================== -->
<%
	if(options.exclude_paths) { 
		exclude_paths = options.exclude_paths;
	} else {
		exclude_paths = [];
	}
%>

<!-- ====================================================================================
 Do we want to disable some links ?
==================================================================================== -->
<%
	if(options.disable) { 
		disable = options.disable;
	} else {
		disable = [];
	}
%>

<!-- ====================================================================================
 Do we want to show detail pages of some listings?
==================================================================================== -->
<%
	if(options.modules) { 
		modules = options.modules;
	} else {
		modules = ['Page', 'Folder'];
	}

%>



<!-- ====================================================================================
 Output the Navigation
==================================================================================== -->
<% if(ul) { %>
	<ul <% if(options.class) { %>class="<%- options.class %>"<% } %>>
<% } %>
	<% var found = false; %>
	<% pages.forEach( function(entry, index) { %>
		<% if(!exclude.includes((entry.url.replace(/\/$/,'').split('/'))[(entry.url.replace(/\/$/,'').split('/').length - 1)]) && !exclude_paths.filter( path => entry.url.includes(path) && entry.url != path ).length) { %>
			
			<% if( entry.url.includes(context) && entry.url.replace(context).split('/').length <= 2 && !entry.hidden && modules.includes(entry.module) && ( (modules.includes('Folder') && !entry.hasOwnProperty('is_startpage')) || !modules.includes('Folder')) ) { found = true; %>

				<%
					if(entry.module == 'Folder' && entry.hasOwnProperty('starting_page')) {
						var folder = Object.assign({}, entry);
						entry = entry.starting_page;
					} else if (entry.module == 'Folder') {
						disable.push(entry.url);
					}
				%>
				<li>
				<% inner_ul = true; %>

				<% if(disable.includes(entry.url)) { %>
					<span class="disabled_link <% if(entry.url == page.url) { %>active<% } %>">
						<%- entry.title %>
					</span>
				<% } else { %>
					<a href="<%- entry.url %>" class="<% if(entry.url == page.url) { %>active<% } %>" <% if(entry.url == context) { %>data-active-no-subpages<% } %>>
						<%- entry.title %>
					</a>
				<% } %>

				<%
					if(folder) {
						entry = folder;
					}
				%>

			<% } %>
			
			<% if((entry.url.includes(context) && entry.url.replace(context).split('/').length <= 2 && !entry.hidden) || !found) { %>
				<% var context_cache = context; %>

				<% if(entry.sub_stories && !entry.hidden) { %>
					<%- plugins.include('snippets/navigation.html', {pages: entry.sub_stories, start_from: entry.url, ul: inner_ul, exclude: exclude, disable: disable, modules: modules, exclude_paths: exclude_paths}) %>
				<% } %>

				<% context = context_cache; %>
			<% } %>
			
			<% if( entry.url.includes(context) && entry.url.replace(context).split('/').length <= 2 && !entry.hidden && modules.includes(entry.module) && ( (modules.includes('Folder') && !entry.hasOwnProperty('is_startpage')) || !modules.includes('Folder')) ) { %>
				</li>
			<% } %>
		<% } %>
	<% }); %>
<% if(ul) { %>
	</ul>
<% } %>