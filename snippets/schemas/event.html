<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "Event",
  "name": "<%= page.title %>",
  "startDate": "<%= page.data.date %>",
  <% if(page.data.end_date) { %>
  "endDate": "<%= page.data.end_date %>",
  <% } %>
  <% if(page.data.performer) { %>
  "performer": {
    "@type": "Person",
    "name": "<%= page.data.performer %>"
  },
  <% } %>
  <% if(page.data.location_name) { %>
  "location": {
    "@type": "Place",
    "name": "<%= page.data.location_name %>",
    "address": "<%= page.data.location_address %>"
  },
  <% } %>
  <% if(page.data.featured_image) { %>
  "image": [
    "<%- plugins.img(page.data.featured_image, { q: 800, w: 60, lossless: 1, auto: 'format' }) %>"
   ],
  <% } %>
  "description": "<%= page.data.schema_description %>"
}
</script>
