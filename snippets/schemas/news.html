<% if(page.data.featured_image) { %>
<script type="application/ld+json" data-no-instant>
{
  "@context": "http://schema.org",
  "@type": "NewsArticle",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "<%= site.config.url %><%= page.url %>"
  },
  "headline": "<%= page.title %>",
  "image": [
    "<%- plugins.img(page.data.featured_image, { q: 800, w: 60, lossless: 1, auto: 'format' }) %>"
   ],
  "datePublished": "<%= page.data.date.value %>",
  "dateModified": "<%= page.data.date.value %>",
  "author": {
    "@type": "Organization",
    "name": "<%= site.config.name %>",
    "logo": {
      "@type": "ImageObject",
      "url": "<%= site.url %>/assets/images/design/logo.png"
    }
  },
   "publisher": {
    "@type": "Organization",
    "name": "<%= site.config.name %>",
    "logo": {
      "@type": "ImageObject",
      "url": "<%= site.config.url %>/assets/images/design/logo.png"
    }
  },
  "description": "<%= page.data.excerpt %>"
}
</script>
<% } %>
