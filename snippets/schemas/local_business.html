<% var settings = plugins.readJSONFile('data/settings.json') %>
<script type="application/ld+json">
{
  "@context": "http://schema.org",
  <% if(settings.schema_business_type) { %>
  "@type": <%= settings.schema_business_type %>,
  <% } else { %>
  "@type": "LocalBusiness",
  <% } %>
  "image": [
    "<%= site.config.url %>/assets/images/design/logo.png"
   ],
  "name": "<%= site.config.title %>",
  "address": "<%= settings.main_address %>",
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": <%= settings.latitude %>,
    "longitude": <%= settings.longitude %>
  },
  "url": "<%= site.config.url %>",
  "telephone": "<%= settings.contact_phone %>",
  "email": "<%= settings.contact_email %>"
}
</script>
