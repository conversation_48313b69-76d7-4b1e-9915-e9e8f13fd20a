

<table>
	<thead>
		<% if(options.table.thead.length && options.table.thead.join('') != '' ) { %>
			<% options.table.thead.forEach(function(th){ %>
				<th><%- th.value %></th>
			<% }); %>
		<% } %>
	</thead>
	<tbody>
		<% if(options.table.tbody.length) { %>
			<% options.table.tbody.forEach(function(tr){ %>
				<tr>
					<% if(tr.body.length) { %>
						<% tr.body.forEach(function(td, index){ %>
							<td data-name="<%- options.table.thead[index].value %>">
								<%- 
									td.value.replace(/(?:\r\n|\r|\n)/g, '<br />') 
								%>
							</td>
						<% }); %>
					<% } %>
				</tr>
			<% }); %>
		<% } %>
	</tbody>
</table>