<% var links = options.pagination; %>
<ul class="pagination_block unstyled" role="navigation" aria-label="Pagination">
    <li class="pagination_block__arrow pagination_block__arrow--previous <% if(!links.previous.url) { %>disabled<% } %>">
        <% if(links.previous.url) { %><a class="pagination_block__arrow_link" href="<%= links.previous.url %>" aria-label="Previous page"><% } %>
        <% if(links.previous.url) { %></a><% } %>
    </li>
    <% links.pages.forEach(function(link) { %>
        <% if(link.current) { %>
            <li class="pagination_block__item current"><%= link.number %></li>
        <% } else { %>
            <li class="pagination_block__item">
                <a class="pagination_block__link" href="<%= link.url %>" aria-label="Page <%= link.number %>"><%= link.number %></a>
            </li>
        <% } %>
    <% }) %>
    <li class="pagination_block__arrow pagination_block__arrow--next <% if(!links.next.url) { %>disabled<% } %>">
        <% if(links.next.url) { %><a class="pagination_block__arrow_link" href="<%= links.next.url %>" aria-label="Next page"><% } %>
        <% if(links.next.url) { %></a><% } %>
    </li>
</ul>