<!-- ========================= -->
<!-- Google Maps Renderer File -->
<!-- ========================= -->

<!-- Parameters -->
<!-- ========== -->
<% if(options.latitude) { %>
    <% gmap_latitude = options.latitude %>
<% } else { %>
    <% gmap_latitude = 53.3483438 %>
<% } %>
<% if(options.longitude) { %>
    <% gmap_longitude = options.longitude %>
<% } else { %>
    <% gmap_longitude = -6.2707275 %>
<% } %>
<% if(options.zoom) { %>
    <% gmap_zoom = options.zoom %>
<% } else { %>
    <% gmap_zoom = 12 %>
<% } %>
<% if(options.address) { %>
    <% gmap_address = options.address %>
<% } else { %>
    <% gmap_address = '' %>
<% } %>
<% if(options.width) { %>
    <% gmap_width = options.width %>
<% } else { %>
    <% gmap_width = '100%' %>
<% } %>
<% if(options.tooltip) { %>
    <% gmap_tooltip = options.tooltip %>
<% } else { %>
    <% gmap_tooltip = false %>
<% } %>
<% if(options.tooltip_width) { %>
    <% gmap_tooltip_width = options.tooltip_width %>
<% } else { %>
    <% gmap_tooltip_width = '150px' %>
<% } %>
<% if(options.tooltip_height) { %>
    <% gmap_tooltip_height = options.tooltip_height %>
<% } else { %>
    <% gmap_tooltip_height = '80px' %>
<% } %>
<% if(options.class) { %>
    <% gmap_class = options.class %>
<% } else { %>
    <% gmap_class = false %>
<% } %>
<% if(options.style) { %>
    <% gmap_style = options.style %>
<% } else { %>
    <% gmap_style = false %>
<% } %>
<% if(options.disable_ui) { %>
    <% gmap_disable_ui = options.disable_ui %>
<% } else { %>
    <% gmap_disable_ui = 'true' %>
<% } %>
<% gmap_id = new Buffer(`map_${gmap_latitude}${gmap_longitude}${gmap_address}`).toString('base64').replace(/\W/g, ''); %>

<!-- The GMAP Div Container -->
<!-- ====================== -->
<div class="gmap <% if (gmap_class) { %><%- gmap_class %><% } %>" id="gmap-canvas-<%- gmap_id %>" style="<% if (gmap_style) { %><%- gmap_style %><% } %>width: <%- gmap_width %>;float: none; margin-right: 0;"></div>

<!-- Generate the map -->
<!-- ================ -->
<script type="text/javascript">

    function initialize_<%- gmap_id %>() {

        var geocoder = new google.maps.Geocoder();
        var address = "<%- gmap_address %>";
        var latitude = <%- gmap_latitude %>;
        var longitude = <%- gmap_longitude %>;
        if(address) {
            geocoder.geocode({ 'address': address }, function(results, status) {
                if(status == google.maps.GeocoderStatus.OK) {
                    latitude = results[0].geometry.location.lat();
                    longitude = results[0].geometry.location.lng();
                    showMap_<%- gmap_id %>(latitude, longitude);
                }
            });
        } else {
            showMap_<%- gmap_id %>(latitude, longitude);
        }

        function showMap_<%- gmap_id %>(latitude, longitude) {

            var myLatlng = new google.maps.LatLng(latitude, longitude);
            var mapOptions = {
                center: myLatlng,
                zoom: <%- gmap_zoom %>,
                disableDefaultUI: <%- gmap_disable_ui %>,
                scrollwheel: false
            };
            var map = new google.maps.Map(document.getElementById('gmap-canvas-<%- gmap_id %>'),
            mapOptions);

            var marker = new google.maps.Marker({
                position: myLatlng
            });

            marker.setMap(map);

            <% if (gmap_tooltip) { %>
                var infowindow = new google.maps.InfoWindow({
                    content: '<div style="width: <%- gmap_tooltip_width %>; overflow: hidden;"><%- gmap_tooltip.replace("'","\'") %></div>'
                });
                infowindow.open(map, marker);
                google.maps.event.addListener(marker, 'click', function() {
                    infowindow.open(map, marker);
                });
            <% } %>

        }

    }

</script>

<!-- Include Gmap JS Library, just one time -->
<!-- ====================================== -->
<script>
    gmap_called = (typeof gmap_called === 'undefined') ? false : gmap_called;
    if(typeof google === 'undefined' && !gmap_called) {
        gmap_called = true;
        (function() {
            var wf = document.createElement('script');
            wf.src = ('https:' == document.location.protocol ? 'https' : 'http') +
            '://maps.googleapis.com/maps/api/js?key=<%- site.config.maps.google.api_key %>';
            wf.type = 'text/javascript';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(wf, s);
        })();
    }
    var gmap_interval_<%- gmap_id %> = setInterval(function() {
        if (typeof google === 'object' && typeof google.maps === 'object') {
            initialize_<%- gmap_id %>();
            clearInterval(gmap_interval_<%- gmap_id %>);
        }
    }, 200);
</script>