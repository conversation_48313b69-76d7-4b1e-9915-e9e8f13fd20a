var storyblokImageService = function(src, quality, width, height) {
  const parameters = []
  const filters = []

  if (width || height) parameters.push(`${parseInt(width || 0)}x${parseInt(height || 0)}`)
  filters.push(`quality(${quality || 60})`)
  parameters.push(`filters:${filters.join(':')}`)
  const storyblokUrl = src += `/m/${parameters.join('/')}`
  return storyblokUrl.replace(/\(/g, '\\(').replace(/\)/g, '\\)')
}

var img = function(url, options) {

  if(!url) {
      return '';
  }

  // Support for the new assets field in Storyblok
  if(typeof url === 'object') {
      if(!url.filename) {
          return '';
      }
      url = url.filename;
  }

  // Use the storyblok image service
  const space_id = module.parent.config.storyblok.space_id;
  if (url.indexOf(`/${space_id}/`) >= 0 || url.indexOf(`/f/`) >= 0) {
      if (typeof options === 'object') {
          url = storyblokImageService(url, options.q || 60, options.w, options.h);
      }
  }

  return url;
};
module.exports = img;