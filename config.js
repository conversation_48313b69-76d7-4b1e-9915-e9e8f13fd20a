/**
 * Site Configuration
 * ==================
 * You can use this configuration file everywhere
 * in your templates by using the config variable.
 * Example: <%= config.maps.google.api_key %>
 */
module.exports = {

    // General Website Informations
    name: "<PERSON><PERSON> Care",
    url: "https://www.stewartscare.ie",
    domain: "stewartscare.ie",
    tab_color: "#1779BA",

    // Storyblok Settings
    storyblok: {
        space_id: 46384,
        api_token: "5rIlrVQqq83XeZ2Qr9lKogtt",
        folder: "storyblok",
        layout: "page",
        datasources: []
    },

    sitemap: {
        exclude_pages: ['settings', 'sitemap'],
        exclude_folders: ['team-members']
    },
    sitemap_news: {
        include_modules_common: ['News Module'],
        include_modules_xml: ['News Module'],
        include_modules_page: [],
        exclude_pages: ['settings', 'sitemap', 'homepage', 'almost-finished', 'search'],
        exclude_folders: ['relationship']
    },
    sitemap_events: {
        include_modules_common: ['Event Module'],
        include_modules_xml: ['Event Module'],
        include_modules_page: [],
        exclude_pages: ['settings', 'sitemap', 'homepage', 'almost-finished', 'search'],
        exclude_folders: ['relationship']
    },
    datasources: ['team-categories'],

    // Modules without detail pages
    // i.e. "Team Module"
    modules_without_detail : [
      
    ],

    // Critical CSS
    criticalcss : {
        include: []
    },

    // Maps APIs
    maps: {
        google: {
            api_key: "AIzaSyDqP7KslrC7sGXJYIo_LpgYeXVVVjnfbGQ"
        }
    },

    // Trackers APIs
    // (GTM, Woopra, etc)
    google_tag_manager: "GTM-T2CNPF8",
    woopra: "",
    google_fonts: [
        { name: "Nunito", "sizes": "400,400i,600,700,700i,800,800i" }
    ],

    // IMGIX Settings
    imgix: {
        source: "togetherdigital.imgix.net",
        secure_url_token: "XCfRGqWFra7knft7",
        to_remove_from_url: "//a.storyblok.com/f/"
    },
    
    // Build Settings
    build: {
        folder: "build",
        include: [
            "assets",
            "robots.txt",
            "favicon.ico"
        ],
        exclude: [
            "assets/scss",
            "assets/js/plugins",
            "assets/js/flash"
        ]
    },

    // Server Watch settings
    watch: {
        build: [ // Here, if one of those files changes, site gets rebuilt
            "config.js",
            "data/**/*.json",
            "layouts/**/*.html",
            "pages/**/*.html",
            "snippets/**/*.html",
            "blocks/**/*.html"
        ],
        assets: [ // Whenever one of those files changes, assets get compiled
            "assets/scss/**/*.scss",
            "assets/js/plugins/*.js",
            "assets/js/flash/*.js",
            "blocks/**/style.scss",
            "blocks/**/script.js"
        ]
    },

    // Assets Compilation Settings
    assets_compile: {
        "assets/css/bundle.min.css": ["assets/scss/main.scss", "blocks/**/style.scss"],
        "assets/js/bundle.min.js": ["node_modules/jquery/dist/jquery.js", "assets/js/plugins/**/*.js", "assets/js/flash/**/*.js", "blocks/**/script.js"]
    }

};