<% var block = options.block %>
<%- block._editable %>
<% var fieldName = plugins.slugify(block.name) %>
<div class="small-12 medium-<%= block.width %> columns">
	<% if(block.label) { %>
		<label for="fieldtype-<%= fieldName %>">
			<%= block.label %><% if(block.required) { %>*<% } %>
		</label>
	<% } %>
	<select 
		id="fieldtype-<%= fieldName %>"
		name="<%= fieldName %>"
		<% if(block.required) { %>required<% } %>>
		<% if(block.placeholder) { %>
			<option selected disabled><%= block.placeholder %><% if(block.required) { %>*<% } %></option>
		<% } %>
		<%- plugins.blocks(block.options) %>
	</select>
</div>