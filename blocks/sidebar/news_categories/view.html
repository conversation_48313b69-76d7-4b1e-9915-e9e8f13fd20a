<article class="row sidebar_news_categories">
	<div class="columns small-12">
		<h4>Blog Categories</h4>
		<ul>
			<li>
				<a href="/{{ segments[1] }}" {% if segments[2] != 'category' %}class="active"{% endif %}>All</a>
			</li>
			{% assign categories = site.entries.blog | categories | sort: 'name' %}
			{% for category in categories %}
				<li>
					<a href="/{{ segments[1] }}/category/{{ category.value }}" {% if segments[3] == category.value %}class="active"{% endif %}>{{ category.name }}</a>
				</li>
			{% endfor %}
		</ul>
	</div>
</article>