<% var block = options.block %>
<%- block._editable %>

<article class="sidebar_event">

	<% var stories = plugins.stories({
        component: 'Event Module',
        sort: 'asc',
        limit: '1',
        order_by: 'data.date.value',
        where: (entry) => {
            if(!entry.data.date) return false;
            var entry_date = Date.parse(entry.data.date);
            var now = new Date();
            return now < entry_date;
        }
    }, (events) => { %>
	
		<div class="sidebar_event__image cover_image">
			<img srcset="<%- plugins.imgSrcSet(events.data.featured_image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>"  src="<%- plugins.img(events.data.featured_image, { q:80 , w:1000 , lossless: 1, auto: 'format' }) %>" alt="" />
		</div>

		<div class="sidebar_event__content">
			
			<h4 class="sidebar_event__heading"><%- events.title.substring(0, '45') %></h4>

			<a href="<%- events.url %>" class="sidebar_event__link link__alternative">Find Out More</a>

		</div>

	<% }) %>

</article>

 