<%
	var segment = '/' + plugins.segment(1) + '/';
	if(plugins.segment(3)) segment = '/' + plugins.segment(1) + '/' + plugins.segment(2) + '/';
	if(plugins.segment(4)) segment = '/' + plugins.segment(1) + '/' + plugins.segment(2) + '/' + plugins.segment(3) + '/';
	if(plugins.segment(5)) segment = '/' + plugins.segment(1) + '/' + plugins.segment(2) + '/' + plugins.segment(3) + '/' + plugins.segment(4) + '/';
%>
<article class="sidebar_subnav">
	<h4 class="sidebar_subnav__heading">In This Section</h4>
	<ul><%- plugins.include('snippets/navigation-start-from.html', { start_from: segment }) %></ul>
</article>