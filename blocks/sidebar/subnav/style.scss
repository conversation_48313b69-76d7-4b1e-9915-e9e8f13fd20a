// ======================================================
// Block Styles
// ============
.sidebar_subnav {
	margin-bottom: 60px;

	&__heading {
		color: $secondary-color;
		font-weight: $weight-extrabold;
		font-size: 0.9375rem;
		letter-spacing: 0.8px;
		margin-bottom: 25px;
	}

	ul {
		margin-left: 0;

		li {
			line-height: 1.4;
			margin-bottom: 15px;

			&:before {
				display: none;
			}

			a {
				color: $text-color;
				font-weight: $weight-normal;
				font-size: 0.9375rem;
				padding: 0 20px 0 0;
				position: relative;
				display: block;

				&::after{
					@extend .icon-chevron-right2:before;
					font-family: 'icomoon';
					@include vertical-align();
					right: 0;
					font-size: 0.9em;
				}

				&.active, &:hover {
					color: $primary-color;
				}
			}
		}
	}
}