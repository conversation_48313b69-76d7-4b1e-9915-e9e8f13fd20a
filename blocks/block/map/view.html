<% 
	var block = options.block 
	var settings = plugins.readJSONFile('data/settings.json')

	var latitude = block.latitude || site.settings.latitude
	var longitude = block.longitude || site.settings.longitude
	var address = block.address || site.settings.main_address
%>

<%- block._editable %>

<article class="block_map">
	<% if(address && (!latitude && !longitude)) { %>
		<%# plugins.include('snippets/gmap.html',{latitude: block.latitude,longitude: block.longitude, zoom: 13 }) %>
		<iframe src="https://maps.google.com/maps?q=<%- address %>&amp;hl=en;z=14&amp;output=embed" height="450" width="600"></iframe>
	<% } else { %>
		<iframe src="https://maps.google.com/maps?q=<%- latitude %>,<%- longitude %>&amp;hl=en;z=14&amp;output=embed" height="450" width="600"></iframe>
	<% } %>
</article>

 