// ======================================================
// Block Styles
// ============
.block_home_banner {
	$block: &;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	position: relative;
	
	@include breakpoint( large up ) {		
		padding-top: 180px;
		padding-bottom: 180px;
	}

	@include breakpoint( medium down) {		
		padding-top: 70px;
		padding-bottom: 70px;
	}
	
	&::before{
		background: rgba(39,39,39,0.4);
		content: " ";
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 1;
	}
	
	&__button{
		position: relative;
		text-align: center;
		z-index: 2;
		
		.button{
			margin: 0 8px 0;
		}
		.button:last-child{
						
			@include breakpoint( small only ) {		
				margin-top: 10px;
			}
		}
	}

	&__description{
		color: $white;
		font-weight: $weight-semibold;
		font-size: 1.5625rem;
		line-height: 2.4375rem;
		margin-bottom: 24px;
		position: relative;
		z-index: 2;
		text-align: center;
		
		@include breakpoint( medium only ) {	
			font-size: 1.25rem;
			line-height: 2rem;
		}

		@include breakpoint( small only ) {		
			font-size: 1rem;
			line-height: 1.8rem;
		}		
	}

	&__heading{
		color: $white;
		font-weight: $weight-bold;
		font-size: 3.625rem;
		margin-bottom: 7px;
		position: relative;
		z-index: 2;		
		text-align: center;
		@include breakpoint( medium only ) {		
			font-size: 3rem;
		}
		@include breakpoint( small only ) {		
			font-size: rem-calc(38);
		}
		@media only screen and (max-width: 320px) {
			font-size: rem-calc(32);
		}
	}
}