<% var block = options.block %>
<%- block._editable %>

<article class="block_enterprises">
	<div class="container">
		<h2 class="block_enterprises__heading"><%- block.heading %></h2>
		<p class="block_enterprises__description"><%- block.description %></p>
		<div class="block_enterprises__grid grid_images">
			<% block.items.forEach( (item, index) => { %>
				<a <% if (item.link) { %>href="<%- plugins.storylink(item.link) %><% } %>" class="grid_images__item">
					<div class="grid_images__image cover_image">
						<img src="<%- plugins.img(item.image, { q:80 , w:800 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(item.image, {w:800}) %>"
						 alt="<%- item.heading %>">
					</div>
					<div class="grid_images__info_box">
						<h3 class="heading--h6 grid_images__heading">
							<%- item.heading %>
						</h3>
						<% if (index == 3) { %>
							<p class="grid_images__description">
								<%- item.description.substring("", 100) + "..." %>
							</p>
						<% } else if (index != "1" && index != "2") { %>
							<p class="grid_images__description">
								<%- item.description.substring("", 129) + "..." %>
							</p>
						<% } %>
					</div>
				</a>
			<% }); %>
		</div>
	</div>
</article>