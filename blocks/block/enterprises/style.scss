// ======================================================
// Block Styles
// ============
.block_enterprises {
	$block: &;
	background: $background-1;

	.container {
		padding: 55px 25px 85px;

		@include breakpoint(medium down) {
			padding-bottom: 50px;
			padding-top: 45px;
		}
	}

	&__heading, &__description {
		margin: auto;
		max-width: 850px;
		text-align: center;
	}

	&__description {
		margin-bottom: 2.1em;
	}
	
	&__heading {
		margin-bottom: 0.25em;
	}

	.grid_images {
//		@include grid($columns: 16, $spacing: 30px, $vertical-spacing: 20px, $breakpoint: large up);
//		@include grid($columns: 2, $spacing: 30px, $vertical-spacing: 20px, $breakpoint: medium only);
//		@include grid($columns: 1, $spacing: 45px, $breakpoint: small down);
		display: flex;
		flex-wrap: wrap;

		@include breakpoint(medium down) {
			flex-direction: column;
		}

		&__item {
			position: relative;
			margin-bottom: 30px;
			margin-right: 20px;
			width: calc(25% - 20px);

			@include breakpoint(medium down) {
				width: 100%;
			}

			&:hover img {
				transform: none;
			}
			&:before {
				@extend .icon-chevron-right2:before;
				display: none;
				@include breakpoint(small down) {
					background: $secondary-color;
					border-radius: 2px;
					color: $white;
					display: block;
					font-family: 'icomoon';
					font-size: 19px;
					height: 41px;
					line-height: 42px;
					padding-left: 16px;
					position: absolute;
					right: 30px;
					top: -20px;
					width: 41px;
					z-index: 999;
				}
			}
			@include breakpoint(medium up) {
				overflow: hidden;
				&:hover .grid_images {
					&__info_box {
						background: $primary-color;
						height: 130px;
						&:before {
							opacity: 1;
						}
						@include transitions();
					}
					&__heading {
						padding-bottom: 5px;
					}
				}
				&:nth-child(2), &:nth-child(3) {
					&:hover .grid_images__info_box {
						height: 70px;
					}
				}
			}
			@include breakpoint(large up) {
				&:first-child {
					width: calc(50% - 20px);
//					grid-column: 1/9;
//					grid-row: 1/2;
				}
//				&:nth-child(2) {
//					grid-column: 9/13;
//					grid-row: 1/2;
//				}
				&:nth-child(3) {
					
//					margin-right: 30px;
//					grid-column: 13/17;
//					grid-row: 1/2;
				}
				&:nth-child(4) {
					width: calc(45% - 20px);
//					grid-column: 1/8;
//					grid-row: 2/3;
				}
				&:nth-child(5) {
					width: calc(55% - 20px);
//					grid-column: 8/17;
//					grid-row: 2/3;
				}
				&:nth-child(6) {
					width: calc(55% - 20px);
//					grid-column: 1/9;
//					grid-row: 3/4;
				}
				&:last-child {
					width: calc(45% - 20px);
//					grid-column: 9/17;
//					grid-row: 3/4;
				}
			}
			@include breakpoint(medium only) {
				grid-column: 1/3;
				&:nth-child(2) {
					grid-column: 1/2;
				}
				&:nth-child(3) {
					grid-column: 2/3;
				}
			}
		}
		&__image {
			height: 336px;
			&:after {
				background: linear-gradient(to top, rgba(0,0,0,.7), rgba(0,0,0,0) 60%);
				content: "";
				height: 100%;
				position: absolute;
				width: 100%;
				top: 0;
				left: 0;
			}
			@include breakpoint(small down) {
				height: 200px;
			}
		}
		&__info_box {
			background: transparent;
			bottom: 0;
			left: 0;
			position: absolute;
			width: 100%;
			height: 65px;
			&:before {
				@extend .icon-chevron-right2:before;
				background: $secondary-color;
				border-radius: 2px;
				color: $white;
				font-family: 'icomoon';
				font-size: 19px;
				height: 41px;
				line-height: 42px;
				padding-left: 16px;
				position: absolute;
				right: 30px;
				top: -20px;
				width: 41px;
				opacity: 0;
			}
			@include transitions();
			@include breakpoint(medium only) {
				height: 70px;
			}
			@include breakpoint(small down) {
				height: auto;
			}
		}
		&__heading {
			color: $white;
			font-weight: $weight-bold;
			padding: 23px 30px 30px;
			margin-bottom: 0;
			@include breakpoint(medium only) {
				font-size: 1.5em;
			}
			@include breakpoint(small down) {
				padding-bottom: 20px;
			}
		}
		&__description {
			color: $white;
			font-weight: $weight-normal;
			font-size: 0.9375rem;
			line-height: 1.75rem;
			padding: 0 30px;
			@include breakpoint(small down) {
				display: none;
			}
		}
	}
}