// ======================================================
// Block Styles
// ============
.block_toc {
	$block: &;
	background: $background-1;
	padding-bottom: 80px;
	padding-top: 80px;
	
	@include breakpoint(medium down) {
		padding-top: 45px;
		padding-bottom: 50px;
	}
	
	&__description{
		margin: auto;
		margin-bottom: 33px;
		max-width: 880px;
		text-align: center;
	}
	
	&__heading{
		margin-bottom: 10px;
		text-align: center;
	}
	
	&__item{		
		border: 1px solid $border-color;
		cursor: pointer;
		display: flex;
    	flex-direction: column;

		&:hover{
			.block_toc__item_button{
				color: darken( $primary-color, 10% );
			}
		}
	}
	
	&__item_content{
		background: $white;
		padding: 25px 30px 25px; 
		text-align: center;
		display: flex;
	    flex-direction: column;
	    flex-grow: 1;
	}
	
	&__item_description{
		font-size: 0.9375rem;
		font-weight: $weight-normal;
		line-height: 1.75rem;
		margin-bottom: 17px;
		text-align: center;
	}

	&__item_button {
		margin: auto 0 0;
	}
	
	&__item_heading{
		align-items: center;
		display: flex;
		justify-content: center;
		margin-bottom: 9px;
		width: 200px;
		max-width: 100%;
		margin-left: auto;
		margin-right: auto;
		
		@include breakpoint( medium up) {
			
		}
		
	}
	
	&__item_image{
		height: 159px;
	}
	
	&__list{
		
	}

	&__list_item {
		@include flexgrid($columns: 3, $spacing: 20px,$breakpoint: large up);
		@include flexgrid($columns: 2, $spacing: 20px,$breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 20px,$breakpoint: small only);	
		justify-content: center;	
	}
}