<% var block = options.block %>
<%- block._editable %>

<article class="block_toc">
	<div class="container">
		<div class="block_toc__list">
			<div class="block_toc__list_item">
				<% if(block.items) { %>
					<% block.items.forEach( (item, index) => { %>									    
						<% if (item.link && item.link.cached_url){ %>
							<a href="<%- plugins.storylink(item.link) %>" class="block_toc__item ">	   				
								<div class="block_toc__item_image cover_image">
								<% if(item.image) {
									var image = item.image;
								} else {
									var image = site.settings.news_image_placeholder;
								} %>                                   
									<img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- item.heading %>">
								</div>
								<div class="block_toc__item_content">
									<h3 class="block_toc__item_heading heading--h6"><%- item.heading %></h3>
									<p class="block_toc__item_description"><%- item.description %></p>
									<% if(item.link) { %>
										<span class="block_toc__item_button link__alternative">read more</span>
									<% } %>
								</div>  	
							</a>    
						<% } %>		
					<% }); %>
				<% } %>
			</div>
		</div>
	</div>
</article>

 