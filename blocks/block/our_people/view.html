<% var block = options.block %>
<%- block._editable %>

<article class="block_our_people">
	<div class="container">

		<!-- Tabs -->
		<div class="block_our_people__tabs">
			
			<div class="block_our_people__tabs_container text-center">
				<a href="/about/our-people/executive-team/" class="block_our_people__tabs_item button button--tabs <% if(plugins.segment(3) == 'executive-team') { %>active<% } %>">Executive Team</a>
				<a href="/about/our-people/the-board/" class="block_our_people__tabs_item button button--tabs <% if(plugins.segment(3) == 'the-board') { %>active<% } %>">The Board</a>
				<!-- <a href="/about/our-people/org-chart/" class="block_our_people__tabs_item button button--tabs <% if(plugins.segment(3) == 'org-chart') { %>active<% } %>">Org. Chart</a> -->
				<a href="/about/our-people/service-user-council/" class="block_our_people__tabs_item button button--tabs <% if(plugins.segment(3) == 'service-user-council') { %>active<% } %>">Service User Council</a>
			</div>		

			<!-- tabs mobile -->
			<div class="block_our_people__tabs_mobile select_wrapper">
				<select onchange="location = this.value;">
					<option value="/about/our-people/executive-team/" <% if(plugins.segment(3) == 'executive-team') { %>selected<% } %>>Executive Team</option>
					<option value="/about/our-people/the-board/" <% if(plugins.segment(3) == 'the-board') { %>selected<% } %>>The Board</option>
					<!-- <option value="/about/our-people/org-chart/" <% if(plugins.segment(3) == 'org-chart') { %>selected<% } %>>Org. Chart</option> -->
					<option value="/about/our-people/service-user-council/" <% if(plugins.segment(3) == 'service-user-council') { %>selected<% } %>>Service User Council</option>
				</select>
			</div>	
			<!-- end tabs mobile -->
		</div> 
		<!-- end tabs    -->

		<p class="block_our_people__description"><%- block.description %></p>
		
		<% if(block.images) { %>
			<div class="block_our_people__gallery">
				<% block.images.forEach( (item, index) => { %>	
					<div class="block_our_people__gallery_item">
						<% if(item.image_desktop) { %>
							<div class="block_our_people__gallery_item_image block_our_people__gallery_item_image--desktop <% if (item.image_desktop_style == "photo") { %>cover_image<% } %>">
								<% if (item.image_desktop_style == "photo") { %>
									<img src="<%- plugins.img(item.image_desktop, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(item.image_desktop, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" <% if (item.description) { %>alt="<%- item.description %>"<% } %>>
								<% } else { %>
									<img src="<%- item.image_desktop %>" <% if (item.description) { %>alt="<%- item.description %>"<% } %>>
								<% } %>
							</div>
						<% } %>
						<% if(item.image_mobile) { %>
							<div class="block_our_people__gallery_item_image block_our_people__gallery_item_image--mobile <% if (item.image_mobile_style == "photo") { %>cover_image<% } %>">
								<% if (item.image_mobile_style == "photo") { %>
									<img src="<%- plugins.img(item.image_mobile, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(item.image_mobile, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" <% if (item.description) { %>alt="<%- item.description %>"<% } %>>
								<% } else { %>
									<img src="<%- item.image_mobile %>" <% if (item.description) { %>alt="<%- item.description %>"<% } %>>
								<% } %>
							</div>
						<% } %>
						<% if (item.download) { %>
							<div class="block_our_people__gallery_item_download">
								<a href="<%- item.download %>" target="_blank" class="button">Download PDF</a>
							</div>
						<% } %>
						<% if (item.description) { %>
							<p class="block_our_people__gallery_item_caption"><%- item.description %></p>
						<% } %>
					</div>
				<% }); %>
			</div>
		<% } %>
		
	</div>
	
	<% if(block.team_members.ids.length > 0) { %>

		<div class="block_our_people__team">

			<div class="container">

				<div class="block_our_people__team_list">

					<% block.team_members.ids.forEach( (id, index) => { %>
						<% var member = plugins.getEntry({id: id}) %>
						<% if (member){ %>
							<div class="block_our_people__team_item">
								<div class="block_our_people__team_image">
									<% if (member.data && member.data.image && member.data.image.filename) { %>
										<img src="<%- plugins.img(member.data.image, { q:85 , w:800 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(member.data.image, { q:85 , w:800 , lossless: 1, auto: 'format' }) %>" alt="<%- member.title %> Photo">
									<% } else { %>
										<div class="block_our_people__team_image--empty">
											<p>No Photo Added</p>
										</div>
									<% } %>
								</div>
								<div class="block_our_people__team_content">
									<h2 class="block_our_people__team_name"><%- member.title %></h3>
									<% if (member.data && member.data.position) { %>
										<h3 class="block_our_people__team_position"><%- member.data.position %></h4>
									<% } %>
								</div>
							</div>

						<% } %>

					<% }) %>
					
				</div>

			</div>

		</div>

	<% } %>

</article>