// ======================================================
// Block Styles
// ============
.block_our_people {
	$block: &;
	padding-top: 60px;

	@include breakpoint(medium down) {
		padding-top: 50px;
	}
	
	&__caption{
		color: $light-grey-2;
		font-style: italic;
		font-size: 0.875rem;
		line-height: 1.75rem;
		max-width: 918px;
		margin: auto;
		margin-bottom: 48px;
		text-align: center;		
	}

	&__description{
		margin: auto;
		margin-bottom: 24px;
		max-width: 880px;
		text-align: center;
	}

	&__gallery_item {
		margin-bottom: 40px;
	}

	&__gallery_item_image {
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 15px;
		max-width: 100%;

        &--desktop {
			
			@include breakpoint(small down) {
				display: none;
        	}
        }

        &--mobile {
        	
        	@include breakpoint(medium up) {
				display: none;
        	}
        }

        &.cover_image {
        	height: 480px;

        	@include breakpoint(medium only) {
				height: 300px;
			}

			@include breakpoint(small only) {
				height: 180px;
	        }
        }

        img {
        	max-width: 100%;
        }
	}

	&__gallery_item_download {
		margin-left: auto;
		margin-right: auto;
		text-align: center;
	}

	&__gallery_item_caption {
		text-align: center;
	}
	
	.select_wrapper{
		width: 260px;
	}
	&__team_item {
		background: $white;
		border: 1px solid $border-color;
		border-radius:2px;
		padding: 32px 32px 27px 32px;
		text-align: center;
		display: grid;
		grid-template-rows: minmax(0, 195px) minmax(0, 65px);
		gap: 29px;
	}
	
	&__team {		
		background: $background-1;		
		padding-bottom: 80px;
		padding-top: 80px;

		@include breakpoint(medium down) {
			padding-bottom: 50px;
			padding-top: 50px;
		}
	}

	&__team_image {
		height: 100%;
		&--empty {
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: $background-1;
			p {
				margin-bottom: 0;
				font-weight: $weight-bold;
			}
		}
		img {
			width: 100%;
			max-width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	&__team_list{		
		@include grid($columns: 1fr 1fr 1fr, $spacing: 22px, $vertical-spacing: 20px, $breakpoint:large up);
		@include grid($columns: 1fr 1fr, $spacing: 22px, $vertical-spacing: 20px, $breakpoint:medium only);
		@include grid($columns: 1fr , $spacing: 22px, $vertical-spacing: 20px, $breakpoint:small only);
	}
	
	&__team_name{
		color: $light-grey-2;
		font-style: italic;
		font-size: 1.1875rem;	
		font-weight: $weight-extrabold;
		text-transform: capitalize;
	}
	
	&__team_position{
		color: $text-color;
		font-style: italic;	
		font-size: 1rem;	
		text-transform: capitalize;
		font-weight:$weight-normal;
		margin: 0;
	}


	// tabs
	&__tabs {
        display: flex;
        flex-direction: row;
        margin: auto;
        margin-bottom: 5px;
    }

    &__tabs_container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;

        & .button--tabs {
            border-radius:0;

        }
        a:first-child  {
            border-radius:4px 0 0 4px;
        }
        a:last-child {
            border-radius:0 4px 4px 0;
        }

        @include breakpoint (small down) {
            display: none;
        }
    }

    &__tabs_item {
		background: $primary-color;
		min-width: unset;
		width: 100%;
		max-width: 242px;
		opacity: 0.5;
        padding: 8px 0;
        font-size: rem-calc(15);
		line-height: 1.93em;
        @include transitions(); 

        &:hover {
            background: $primary-color;
			opacity: 1;
        }

    }

    &__tabs_item + &__tabs_item {
        margin-left: 2px;
    }

    &__tabs_mobile {
        display: block;
        max-width: 400px;
       	margin: 0 auto 15px;

        & select {
            font-weight: $weight-bold !important;
			outline: 0;
			border: transparent;
			
            &:focus {
				font-weight: $weight-bold !important;
            }
		}
		
		@include breakpoint (medium up) {
            display: none;
        }
    }

    .active {
        background: $primary-color;
		opacity: 1;
    }
}

.js_init--block_our_people {
	.block_layers {
		max-width: 880px;
		margin: 20px auto;
		margin-bottom: 70px;
		.columns {
			padding: 0 25px;
		}
	}
	.block_our_people + .block_layers {
		display: flex;
		justify-content: center;
		margin-top: 48px;
	}
}