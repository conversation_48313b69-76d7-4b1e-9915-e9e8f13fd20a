// ======================================================
// Block Styles
// ============
.block_featured_info {
	$block: &;
	background: $background-1;
	position: relative;

	&__container {
		@include flexgrid($columns: 60% auto, $vertical-align: center, $breakpoint: large up);
		@include flexgrid($columns: 1, $breakpoint: medium down);
		padding-top: 60px;
		padding-bottom: 65px;
		
		@include breakpoint (medium down) {
			padding-top: 45px;
	    	padding-bottom: 50px;
		}
	}

	&__image_container {
		

		@include breakpoint (large up) {
			position: absolute;
			top: 0;
			right: 0;
			width: 38%;
			height: 100%;
		}
	}

	&__image {
		height: 100%;
		width: 100%;
		background-size: cover;
		background-repeat: no-repeat;

		@include breakpoint (medium down) {
			height: 400px;
			width: 100%;
			
		}
		@include breakpoint (small down) {
			height: 224px;
		}
	}
	
	&__content{
		
		
		@include breakpoint (large up) {
			
		}
		
		@include breakpoint( medium down) {
			
			text-align: center;
		}
	}
	
	&__heading{
		margin-bottom: 15px;
	}

	&__description {
		margin-bottom: 25px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	&__button{
		a{
			margin-bottom: 0;
		}
		.button + .button{			
			@include breakpoint( medium up) {		
				margin-left: 19px;
			}
			@include breakpoint(small down) {		
				margin-top: 10px;
			}	
		}
	}
	
	.featured_info__video{	
		cursor: pointer;
		position: relative;	

		&:hover{
				&::after{
					opacity: 0.8;				
			}
		}

		&::before{
			background: $black;
			content: " ";
			height: 100%;
			opacity: 0.2;
			position: absolute;
			width: 100%;
			z-index: 1;
		}

		&::after{
			content: " ";
			background-image: url(/assets/images/flaticons/circle.svg);
			height:55px;
			left: 50%;
			position: absolute;top: 50%;
			transform: translate(-50%, -50%);
			width:55px;
			z-index: 2;
			@include transitions();
		}
	}
}