<% var block = options.block %>
<%- block._editable %>

<article class="block_featured_info">
	<div class="container">
		<div class="block_featured_info__container">
			<div class="block_featured_info__content">
				<div class="block_featured_info__info">
					<h2 class="block_featured_info__heading heading--h3"><%- block.heading %></h2>
					<p class="block_featured_info__description"><%- block.description %></p>
					<% if (block.buttons && block.buttons.length) {%>
						<div class="block_featured_info__button">
							<%- plugins.blocks(block.buttons) %>
						</div>
					<% } %>	
				</div>
			</div>
			<div class="block_featured_info__content">

			</div>
		</div>
	</div>
	<div class="block_featured_info__image_container">
		<div class="block_featured_info__image <% if(block.video_url) { %>featured_info__video<% } %>" style="background-image: url('<%- plugins.img(block.image, { q: 85, w: 800, lossless: 1, auto: 'format' }) %>');"
		 <% if(block.video_url) { %>data-lightbox-video="<%- block.video_url %>"<% }%> ></div>
	</div>
</article>


 