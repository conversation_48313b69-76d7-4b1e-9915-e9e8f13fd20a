// ======================================================
// Block Styles
// ============
.block_our_vision_slider {
  $block: &;
  // LAYOUT
  // =================
  padding: 76px 0;

  @include breakpoint(small down) {
    padding: 80px 0;
  }

  &.white-background {
    background-color: white;
    padding-top: 0 !important;
  }

  position: relative;

  min-height: 840px;

  @include breakpoint(medium down) {
    min-height: 650px;
   }

  @include breakpoint(small down) {
   min-height: unset;
  }
  // SLIDER
  // ===============
  .splide {
    // position: relative;
    padding-left: calc((100% - 1180px) / 2);
    position: unset !important;

    &__slide {
      max-height: 653.95px;

      &__image {
        width: 100%;
        height: 100%;
        position: relative;
        transition: 0.4s ease all;
        img {
          width: 100%;
          height: 100%;
          transition: 0.4s ease all;
        }
        @include breakpoint(small down) {
          display: flex;
          flex-direction: column;
        }

        &__box {
          position: relative;
          background-color: #ffbc00;
          top: 120px;
          right: -296px;
          position: absolute;
          //   padding: 93px 24px 24px 66px;
          justify-content: center;
          align-items: center;

          opacity: 0;
          width: 482px;
          height: 412px;
          z-index: 99;
          display: flex;

          &__content {
            display: flex;
            flex-direction: column;
            gap: 36px;
            @include breakpoint(large down) {
              gap: 10px;
            }
          }

          @include breakpoint(large down) {
            right: -50px;
          }
          @include breakpoint(medium down) {
            background-color: rgba(255, 188, 0, 0.8);
            height: 312px;
          }

          @include breakpoint(small down) {
            padding: 42px 32px 32px 32px;
            height: auto;
            width: auto;
            position: unset;
          }

          &__title {
            text-align: left;
            font-size: 44.791px;
            font-weight: 700;
            line-height: 55.989px;

            @include breakpoint(small down) {
              font-size: 26.791px;
              line-height: 100%;
            }
          }
          &__description {
            max-width: 357px;
            color: #000;
            font-family: Nunito;
            font-size: 25px;
            font-style: normal;
            font-weight: 400;
            line-height: 130%;

            @include breakpoint(medium down) {
              font-size: 18.791px;
            }
          }
        }
      }

      & > div div {
        transition: 0.4s ease-in-out all;
      }

      &.is-active {
        transition: 0.4s ease-in-out all;

        & > div div {
          opacity: 100;
        }
      }

      &:not(.is-active) {
        max-height: 459.11px;

        & > * img {
          @include breakpoint(medium up) {
            max-width: 885px;
          }
        }

        & > div div {
          opacity: 0;
        }
      }
    }

    &__arrows {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      position: absolute;
      bottom: 130px;
      right: calc((100% - 1220px) / 2);

      @include breakpoint(large down) {
        right: calc((100% - 1050px) / 2);
        bottom: 60px;
      }

      @include breakpoint(medium down) {
        bottom: 10px;
         right: 20px;
      }

      &_header {
        align-items: baseline;
        display: flex;
        width: 100%;

        h2 {
          padding-right: 24px;
          font-size: 36px;
          font-style: normal;
          font-weight: 600;
          line-height: 125%; /* 45px */
        }

        a {
          padding-left: 24px;
          font-size: 15px;
          font-style: normal;
          font-weight: 700;
          line-height: 140%; /* 21px */
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 8px;
          svg {
            width: 17px;
            height: 17px;
            fill: black;
          }

          &:hover {
            color: $primary-color;

            svg {
              fill: $primary-color;
            }
          }

          @include breakpoint(medium down) {
            display: none;
          }
        }
      }

      &_wrapper {
        display: flex;
        gap: 75px;
        align-items: center;
        @include breakpoint(large down) {
          gap: 20px;
        }

        @include breakpoint(medium down) {
          gap: 20px;
          button {
            width: 38.228px;
            height: 14.635px;
          }
        }

        button {
          width: 58.228px;
          height: 24.635px;
          background-color: transparent;
          border: none;
          cursor: pointer;

          @include breakpoint(medium up) {
            &:hover {
              opacity: 0.25;
            }
          }

          svg {
            width: 58.228px;
            height: 24.635px;
          }
        }
        button:disabled {
          opacity: 0.25;
        }
      }
    }

    ul.splide__bullets {
      display: none;
    }
  }
}
