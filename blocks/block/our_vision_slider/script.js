
$(document).ready(function () {
  var $block = $(".block_our_vision_slider");
  if ($block.length && !$block.data("initialized")) {
    $block.data("initialized", true);

    var $slider = $block.find(".splide");
    if ($slider.length) {
      new Splide($slider[0], {
        type: "loop", // Ensures that it slides normally
        gap: "25px", // Space between slides
        updateOnMove: true,
        fixedWidth: "885px",
        fixedHeight: "653.95px",
        drag: false,
        speed:800,
        isNavigation: true,
        slideFocus: true,
        arrows: true,
        classes: {
          pagination: "unstyled splide__bullets",
        },
        autoplay: false,

        perPage: 1,
        interval: 7000,
        breakpoints: {
          1846: {
            gap: "20px",
            padding: {
              // right: '2rem',
              // left: '2rem'
            },
          },
          1023: {
            fixedHeight: "auto",
            fixedWidth: "calc(100vw - 3rem)",
            gap: "20px",
            padding: {
              right: "2rem",
              left: "1rem",
            },
          },
          940: {
            // Tablets

            fixedWidth: "calc(100vw - 3rem)",
            gap: "20px", // Smaller gap for smaller screens
            fixedHeight: "auto",
            padding: {
              right: "1rem",
              left: "1rem",
            },
          },
          600: {
            // Mobiles
            fixedWidth: "calc(100vw - 3rem)",
            fixedHeight: "auto",
            gap: "10px", // Even smaller gap for mobile devices
            padding: {
              right: "3rem",
              left: "0.5rem",
            },
          },
        },
      }).mount();
    }
  }
});
