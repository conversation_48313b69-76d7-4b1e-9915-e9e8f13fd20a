// ======================================================
// Block Styles
// ============
.block_opportunities {
	$block: &;
	padding-bottom: 80px;
	overflow: hidden;

	@include breakpoint( medium down ) {
		padding-bottom: 50px;
	}
	
	.container{
		position: relative;
		&::after{
			@include breakpoint( large up ) {
				content: " ";
				background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
				height: 4px;
				position: absolute;
				left: 50%;
				width: 50vw;
				top: 13px;
			}		
				
			
			@include breakpoint( small only ) {
				top: 80px;
			}
		}
	}
	
	
	&__heading{
		margin-bottom: 60px;
		position: relative;
		@include breakpoint( large up ) {			
		}
		@include breakpoint( medium down ) {
			text-align: center;
			margin-bottom: 80px;
		}
		@include breakpoint( small only ) {
			
		}
	}

	&__heading_text {
		background: $white;
		padding-right: 30px;
		z-index: 1;
    	position: relative;

    	@include breakpoint( medium down ) {
			padding-right: 0;
		}
	}
	&__title{
		@include flexgrid($columns: 2, $breakpoint : medium up);
		align-items: center;
		margin-bottom: 64px;
	}
	
	&__item{
		padding-bottom: 23px;
		border-bottom: 1px solid $border-color;
		
	}
	&__item_description{
		font-size: 0.9375rem;
		line-height: 1.75rem;
		margin-bottom: 0;
	}
	
	&__item_heading{
		font-size: 1.25rem;
		margin-bottom: 7px;		
		text-transform: capitalize;
	}
	
	&__list{
		@include flexgrid($columns: 2, $spacing:83px, $vertical-spacing:32px, $breakpoint : large up);
		@include flexgrid($columns: 1, $spacing:30px, $breakpoint : medium down);
		position: relative;

		&:before {
			@include breakpoint( medium down ) {
				background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
				bottom: 0;
				content: " ";
				height: 4px;
				left: 50%;
				top: -50px;
				transform: translate(-50%, 0%);
				position: absolute;
				width: 264px;	
			}
		}
		
	}
	
	
}