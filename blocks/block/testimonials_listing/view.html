<article class="row block_testimonials_listing">
    <% total_rating = 0 %>

    <!-- News Listing -->
    <% var stories = plugins.stories({
        component: 'Testimonials Module',
        limit: 100,
        sort: 'asc',
        order_by: 'title',
        paginate: false,
        categoriesPages: false
    }, (testimonial) => { %>

        <% total_rating += parseInt(testimonial.data.rating) %>
        <div class="testimonial_item">
            <% if (testimonial.data.image) { %>
                <div class="testimonial_item__image cover_image" <% if(testimonial.data.video_url) { %>data-lightbox-video="<%= testimonial.data.video_url %>"<% } %>>
                    <img srcset="<%- plugins.imgSrcSet(testimonial.data.image, { q: 80, w: 1000, lossless: 1, auto: 'format' }) %>"  src="<%= plugins.img(testimonial.data.image, { q: 80, w: 1000, lossless: 1, auto: 'format' }) %>">
                </div>
            <% } %>
            <div class="testimonial_item__content">
                <p class="testimonial_item__quote">
                    <%= testimonial.data.quote %>
                </p>
                <p class="testimonial_item__rating">
                    <%= testimonial.data.rating %>
                </p>
                <span class="testimonial_item__name"><%= testimonial.title %></span>
                <span class="testimonial_item__position"><%= testimonial.data.position %></span>
                <% if(testimonial.data.logo) { %>
                    <img srcset="<%- plugins.imgSrcSet(testimonial.data.logo, { q: 80, w: 1000, lossless: 1, auto: 'format' }) %>"   src="<%= plugins.img(testimonial.data.logo, { q: 80, w: 400, lossless: 1, auto: 'format' }) %>" class="testimonial_item__logo">
                <% } %>
            </div>
        </div>

    <% }, () => { %>

        <!-- Uh oh, we didn't find any testimonials :( -->
        <div class="columns small-12">
            <p>Sorry, there's currently no testimonials in the database.</p>
        </div>

    <% }) %>

</article>

<script type="application/ld+json">
{ "@context": "http://schema.org",
  "@type": "Product",
  "name": "Product Name",
  "aggregateRating":
    {"@type": "AggregateRating",
     "ratingValue": "<%= (total_rating / stories.stories.length) %>",
     "reviewCount": "<%= stories.stories.length %>"
    }
}
</script>