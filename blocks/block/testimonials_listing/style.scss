// ======================================================
// Block Styles
// ============
$background-color: $white;
.block_testimonials_listing {
	margin-bottom: 0 !important;

	&__list {
		column-gap: 71px;
		@include breakpoint( medium down ) {
			column-gap: 40px;
		}
		@media (min-width: 800px) {
			column-count: 2;
		}
		@media (max-width: 799px) {
			text-align: center;
		}
	}

	.testimonial_item {
		background-color: $background-color;
		display: inline-block;
		margin-bottom: 71px;
		@media (max-width: 799px) {
			max-width: 600px;
		}
		@include breakpoint( small down ) {
			margin-bottom: 40px;
		}
		&__image {
			align-items: center;
			display: flex;
			height: 247px;
			justify-content: center;
			position: relative;
			width: 100%;
			img {
				position: relative;
				z-index: 2;
			}
			&[data-lightbox-video] {
				cursor: pointer;
				&:hover:before {
					background-color: $white;
					color: $primary-color;
				}
			}
			&[data-lightbox-video]:after {
				background-color: rgba(#909090, .7);
				content: '';
				display: block;
				height: 100%;
				left: 0;
				position: absolute;
				top: 0;
				width: 100%;
				z-index: 3;
			}
			&[data-lightbox-video]:before {
				align-items: center;
				background-color: $primary-color;
				border-radius: 50%;
				color: $white;
				content: '\f100';
				display: flex;
				font-family: 'Flaticon';
				font-size: 4rem;
				height: 63px;
				justify-content: center;
				margin: auto;
				position: absolute;
				width: 63px;
				z-index: 4;
				@include transitions();
			}
		}
		&__content {
			padding: 22px 38px 31px;
			@include breakpoint( small down ) {
				padding: 20px;
			}
		}
		&__quote {
			font-style: italic;
			margin-bottom: 12px;
		}
		&__position,
		&__name {
			color: $secondary-color;
			display: block;
			font-size: 1.125rem;
			line-height: 1.75rem;
		}
		&__logo {
			height: 50px;
			margin-top: 27px;
		}
		&:last-child{
			@include breakpoint( small down ) {
				margin-bottom: 40px;
			}
		}
	}
}