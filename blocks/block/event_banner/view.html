<% var block = options.block %>
<%- block._editable %>

<article class="block_event_banner" style="background-image: url('<%- plugins.img(block.image, { q: 85, w: 1600, lossless: 1, auto: 'format' }) %>');">
	<div class="container">
		<% if(block.logo) { %>
			<div class="block_event_banner__logo contain_image">
		    	<img srcset="<%- plugins.imgSrcSet(block.logo, { q: 80, w: 600, lossless: 1, auto: 'format' }) %>" src="<%= plugins.img(block.logo, { q: 80, w: 600, lossless: 1, auto: 'format' }) %>" class="block_event_banner__pic">
		    </div>
		<% } %>
		<div class="block_event_banner__content">
			<h1 class="block_event_banner__heading"><%- block.heading %></h1>
			<p class="block_event_banner__description"><%- block.description %></p>
		</div>
	</div>
</article>