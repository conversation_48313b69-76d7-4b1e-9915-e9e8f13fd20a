// ======================================================
// Block Styles
// ============
.block_event_banner {
	$block: &;
//	align-items: center;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
//	display: flex;
//	justify-content: center;	
	position: relative;
	
	@include breakpoint( large up ) {		
		padding-top: 80px;
		padding-bottom: 80px;
	}

	@include breakpoint( medium down) {		
		padding-top: 70px;
		padding-bottom: 70px;
	}
	
	&::before{
		background: rgba(39,39,39,0.6);
		content: " ";
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 1;
	}

	&__logo {
		width: 250px;
		margin: 0 auto 30px;
		z-index: 2;
		position: relative;
		display: flex;

		@include breakpoint( small down) {		
			width: 200px;
		}
	}

	&__pic {
		border-radius: 4px;
		position: relative;
	}

	&__content {
		width: 600px;
		margin-left: auto;
		margin-right: auto;
		max-width: 100%;
	}
	
	&__button{
		position: relative;
		text-align: center;
		z-index: 2;
		
		.button{
			margin: 0 8px 0;
		}
		.button:last-child{
						
			@include breakpoint( small only ) {		
				margin-top: 10px;
			}
		}
	}

	.container{
		
	}

	&__description{
		color: $white;
		font-weight: $weight-semibold;
		font-size: 1.5625rem;
		line-height: 2.4375rem;
		margin-bottom: 24px;
		position: relative;
		z-index: 2;
		text-align: center;
		
		@include breakpoint( medium only ) {	
			font-size: 1.25rem;
			line-height: 2rem;
		}

		@include breakpoint( small only ) {		
			font-size: 1rem;
			line-height: 1.8rem;
		}		
	}

	&__heading{
		color: $white;
		font-weight: $weight-bold;
		font-size: 3.625rem;
		margin-bottom: 7px;
		position: relative;
		z-index: 2;		
		text-align: center;
		@include breakpoint( medium only ) {		
			font-size: 3rem;
		}
		@include breakpoint( small only ) {		
			font-size: rem-calc(38);
		}
	}
}