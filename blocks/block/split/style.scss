// ======================================================
// Block Styles
// ============
.block_split {
	$block: &;
	padding-top: 50px;

	&__content{
		display: flex;
		padding: 50px 40px 40px 35px;
		
		@include breakpoint( small only) {
			padding: 30px 0 0;
		}
	}

	&__info {
		display: flex;
		flex-grow: 1;
		flex-direction: column;
		width: 100%;
	}

	&__link {
		margin: auto 0 0;
	}
	
	&__description{
		color: $text-color;
		font-weight: $weight-normal;
		font-size: 0.9375rem;
		line-height: 1.75rem;
		margin-bottom: 22px;
		width: 100%;

		@include breakpoint( medium only) {
			max-width: 800px;
		}
	}
	&__heading{
		margin-bottom: 15px;

		@include breakpoint( medium only) {
			max-width: 800px;
		}
	}
	
	&__information{
		text-align: center;
		margin: 0 0 60px;
		display: flex;
    	justify-content: center;

		@include breakpoint( medium down ) {
			margin: 0 auto 50px;
			flex-direction: column;
			align-items: center;
		}
	}

	&__information_item {
		display: inline-flex;
		color: $secondary-color;
		font-weight: $weight-extrabold;
		font-size: rem-calc(22);
		text-align: center;
		max-width: 100%;
		padding-left: 35px;
		padding-right: 35px;
		border: 1px solid $border-color;

		@include breakpoint( medium down ) {
			justify-content: center;
			min-width: 400px;
			width: 400px;
			max-width: 100%;
		}

		@include breakpoint( medium down ) {
			min-width: 0;
		}

		&:first-child {
			border-right: transparent;

			@include breakpoint( medium down ) {
				border-right: 1px solid $border-color;
				border-bottom: transparent;
			}
		}
	}

	&__image{			
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center center;

		@include breakpoint( small down ) {
			height: 180px;
		}
	}
	
	&__item{
		@include flexgrid($columns: 33% auto, $breakpoint : medium up);
		@include flexgrid($columns: 1 , $breakpoint : small down);
 
		@include breakpoint( small only) {
			padding-top: 20px;
		}
	}

	&__list{
		@include flexgrid($columns: 2, $breakpoint : large up);
		@include flexgrid($columns: 1, $breakpoint : medium down); 
		background: $background-1;

		@include breakpoint( small only) {
			padding-left: 25px;
			padding-right: 25px;
			padding-bottom: 40px;
			padding-top: 30px;
		}
	}
//	&__mail, &__phone{
//		align-items: center;
//		color: $secondary-color;
//		display: flex;
//		font-weight: $weight-extrabold;	
//		justify-content: center;
//		margin-bottom: 0;
//		padding-bottom: 13px;	
//		padding-top: 13px;
//		text-align: center;	
//		@include transitions();
		
//		&:hover {
//			color: darken( $secondary-color, 10% );
//		}
//	}
	
//	&__mail{
//		text-transform: inherit;
		
//		@include breakpoint( medium up ) {
//			border-right: 1px solid $border-color;
//		}
		
//		@include breakpoint( small only ) {
//			border-bottom: 1px solid $border-color;
//		}
		
//	}
	&__question{
		margin-bottom: 30px;

		p{
			font-size: 1.6875rem;
			line-height: 1.625rem;
			margin: auto;
			max-width: 938px;
			text-align: center;
			
			@include breakpoint( small only ) {				
				line-height:2rem;
			}

			a{
				@include transitions();
				&:hover {
					color: darken( $primary-color, 10% );
				}
			}
		}
	}
}