// ======================================================
// Block Styles
// ============
.block_resources_listing {
	$block: &;
	padding-top: 60px;
	padding-bottom: 60px;
	
	@include breakpoint( medium down ) {
		padding-bottom: 50px;
		padding-top: 50px;
	}
	
	&__button{
		margin-top: auto;
		margin-bottom: 0;
		@include transitions();	
	}
	
	&__category{
		color: $border-color;
		font-weight: $weight-extrabold;
		font-size: 0.9375rem;
		letter-spacing: 0.38px;
		margin-bottom: 4px;
		@include transitions();	
		
	}
	
	&__item{
		border: 1px solid $background-1;
		padding: 16px 20px;
		display: flex;
		flex-direction: column;
		@include transitions();	
		&:hover{
			background: $primary-color;
			border: 1px solid $background-1;
			
			.block_resources_listing__category, .block_resources_listing__title , .block_resources_listing__button{
				color: $white;
			}
			.block_resources_listing__category{
				opacity:0.8;
			}	
		}
	}
	
	&__list{
		@include flexgrid($columns: 4, $spacing:20px, $vertical-spacing:35px,$breakpoint : large up);
		@include flexgrid($columns: 2, $spacing:20px, $vertical-spacing:20px, $breakpoint : medium only);
		@include flexgrid($columns: 1, $spacing:20px, $breakpoint : small only);
		
	}
	.pagination_block{
		margin-top: 50px;
	}
	.pagination_block__arrow {
		display: none;
	}
	&__title{
		color: $headings-color;
		display: flex;
		font-weight: $weight-bold;
		font-size: 1.0625rem;
		line-height: 1.8125rem;
		margin-bottom: 7px;	
		@include transitions();
	}
}