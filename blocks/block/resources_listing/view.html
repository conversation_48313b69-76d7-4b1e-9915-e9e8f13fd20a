<% var block = options.block %>
<%- block._editable %>

<article class="block_resources_listing">
	<div class="container">
		<div class="block_resources_listing__list">
				<% var stories = plugins.stories({
					component: 'Resources Module',
					limit: '16',
					sort: 'asc',
					order_by: 'title',
					paginate: true,
					categoriesPages: true
					}, (resources) => { %>
						<a href="<%- resources.data.file %>" target="_blank" class="block_resources_listing__item">
							<p class="block_resources_listing__category">PDF - <% if (resources.data) { %><%- resources.data.file_size %><% } %></p>
							<p class="block_resources_listing__title"><%- resources.title.substring(0, 66) %></p>
							<p class="link__alternative block_resources_listing__button">download</p>
						</a>

					<% }, () => { %>

					<!-- Uh oh, we didn't find any news :( -->
					<p>Sorry, there currently are no initiatives in the database.</p>
					<% }) %>
		
		</div>
		   <!-- Pagination -->
		   <% if(stories.hasOwnProperty('pagination') && stories.pagination.pages.length > 1) { %>
			<%- plugins.include('snippets/pagination.html', { pagination: stories.pagination }) %>
		<% } %>
	</div>
</article>

 