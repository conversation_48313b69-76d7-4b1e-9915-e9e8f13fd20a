<% var block = options.block %>
<%- block._editable %>

<article class="block_text_image_left container">
	<div class="cover_image block_text_image_left__image">
		<img src="<%- plugins.img(block.image, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(block.image, {w:500}) %>" alt="<%- block.heading %>">
	</div>
	<div class="block_text_image_left__description">
		<% if (block.heading) { %>
			<h2 class="block_text_image_left__heading heading--h3"><%- block.heading %></h2>
		<% } %>
		<% if (block.description) { %>
			<%- plugins.markdownify(block.description) %>
		<% } %>
		<% if (block.button && block.button.length) { %>
			<div class="block_text_image_left__button">
				<%- plugins.blocks(block.button) %>
			</div>			
		<% } %>
	</div>
</article>