// ======================================================
// Block Styles
// ============
.block_text_image_left {
	$block: &;
	margin: 65px auto 65px;
	
	@include flexgrid($columns: auto 580px, $spacing: 45px, $vertical-align: center, $breakpoint: large up);
	@include flexgrid($columns: 1, $spacing: 15px, $vertical-align: center, $breakpoint: medium down);
	
	@include breakpoint( medium down ) { 
		margin: 45px auto 50px;
	}

	&__heading {
		margin-bottom: 15px;
	}

	&__button{
		margin-top: 25px;

		.button{
			margin-bottom: 0;
		}
	}

	&__description {

		@include breakpoint( medium down ) {
			text-align: center;
			margin-bottom: 30px;
		}
		p {
			margin-bottom: 12px;

			&:last-child {
				margin-bottom: 0;
			}
		}
	}

	&__image {
		height: 290px;

		@include breakpoint( medium down ) {
			order: 1;
			margin-top: 15px;
		}
		@include breakpoint( small down ) {
			height: 190px;
		}
	}
}