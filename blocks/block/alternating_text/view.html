<% var block = options.block %>
<%- block._editable %>

<article class="block_alternating_text">
	<div class="container">
		<% if(block.items) { %>
			<% block.items.forEach( (item, index) => { %>	
				<div class="block_alternating_text__item">
					<% if (item.image) { %>
						<div class="<% if (item.contain_image) { %>contain_image<% } else { %>cover_image<% } %> block_alternating_text__image">
							<img src="<%- plugins.img(item.image, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(item.image, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" alt="<%- item.heading %>">
						</div>
					<% } %>
					<div class="block_alternating_text__description">
						<% if (item.heading) { %>
							<h3 class="block_alternating_text__heading heading--h4"><%- item.heading %></h3>
						<% } %>
						<%- plugins.markdownify(item.description) %>
					</div>
				</div>		
			<% }); %>
		<% } %>
	</div>
</article>

 