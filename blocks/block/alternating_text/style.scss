// ======================================================
// Block Styles
// ============
.block_alternating_text {
	$block: &;
	padding-top: 65px;
	padding-bottom: 65px;
	
	@include breakpoint(medium down) {
		padding-top: 50px;
		padding-bottom: 50px;
	}
	
	&__description{
		margin-bottom: 0;

		p {
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
	
	&__item{
		@include flexgrid($columns: 1fr 1fr, $spacing: 45px, $breakpoint: large up);
		@include flexgrid($columns: 1fr, $spacing: 10px, $breakpoint: medium down);
		align-items: center;

		&:not(:last-child){
			margin-bottom: 55px;

			@include breakpoint( medium down) {
				margin-bottom: 35px;
			}
		}
		&:nth-child(even){
			.block_alternating_text__description{
				order: -1;
				margin-right: 45px;
			}
			.block_alternating_text__image{
				margin-right: 0;

				@include breakpoint( medium down) {
					order: -1;
				}
			}
			
			&:first-child{
				margin-bottom: 56px;
				
			}	
		}
		
	}	
		
		
	&__image{
		height: 293px;

		@include breakpoint( medium only) {
			height: 250px;
			margin-bottom: 15px;
		}

		@include breakpoint( small only) {
			height: 180px;
			margin-bottom: 15px;
		}
	}
}