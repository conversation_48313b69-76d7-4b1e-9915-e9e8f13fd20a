// ======================================================
// Block Styles
// ============
.block_service_toc {
	$block: &;
	padding-bottom: 70px;
	padding-top: 65px;
	
	@include breakpoint( medium down) {
		padding-bottom: 50px;
		padding-top: 45px;
	}
	
	&__description{
		max-width: 880px;
		margin: auto;
		margin-bottom: 28px;
		text-align: center;
	}
	
	&__heading{
		margin-bottom: 26px;
		padding-bottom: 20px;
		position: relative;
		text-align: center;
		
		&::after{
			background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
			bottom: 0;
			content: " ";
			height: 4px;
			left: 50%;
			position: absolute;
			transform: translate(-50%, -50%);
			width: 264px;			
		}
	}
	
	&__item{
		border: 1px solid $border-color;
		text-align: center;
		display: flex;
    	flex-direction: column;
	}
	
	&__item_button{
		margin-bottom: 0;
	}
	
	&__item_description{
		font-size: 0.9375rem;
		line-height: 1.75rem;
		margin-bottom: 16px;
	}
	
	&__item_image{
		height:149px;
	}
	
	&__item_heading{
		margin-bottom: 5px;
		text-align: center;

		@include breakpoint( medium up) {
			align-items: center;
			display: flex;
			justify-content: center;
			margin-bottom: 11px;	
		}
	}
	
	&__item_text{
		padding: 25px 25px 25px 25px;
		display: flex;
	    flex-direction: column;
	    flex-grow: 1;

		@include breakpoint( small only) {
			padding: 20px 15px;
		}
	}

	&__item_link {
		margin: auto 0 0;
	}
	
	&__list{
		@include flexgrid($columns: 3, $spacing: 35px, $breakpoint: large up);
		@include flexgrid($columns: 2, $spacing: 35px, $breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 35px, $breakpoint: small only);
		width: 1110px;
		max-width: 100%;
		margin-left: auto;
		margin-right: auto;
	}
}