<% var block = options.block %>
<%- block._editable %>

<article class="block_service_toc">
	<div class="container">
		<h2 class="block_service_toc__heading"><%- block.heading %></h2>
		<p class="block_service_toc__description"><%- block.description %></p>

		<div class="block_service_toc__list">
			<% if(block.items) { %>
				<% block.items.forEach( (item, index) => { %>	
					<!-- toc item -->
					<div class="block_service_toc__item">
						<div class="block_service_toc__item_image cover_image">
						<% if(item.image) {
							var image = item.image;
						} else {
							var image = site.settings.news_image_placeholder;
						} %>                                   
							<img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 600, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- item.heading %>">
						</div>						
						
						<div class="block_service_toc__item_text"> 
							<h3 class="block_service_toc__item_heading heading--h6"><%- item.heading %></h3>
							<% if (item.description) { %>
								<p class="block_service_toc__item_description"><%- item.description.substring(0,81) %></p>			
							<% } %>
							<a href="/<%- item.link.cached_url %>" class="link__alternative  block_service_toc__item_link">Find out more</a>			
						</div>
					</div>
					<!-- end toc item -->
				<% }) %>
			<% } %>
		</div>
	</div>
</article>

 