// ======================================================
// Block Styles
// ============
.block_sitemap {
	max-width: 880px;
    margin-top: 50px !important;
    margin-bottom: 80px !important;
    margin: auto;
    
	.sitemap {
    	list-style-type: none;
	    padding: 0;
	    margin: 0;
    }
    
    &__heading{
        text-align: center;
    }
	
	ul {
		margin-left: 0;
		

		li {
			list-style-type: none;
			position: relative;
			display: block;
			margin-bottom: 10px !important;
	        padding-left: 0;
			line-height: 1.4;

	        &:before {
	        	display: none;
	        }

			a, span.disabled_link {
				background: $primary-color;
				border: 1px solid $primary-color;
				border-radius: 6px;
				color: $white;
				display: block;
				padding: 13px 45px 13px;
	            position: relative;
	            @include transitions();

				&:before{
			        position: absolute;
			        font-family: "icomoon";
			        content: "\f0f6";
			        color: $white;
			        top: 15px;
				    left: 20px;
				    font-size: 0.8em;
				}

				&:hover{
					color: $white;
					background: darken($primary-color, 5%);
					border: 1px solid darken($primary-color, 5%);
					&:before{
						color: $white;
					}
				}
			}

			ul {
				margin-left: 25px;
				margin-top: 10px;
				margin-bottom: 0;
				
				@include breakpoint(medium down) {
					padding:0;
				}
				li {

					a {
						font-size: 0.9em !important;
						background: $white;
						color: $text-color;
						border: 1px solid $border-color;

						&:hover {
							color: $white !important;
							background: $primary-color;
							border: 1px solid $primary-color;
						}

						&:before{
							top: 15px !important;
							content: "\f078";
							color: $text-color;
						}
					}
				}
			}
		}
	}

	span{
		font-weight: $weight-bold;
	}
}

.disabled_link {
	display: none;
}