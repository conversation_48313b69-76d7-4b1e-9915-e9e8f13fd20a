<% var block = options.block %>
<%- block._editable %>

<div class="block_home_video">
	<div class="container">
		
		<% if(block.heading) { %>
			<h2 class="block_home_what_we_do__heading"><%- block.heading %></h2>
		<% } %>

		<% if(block.block_style == "video") { %>

			<div <% if(block.video_url) { %>data-lightbox-video="<%- block.video_url %>"<% } %> class="block_home_video__wrap <% if(block.video_url) { %>block_home_video__wrap--link<% } %>">
				<% if(block.video_url) { %>
					<img src="/assets/images/design/watchvid.svg" class="block_home_video__play" alt="Play Video">
				<% } %>
				<% if(block.video_file.filename) { %> 
					<video autoplay loop muted preload="yes" class="cover_image"> 
						<source src="<%- block.video_file.filename %>" type="video/mp4"  /> 
						Your browser does not support the video tag. I suggest you upgrade your browser.
					</video>
				<% } %>
			</div>
		
		<% } else { %>

			<div <% if(block.video_url) { %>data-lightbox-video="<%- block.video_url %>"<% } %> class="block_home_video__wrap <% if(block.video_url) { %>block_home_video__wrap--link<% } %>">
				<% if(block.video_url) { %>
					<img src="/assets/images/design/watchvid.svg" class="block_home_video__play" alt="Play Video">
				<% } %>
				<% if(block.image) { %> 
					<div class="block_home_video__image cover_image">
						<img class="block_home_video__image" src="<%- plugins.img(block.image, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(block.image, { q:85 , w:1000 , lossless: 1, auto: 'format' }) %>" <% if(block.heading) { %>alt="<%- block.heading %>"<% } %>>
					</div>
				<% } %>
			</div>

		<% } %>

	</div>
</div>