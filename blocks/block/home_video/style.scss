// ======================================================
// Block Styles
// ============
.block_home_video {
	$block: &;
	padding-top: 55px;
	padding-bottom: 70px;
	text-align: center;
	position: relative;

	@include breakpoint( medium down) {
		padding-top: 45px;
		padding-bottom: 50px;
	}

	&:after {
		height: 160px;
		width: 100%;
		background: $primary-color;
		display: block;
		content: "";
		position: absolute;
		left: 0;
		bottom: 0;
		z-index: 1;
	}

	&__wrap {
		position: relative;
		margin-top: 40px;
		z-index: 2;
		width: 1000px;
		max-width: 100%;
		margin-left: auto;
		margin-right: auto;
		
		&--link {
			cursor: pointer;

			&:hover {
				&:after {
					background: rgba(139,139,139,0.5);
				}
			}
		}
		
		&:after {
			@include transitions();
			background: rgba(139,139,139,0.35);
			display: block;
			content: "";
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
		}
		video {
			width: 100%;
			display: block;
			cursor: pointer;
		}
	}

	&__image {
		height: 540px;

		@include breakpoint(medium down) {
			height: 400px;
		}

		@include breakpoint(small down) {
			height: 250px;
		}
	}

	&__play { 
		@include horizontal-vertical-align();
		z-index: 2;
		

		@include ie {
			
		}
	}
}