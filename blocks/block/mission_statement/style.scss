// ======================================================
// Block Styles
// ============
.block_mission_statement {
	$block: &;
	
	&__content{
		padding: 45px 40px 40px;
		position: relative;
		
		&::after{
			position: absolute;
			content: " ";
			width: 100%;
			height: 100%;
			top:0;
			left: 0;
			background: url(/assets/images/design/cta_bg.png);
			background-repeat: repeat;
			background-size: contain;
			z-index: 1;
		}
		
		@include breakpoint( medium only) {
			
		}
		
		@include breakpoint( small only) {
			
		}
		
	}
	
	&__description{
		color: $white;
		font-size: 1.1875rem;
		font-style: italic;
		line-height: 2.3125rem;
		max-width: 880px;
		margin: auto;
		margin-bottom: 0;
		text-align: center;
		position: relative;
		z-index: 2;
	}
	&__heading{
		color: $white;
		margin-bottom: 15px;
		text-align: center;
		position: relative;
		z-index: 2;
	}
}