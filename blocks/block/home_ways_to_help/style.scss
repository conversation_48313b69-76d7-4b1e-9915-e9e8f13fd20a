// ======================================================
// Block Styles
// ============
.block_home_ways_to_help {
	$block: &;
	overflow: hidden;
	background: $background-1;
	position: relative;
	
	@include breakpoint( medium down) {
	    display: flex;
    	flex-direction: column;
    }
	
	>.container {
		order: 1;

		@include breakpoint( medium down) {
			width: 100%;
		}
	}

	&__buttons{
		.button:nth-child(2){
			@include breakpoint( medium up) {		
				margin-left: 19px;
			}	
		}
		.button{
			@include breakpoint( medium up) {			
				margin-bottom: 0;
			}	
		}
		.link__alternative{
			display: block;
			color: $headings-color;
			font-style: italic;
			font-weight: $weight-extrabold;
			letter-spacing: 0.4px; 
			@include breakpoint( medium up) {	
				margin-top: 30px;
			}
			&:after {
				font-style: initial;
			}
			&:hover {
				color: lighten($headings-color, 30%);
			}	
		}
	}

	&__wrap {
		@include flexgrid($columns: auto 58%, $breakpoint: large up);
		@include flexgrid($columns: 1, $breakpoint: medium down);
	}
	
	&__content{
		position: relative;
		padding-top: 65px;
		padding-bottom: 60px;
		@include breakpoint( large up) {	
//			padding: 65px;
		}

		@include breakpoint( medium down) {	
			text-align: center;
			order: -1;
			max-width: 800px;
			padding-left: 25px;
    		padding-right: 25px;
    		padding-top: 45px;
    		padding-bottom: 40px;
    		margin-left: auto;
    		margin-right: auto;
		}

		@include breakpoint( small only) {	
			
		}	
		
		&::after{
			@include breakpoint( large up) {
				background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
				content: " ";
				height: 4px;
				position: absolute;
				right: -218px;
				top: 88px;
				transform: translate(-50%, -50%);
				width: 50%;
			}	
		}
	}
	
	&__description{
		margin-bottom: 25px;

		@include breakpoint( large up) {
//			max-width: 670px;
		}
		@include breakpoint( medium down) {
			max-width: 800px;
			margin-left: auto;
			margin-right: auto;
		}
	}
	
	&__images{
		@include flexgrid($columns: 2, $breakpoint: small up);
		position: absolute;
		top: 0;
		left: 0;
		width: 38%;
		height: 100%;

		@include breakpoint( medium down) {		
			height:300px;
			position: relative;
    		width: 100%;
    		order: 2;
		}
		
	}
	
	&__heading{
		margin-bottom: 15px;
		position: relative;
		display: inline-block;
	    z-index: 1;
	    background: $background-1;
	    
		@include breakpoint(large up) {	
			padding-right: 40px;
		}
		
		@include breakpoint(medium down) {	
			padding-bottom: 20px;
			margin-bottom: 25px;

			&::after{
				background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
				bottom: 0;
				content: " ";
				height: 4px;
				left: 50%;
				position: absolute;
				transform: translate(-50%, -50%);
				width: 264px;			
			}

		}
	}
	
	.help_images_first{
		@include flexgrid($columns: 1, $breakpoint: small up);
	}
	.help_images_second__image3{
		height: 100%;
		width: 100%;
	}
	.help_images_first__image1, .help_images_first__image2, .help_images_second__image3{
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center center;
	}
}