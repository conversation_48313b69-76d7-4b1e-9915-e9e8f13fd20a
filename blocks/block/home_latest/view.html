<% var block = options.block %>
<%- block._editable %>

<article class="block_home_latest">
	<div class="container">
		<h2 class="block_home_latest__heading"><%- block.heading %></h2>
		<div class="block_home_latest__list">
			
			<!-- if client has chosen news in cms -->
			<% var entries_ids = []; %>
				
			<% if(block.entries.ids.length > 0) { %>
				<% plugins.relationship(block.entries, (news, index) => { %>
					
					<!--News card-->
					<a href="<%- news.url %>" class="item_card item_card--news">
						<div class="item_card__image cover_image">
							<% if(news.data.featured_image) {
								var image = news.data.featured_image;
							} else {
								var image = site.settings.news_image_placeholder;
							} %>                                   
							<img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- news.title %>">
							<% if(news.data.date) { %>
								<div class="item_card__image_date"><%- plugins.formatDate(news.data.date, 'DD MMMM') %></div>
							<% } %>
						</div>						
						
						<div class="item_card__text"> 
							<% if(news.published_at && !news.data.date) { %>
								<p class="item_card__date">
									<%- plugins.formatDate(news.published_at, 'DD MMM YYYY') %>
								</p>
							<% } %>
							<% if(news.data.excerpt) { %>
							<p class="item_card__excerpt"><%- news.data.excerpt.substring(0,75) %></p>			
							<% } %>
							<p class="link__alternative link__alternative--red item_card__link">
								<% if(news.data.component == "Event Module") { %>
									Find Out more
								<% } else if(news.data.component == "News Module") { %>
									Read more
								<% } %>
							</p>			
						</div>			
					</a>                            
					<!--End Card-->
				<% }); %>
			<% } %>

			<!-- if the clients didn't choose any news -->
			<% if(block.entries.ids.length < 4) { 
			var length = block.entries.ids.length;
			
			    var stories = plugins.stories({
			    sort: 'desc',
				order_by: 'data.date',
				 where: entry => !block.entries.ids.includes(entry.uuid) && ['News Module', 'Event Module'].includes(entry.data.component),
				limit: 4 - length
			}, (entry) => { %>
		
				<!--News card-->
				<a href="<%- entry.url %>" class="item_card item_card--news">

					<div class="item_card__image cover_image">            
						<img srcset="<%- plugins.imgSrcSet(entry.data.featured_image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(entry.data.featured_image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- entry.title %>">
						<% if(entry.data.date) { %>
							<div class="item_card__image_date"><%- plugins.formatDate(entry.data.date, 'DD MMMM') %></div>
						<% } %>
					</div>						
							
					<div class="item_card__text"> 
						<% if(entry.data.date) { %>
							<p class="item_card__date">
								<%- plugins.formatDate(entry.data.date, 'DD MMM YYYY') %>
							</p>
						<% } %>
						<% if(entry.data.excerpt) { %>
						<p class="item_card__excerpt"><%- entry.data.excerpt.substring(0,75) %></p>			
						<% } %>
						<p class="link__alternative link__alternative--red item_card__link">
							<% if(entry.data.component == "Event Module") { %>
								Find Out more
							<% } else if(entry.data.component == "News Module") { %>
								Read more
							<% } %>
						</p>			
					</div>
								
				</a>                            
				<!--End Card-->
								
				<% }, () => { %>
									
				<!-- Uh oh, we didn't find any news :( -->
				<div>
					<p>Sorry, there's currently no news in the database.</p>
				</div>
										
			<% }) %>
			<% } %>	

		</div>

	</div>
</article>