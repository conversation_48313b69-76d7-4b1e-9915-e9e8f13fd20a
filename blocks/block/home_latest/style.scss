// ======================================================
// Block Styles
// ============
.block_home_latest {
	$block: &;
	padding-top: 55px;
	padding-bottom: 80px;
	
	@include breakpoint( medium down) {
		padding-top: 45px;
		padding-bottom: 50px;
	}
	&__heading{
		text-align: center;
		margin-bottom: 49px;
		padding-bottom: 24px;
		position: relative;
		
		&::after{
			background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
			bottom: 0;
			content: "";
			height: 4px;
			left: 50%;
			position: absolute;
			transform: translate(-50%, -50%);
			width: 264px;			
		}
	}
	
	&__list{
		@include grid($columns: 1fr 1fr 1fr 1fr, $spacing :18px, $breakpoint: large up);
		@include grid($columns: 1fr 1fr , $spacing :18px, $breakpoint: medium only);
		@include grid($columns: 1fr , $spacing :25px, $breakpoint: small only);
	}
	
	.item_card {
		display: flex;
		flex-direction: column;
		flex-grow: 1;

		&__image{
			height: 168px;
			position: relative;
		}

		&__image_date {
			background: $primary-color;
			bottom: 0;
			color: $white;
			font-size: 0.875rem;
			font-weight: $weight-extrabold;
			letter-spacing: 0.35px;
			line-height: 1.1875rem;
			padding: 4px 20px 4px 15px;
			position: absolute;
			right: 0;
			text-transform: uppercase;
		}
		
		&__text{
			background: $background-4;
			border: 1px solid $background-1;
			display: flex;
			flex-grow: 1;
			flex-direction: column;
			min-height: 173px;
			padding: 16px 21px;
		}

		&__date {
			color: #9fa4a6;
			font-weight: $weight-extrabold;
			font-size: 0.875rem;
			letter-spacing: 0.35px;
			line-height: 1.1875rem;
			margin-bottom: 0;
			text-transform: uppercase;
		}
		
		&__excerpt{
			color: $headings-color;
			font-weight: $weight-bold;
			font-size: 1.125rem;
			line-height: 1.9375rem;
			margin-bottom: 14px;		
		}

		&__link{
			margin-top: auto;
			margin-bottom: 0;
		}
	}
}