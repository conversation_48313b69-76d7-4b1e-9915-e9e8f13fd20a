// ======================================================
// Block Styles
// ============
.block_family_support {
	$block: &;
	position: relative;
	background: url(/assets/images/design/family_support_bg.png);
	background-position: center center;
	background-repeat: repeat;
	background-size: 1440px 521px;
	
	&__wrap {
		@include flexgrid($columns: auto 66%, $breakpoint: large up);
		@include flexgrid($columns: 1, $breakpoint: medium down);
		padding-top: 50px;
		padding-bottom: 30px;

		@include breakpoint( medium down) {
			padding-top: 40px;
			padding-bottom: 40px;
		}
	}
	&__content{
		position: relative;
		
		@include breakpoint(large up) {
//			padding: 50px 60px 35px 60px;
		}
	
		

		li{
			color: $white;
			counter-increment: item;
//			font-size: 0.9375rem;
			margin-bottom: 18px;
			line-height: 1.625rem;
			padding-left: 61px;
			position: relative;
			
			@include breakpoint( large up) {
				max-width: 745px;
			}
			
			&::before{
				align-items: center;
				background: $white;
				border-radius:2.4px;
				color: $secondary-color;
				content: counter(item);
				display: flex;
				font-weight: $weight-extrabold;
				font-size: 1.25rem;	
				height:36px;
				justify-content: center;	
				left: 0;		
				position: absolute;
				top: 50%;
				transform: translate(0%, -50%);
				width:36px;
			}
		}	

		ol{
			counter-reset: item;
			list-style: none;
			position: relative;
			z-index: 2;	
			
			@include breakpoint( medium only) {
				max-width: 800px;
				margin: auto;
			}
		}
		
		p{
			color: $white;
			position: relative;
			z-index: 2;

			a {
				color: $white;

				&:hover {
					opacity: 0.5;
				}
			}
			@include breakpoint( large up) {
				max-width: 745px;
			}
			@include breakpoint( medium only) {
				max-width: 800px;
			}
		}
	}
	
	&__content_head{
		align-items: center;
		display: flex;
		justify-content: space-between;
		margin-bottom: 15px;
		position: relative;
		z-index: 2;

		@include breakpoint( large up) {
			max-width: 745px;
		}
		@include breakpoint( medium only) {
		
		}
		@include breakpoint( small only) {
			flex-direction: column;
			justify-content: left;
			align-items: self-end;
		}
	}
	
	&__content_pdf{
		padding-left: 29px;
		position: relative;
		top: -1px;

		&::before{
			background: url(/assets/images/flaticons/004-pdf-white.svg);
			background-position: center center;
			background-repeat: no-repeat;
			background-size: cover;
			content: " ";
			height:24px;
			left:0;
			position: absolute;
			top: 50%;
			transform: translate(0%, -50%);
			width:18px;
		}
		
		p{
			color: $white;
			font-weight: $weight-extrabold;
			letter-spacing: 0.4px;
			margin-bottom: 0;
			text-transform: uppercase;
			@include transitions();
			
			&:hover {
				color: darken( $white, 10% );
			}
			
		}
	}

	&__heading{
		color: $white;
		margin: 0;
		@include breakpoint( small only) {
			margin-bottom: 10px;
		}
	}
	
	&__image{
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		position: absolute;
		top: 0;
		left: 0;
		width: 33%;
		height: 100%;

		@include breakpoint( medium only) {
			height: 300px;
			width: 100%;
			position: relative;
		}
		
		@include breakpoint( small only) {
			height: 180px;
			width: 100%;
			position: relative;
		}
	}
	
	
}