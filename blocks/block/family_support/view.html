<% var block = options.block %>
<%- block._editable %>

<article class="block_family_support">
	<div class="block_family_support__image" style="background-image: url('<%- plugins.img(block.image, { q: 85, w: 1200, lossless: 1, auto: 'format' }) %>');"></div>
	<div class="container">
		<div class="block_family_support__wrap">
			<div class="block_family_support__spacing"></div>
			<div class="block_family_support__content">
				<div class="block_family_support__content_head">
					<h2 class="block_family_support__heading heading--h3"><%- block.heading %></h2>
					<% if (block.file) { %>
						<a href="<%- block.file %>" target="_blank" class="block_family_support__content_pdf"><p>download pdf</p></a>
					<% } %>
					</div>
				<% if (block.content) { %>
					<%- plugins.markdownify(block.content) %>
				<% } %>
			</div>
		</div>
		
	</div>
	
</article>

 