// ======================================================
// Block Styles
// ============

// avoiding homepage
body:not(.home){
	.block_call_to_action{
		margin-bottom: 0 !important;
	}
}

.background--blue{
	background-color: $primary-color;
}

.background--red{
	position: relative;

	&::after{
		position: absolute;
		content: " ";
		width: 100%;
		height: 100%;
		top:0;
		left: 0;
		background: url(/assets/images/design/cta_bg.png);
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		z-index: 0;
	}
}

.block_call_to_action {
	padding-top: 35px;
	padding-bottom: 35px;

	@include breakpoint(medium down){
		text-align: center;
		padding-bottom: 45px;
	}
	
	@include breakpoint(small only) {
		padding-top: 40px;
	}

	&__button {
		font-size: rem-calc(16);
		margin: 0;
		min-width: 195px;

		a{
			margin-bottom: 0;
		}
		@include breakpoint(medium only){
			width: 100%;
			margin-top: 17px;
		}
	}

	.container{
		z-index: 1;
		position: relative;
	}

	&__wrap {
		align-items: center;
		display: flex;
		justify-content: space-between;
		position: relative;
		
		@include breakpoint(medium down){
			flex-direction: column;
		}
	}
	
	&__text{
		padding-right: 40px;

		@include breakpoint(medium down){
			padding-right: 0;
			width: 100%;
		}
	}
	&__description{
		color: $white;
		margin-bottom: 0;
		font-weight: $weight-normal;
		line-height: 1.9;

		@include breakpoint(medium down){
			width: 100%;
		}
	}
	
	&__heading {
		color: $white;
		line-height: 1.5;
		margin-bottom: 0;
		
		@include breakpoint(large down){
			
		}
	}

	&__text{
		@include breakpoint(small only){
			margin-bottom: 20px;
			text-align: center;
		}
	}
	
}