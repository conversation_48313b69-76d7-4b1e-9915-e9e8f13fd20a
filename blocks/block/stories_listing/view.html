<% var block = options.block %>
<%- block._editable %>

<article class="block_stories_listing">
	<div class="container">
		<p class="block_stories_listing__introduction"><%- block.intro %></p>

		<div class="block_stories_listing__list">

			<% var stories = plugins.stories({
				component: 'Stories Module',
				limit: 9,
				sort: 'asc',
				order_by: 'title',
				paginate: true,
				categoriesPages: true
			}, (story) => { %>
			<!--Stories card-->
			<a class="block_stories_listing__item" <% if(story.data && story.data.featured_video_url) { %>data-lightbox-video="<%- story.data.featured_video_url %>"<% } else { %>href="<%- story.url %>" <% } %>>
				<div class="cover_image block_stories_listing__item_image <% if (story.data && story.data.featured_video_url){ %>block_stories_listing__item_video<% } %>">
					<% if(story.data.featured_image) {
						var image = story.data.featured_image;
					} else {
						var image = site.settings.news_fallback_image;
					} %>
						<img srcset="<%- plugins.imgSrcSet(image, { q: 80, w: 1000, lossless: 1, auto: 'format' }) %>"  src="<%- plugins.img(image, { q: 85, w: 800, lossless: 1, auto: 'format' }) %>" alt="<%- story.title %>">
					<% if(story.data && story.data.featured_video_url) { %>
						<div class="block_stories_listing__item_sticker">new video</div>
					<% } %>
				</div>
				<div class="block_stories_listing__item_content">
					<h6 class="block_stories_listing__item_heading">
						<%- story.title %>
					</h6>
					<% if(story.data.excerpt) { %>
						<p class="block_stories_listing__item_description">
							<%- story.data.excerpt.substring(0,147)+"." %>
						</p>
					<% } %>
				</div>
				<span class="link__alternative link__alternative--red block_stories_listing__item_link"><% if (story.data && story.data.featured_video_url){ %>Watch video <% } else { %> Read More<% } %></span>
			</a>

			<% }, () => { %>
				<!-- Uh oh, we didn't find any news :( -->
				<p>Sorry, there's currently no news in the database.</p>
			<% }) %>
		</div>
		<!-- Pagination -->
		<!-- Pagination -->
        <% if(stories.hasOwnProperty('pagination') && stories.pagination.pages.length > 1) { %>
			<%- plugins.include('snippets/pagination.html', { pagination: stories.pagination }) %>
		<% } %>
	</div>
</article>

 