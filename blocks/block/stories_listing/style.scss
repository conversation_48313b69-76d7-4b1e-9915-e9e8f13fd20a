// ======================================================
// Block Styles
// ============
.block_stories_listing {
	$block: &;
	padding-bottom: 73px;
	padding-top: 43px;
	
	&__introduction{
		margin: auto;
		margin-bottom: 37px;
		max-width: 880px;
		text-align: center;
	}
	
	&__item{
		&:hover{
			.block_stories_listing__item_link{
				color: rgba($secondary-color, 0.8);
			}
		}
	}
	
	&__item_content{
		padding-top: 15px;
	}
	
	&__item_description{
		font-size: 0.9375rem;
		font-weight: $weight-normal;
		line-height: 1.75rem;
		margin-bottom: 14px;
		
	}

	&__item_heading{
		margin-bottom: 5px;
		line-height: 1.35em;
	}

	&__item_image{
		height: 270px;
		position: relative;
		
		@include breakpoint( medium down) {
			height: 180px;
		}
	}

	&__item_link{
		cursor: pointer;
	}

	&__item_sticker{
		background: $primary-color;
		bottom: 32px;
		color: $white;
		font-weight: $weight-extrabold;
		font-size: 0.8125rem;
		padding: 2px 17px 0;	
		position: absolute;
		right: 0;
		text-transform: uppercase;
		z-index: 2;
		
	}

	&__item_video{	
		cursor: pointer;
		position: relative;	

		&:hover{
				&::after{
					opacity: 0.8;				
			}
		}

		&::before{
			background: $black;
			content: " ";
			height: 100%;
			opacity: 0.2;
			position: absolute;
			width: 100%;
			z-index: 1;
		}

		&::after{
			content: " ";
			background-image: url(/assets/images/flaticons/circle.svg);
			height:50px;
			left: 50%;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);
			width:50px;
			z-index: 2;
			@include transitions();

			
		}
	}
	
	&__list{
		@include grid($columns: 1fr 1fr 1fr, $spacing: 65px, $vertical-spacing: 45px, $breakpoint: large up);
		@include grid($columns: 1fr 1fr, $spacing: 35px, $vertical-spacing: 45px, $breakpoint: medium only);
		@include grid($columns: 1fr, $spacing: 25px, $breakpoint: small down);
		margin-bottom: 59px;		
	}

	.pagination_block__arrow {
		display: none;
	}
}