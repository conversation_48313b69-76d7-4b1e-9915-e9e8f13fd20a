// ======================================================
// Block Styles
// ============
.block_home_what_we_do {
	$block: &;
	padding-top: 55px;
	padding-bottom: 55px;
	text-align: center;
	
	@include breakpoint( medium down) {
		padding-top: 45px;
		padding-bottom: 40px;
	}

	&__heading{
		margin-bottom: 25px;
		padding-bottom: 20px;
		position: relative;
		
		&::after{
			background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
			bottom: 0;
			content: " ";
			height: 4px;
			left: 50%;
			position: absolute;
			transform: translate(-50%, -50%);
			width: 264px;			
		}
	}
	
	&__description{
		max-width: 880px;
		margin: auto;
		margin-bottom: 39px;
	}

	&__list{
		@include flexgrid($columns: 3, $spacing: 45px);
		@include flexgrid($columns: 2, $spacing: 45px, $breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 30px, $breakpoint: small only);
		justify-content: center;
		max-width: 100%;
		width: 1000px;
		margin-left: auto;
		margin-right: auto;
	}
	
	&__item{
		position: relative;
		overflow: hidden;

		&:hover{
			
			// .block_home_what_we_do__item_icon {
			// 	opacity: 0;
			// }

			.block_home_what_we_do__item_icon_hover {
				opacity: 1;
			}

			.block_home_what_we_do__item_button {

				@include breakpoint( large up) {
					bottom: -10px;
				}				
			}			
		}
	}

	&__item_icon {
		height: 55px;
		width: 100%;
		margin-bottom: 20px;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: contain;
		transition: opacity 0.25s ease-out;
		-moz-transition: opacity 0.25s ease-out;
		-webkit-transition: opacity 0.25s ease-out;
		-o-transition: opacity 0.25s ease-out;

		&.doctor{
			background-image: url(/assets/images/flaticons/007-doctor.svg);		
		}
		&.home{
			background-image: url(/assets/images/flaticons/006-home.svg);
		}
		&.kids{
			background-image: url(/assets/images/flaticons/008-children.svg);
		}
		&.hat{
			background-image: url(/assets/images/flaticons/009-mortarboard.svg);
		}
		&.pool{
			background-image: url(/assets/images/flaticons/010-swimming-pool.svg);
		}
		&.kettlebell{
			background-image: url(/assets/images/flaticons/011-kettlebell.svg);
		}
		&.id{
			background-image: url(/assets/images/flaticons/012-id.svg);	
		}
		&.tower{
			background-image: url(/assets/images/flaticons/electricity-tower.svg);	
		}
		&.parental{
			background-image: url(/assets/images/flaticons/001-parental.svg);	
		}
		&.clock{
			background-image: url(/assets/images/flaticons/002-clock.svg);	
		}
		&.education{
			background-image: url(/assets/images/flaticons/003-education.svg);	
		}
		&.salary{
			background-image: url(/assets/images/flaticons/004-salary.svg);	
		}
		&.learning{
			background-image: url(/assets/images/flaticons/005-learning.svg);	
		}
		&.goal{
			background-image: url(/assets/images/flaticons/006-goal.svg);	
		}
	}

	// &__item_icon_hover {
	// 	position: absolute;
	// 	height: 55px;
	// 	width: 100%;
	// 	margin-bottom: 20px;
	// 	background-position: center center;
	// 	background-repeat: no-repeat;
	// 	background-size: contain;
	// 	opacity: 0;
	// 	top: 0;
	// 	transition: opacity 0.25s ease-out;
	// 	-moz-transition: opacity 0.25s ease-out;
	// 	-webkit-transition: opacity 0.25s ease-out;
	// 	-o-transition: opacity 0.25s ease-out;
		
	// 	&.home{
	// 		background-image: url(/assets/images/flaticons/006-home-red.svg);
	// 	}
	// 	&.kids{
	// 		background-image: url(/assets/images/flaticons/008-children-red.svg);
	// 	}
	// 	&.hat{
	// 		background-image: url(/assets/images/flaticons/009-mortarboard-red.svg);
	// 	}
	// 	&.doctor{
	// 		background-image: url(/assets/images/flaticons/007-doctor-red.svg);
	// 	}
	// 	&.pool{
	// 		background-image: url(/assets/images/flaticons/swimming-pool-red.svg);
	// 	}
	// 	&.kettlebell{
	// 		background-image: url(/assets/images/flaticons/kettlebell-red.svg);
	// 	}
	// 	&.id{
	// 		background-image: url(/assets/images/flaticons/id-card-red.svg);
	// 	}
	// 	&.tower{
	// 		background-image: url(/assets/images/flaticons/electricity-tower-red.svg);	
	// 	}
	// 	&.parental{
	// 		background-image: url(/assets/images/flaticons/001-parental.svg);	
	// 	}
	// 	&.clock{
	// 		background-image: url(/assets/images/flaticons/002-clock.svg);	
	// 	}
	// 	&.education{
	// 		background-image: url(/assets/images/flaticons/003-education.svg);	
	// 	}
	// 	&.salary{
	// 		background-image: url(/assets/images/flaticons/004-salary.svg);	
	// 	}
	// 	&.learning{
	// 		background-image: url(/assets/images/flaticons/005-learning.svg);	
	// 	}
	// 	&.goal{
	// 		background-image: url(/assets/images/flaticons/006-goal.svg);	
	// 	}
	// }

	&__item_heading{
		color: $headings-color;
		font-size: 1.1875rem;
		font-weight: $weight-bold;
		line-height: 1.6875rem;
		margin-bottom: 10px;
	}
	
	&__item_description{
		font-weight: $weight-normal;
		font-size: 0.9375rem;
		line-height: 1.75rem;
		width: 100%;

		@include breakpoint( large up) {
			margin-bottom: 30px;
		}
	} 
	
	&__item_button{
		width: 100%;

		@include breakpoint( large up) {		
			position: absolute;
			bottom: -100px;
			left: 0;
			@include transitions();
		}
		@include breakpoint( medium down) {
			margin: auto 0 0;
		}
	}
}