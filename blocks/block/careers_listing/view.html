<article class="row block_careers_listing">

    <% var stories = plugins.stories({
        component: 'Careers Module',
        limit: 1,
        sort: 'asc',
        order_by: 'title',
        paginate: true,
        categoriesPages: true
    }, (careers) => { %>

        <!-- We got some results, list the careers! -->
        <div class="columns small-12 careers-item">
            <h4><%= careers.title %></h4>
            <p><%= careers.data.excerpt %></p>
            <a href="<%= careers.url %>" class="button">Read More</a>
            <a href="<%= careers.previous.url %>" class="button">Previous Careers (<%= careers.previous.title %>)</a>
            <a href="<%= careers.next.url %>" class="button">Next Careers (<%= careers.next.title %>)</a>
        </div>

    <% }, () => { %>

        <!-- Uh oh, we didn't find any careers :( -->
        <div class="columns small-12">
            <p>Sorry, there's currently no careers in the database.</p>
        </div>

    <% }) %>

    <!-- Pagination -->
    <%- plugins.include('snippets/pagination.html', { pagination: stories.pagination }) %>

</article>