// ======================================================
// Block Styles
// ============
.block_fundraising_aims {
	$block: &;
	padding-top: 70px;
	padding-bottom: 60px;
	
	@include breakpoint( medium down ) {
		padding-top: 50px; 
		padding-bottom: 45px;
	}

	&__aims_item{
		background: $background-1;
		border: 1px solid $border-color;
		border-radius:2.16px;		
		color: $headings-color;
		font-weight: $weight-bold;
		font-size: 1.3125rem;
		padding: 19px;
		position: relative;
		text-transform: uppercase;

		&:first-child{
			border-left:9px solid $primary-color;
		}
		&:nth-child(2){
			border-left:9px solid $tertiary-color;
		}

		&:last-of-type{
			border-left:9px solid $secondary-color;
		}	
	}

	&__aims_list{
		justify-content: center;
		@include flexgrid($columns: 3, $spacing: 25px, $breakpoint : large up);
		@include flexgrid($columns: 2, $spacing: 25px, $breakpoint : medium only);
		@include flexgrid($columns: 1, $spacing: 25px, $breakpoint : small only);
		
	}

	&__description{
		margin-bottom: 0;
	}
	
	&__feature{
		@include grid($columns: 1fr 1fr, $spacing: 53px, $breakpoint : large up);
		@include grid($columns: 1fr, $spacing: 20px, $breakpoint : medium down);
		align-items: center;
		margin-bottom: 60px;

		@include breakpoint( medium only) {		
			margin-bottom: 30px;
		}
		@include breakpoint( small only) {		
			margin-bottom: 30px;
		}
	}
	
	&__feature_image{
		height: 293px;

		@include breakpoint( small only ) {
			height: 180px;
		}
		img{
			width: 100%;
			height: 100%;
		}
	}
		
	&__feature_text{
		@include breakpoint( small only ) {
			
		}
	}

	.featured_video{	
		cursor: pointer;
		position: relative;	

		&:hover{
				&::after{
					opacity: 0.8;				
			}
		}

		&::before{
			background: $black;
			content: " ";
			height: 100%;
			opacity: 0.2;
			position: absolute;
			width: 100%;
			z-index: 1;
		}

		&::after{
			content: " ";
			background-image: url(/assets/images/flaticons/circle.svg);
			height:55px;
			left: 50%;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);
			width:55px;
			z-index: 2;
			@include transitions();

			@include breakpoint( small only ) {
				height: 45px;
				width: 45px;
			}
		}
	}
	
	&__heading{
		margin-bottom: 12px;
	}
	
}