<% var block = options.block %>
<%- block._editable %>

<article class="block_featured">


	<div class="container">
		<div class="block_featured__grid">
		<% if(block.featured) { %>
			<% block.featured.forEach( (item, index) => { %>	
				<div class="block_featured__item">
					
					<% if (item.image) { %>
						<div class="cover_image block_featured__image">
							<img src="<%- plugins.img(item.image, { q:85 , w:600 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(item.image, { q:85 , w:600  }) %>" alt="<%- item.heading %>">
						</div>
					<% } %>

					<div class="block_featured__item__inner" >
						<% if (item.heading) { %>
							<h3 class="block_featured__heading heading--h4"><%- item.heading %></h3>
						<% } %>
						<div class="block_featured__description">
							<%- plugins.markdownify(item.description) %>
						</div>
	
						<%- plugins.simpleLink(item.link, 'block_featured__item__link link__alternative link__alternative--white item_card__link') %>

					</div>
				</div>		
			<% }); %>
		<% } %>
	</div>
	</div>
</article>

 