// ======================================================
// Block Styles
// ============
.block_featured {
  $block: &;
  padding: 2rem 0;

  &__grid {
    gap: 70px;
    display: flex;
    flex-direction: row;
    justify-content: center;

    @include breakpoint(medium down) {
      gap: 30px;
      flex-direction: column;
      align-items: center;
    }
  }
}

.block_featured__item {
  display: grid;
  grid-template-rows: 251px 1fr;
  text-align: center;
  margin-bottom: 2rem;
  max-width: 556px;

  &:nth-child(1) {
    background-color: $secondary-color;
  }

  &:nth-child(2) {
    background-color: $primary-color;
  }

  &__inner {
    padding: 35px;
    display: grid;
    gap: 10px;
    grid-template-rows: 60px 1fr 50px;

    @include breakpoint(medium down) {
      gap: 10px;
      padding: 30px 20px;
    }
  }

  &__link {
    color: #fff;
    text-decoration: none;
  }
}

.block_featured__image {
  height: 251px;
  max-width: 556px;
  @include breakpoint(medium down) {
    height: auto;
  }
}

.block_featured__heading {
  font-size: 1.25rem;
  color: #fff;
  text-align: center;
  font-family: Nunito;
  font-size: 25px;
  font-style: normal;
  font-weight: 700;
  text-transform: uppercase;
  margin: 0 auto;

  @include breakpoint(medium down) {
    font-size: 20px;
  }
}

.block_featured__description p {
  color: #fff;
  color: #fff;
  text-align: center;
  font-family: Nunito;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px; /* 177.778% */

  @include breakpoint(medium down) {
    font-size: 16px;
  }
}

.cta-link {
  display: inline-block;
  margin-top: 1rem;
  color: #fff;
  font-weight: bold;
  text-decoration: none;
  border-bottom: 1px solid currentColor;
}

.cover_image img {
  object-fit: cover;
  object-position: top;
}

.contain_image img {
  object-fit: contain;
}
