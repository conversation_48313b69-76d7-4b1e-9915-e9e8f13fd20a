<% var block = options.block %>
<%- block._editable %>

<div class="block_buttons_with_lightboxes">
	<div class="container">
		<h2 class="block_buttons_with_lightboxes__heading"><%- block.heading %></h2>

		<div class="block_buttons_with_lightboxes__buttons">
			<% (block.buttons || []).forEach((button,index) => { %>
				<div class="block_buttons_with_lightboxes__button_wrapper">
					<!-- BUTTON -->
					<button class="block_buttons_with_lightboxes__button" data-lightbox=".button<%- index %>"><%- button.heading %></button>

					<!-- LIGHTBOX -->
					<div class="block_buttons_with_lightboxes__lightbox_wrapper">
						<div class="block_buttons_with_lightboxes__lightbox button<%- index %>">
							<h3 class="block_buttons_with_lightboxes__lightbox_heading"><%- button.heading %></h3>
							<div class="block_buttons_with_lightboxes__lightbox_description"><%- plugins.richText(button.description) %></div>
							<%- plugins.simpleLink(button.link, 'block_buttons_with_lightboxes__lightbox_link button button--secondary') %>
						</div>
					</div>
				</div>
			<% }) %>
		</div>
	</div>
</div>