// ======================================================
// Block Styles
// ============
.block_buttons_with_lightboxes {
	$block: &;

	// LAYOUT
	// ==============
	padding-bottom: 80px;
	padding-top: 65px;
	
	// TEXT
	// =================
	&__heading {
		margin-bottom: 44px;
		padding-bottom: 20px;
		position: relative;
		text-align: center;
		
		&::after{
			background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
			bottom: 0;
			content: " ";
			height: 4px;
			left: 50%;
			position: absolute;
			transform: translate(-50%, -50%);
			width: 264px;			
		}
	}

	// BUTTONS
	// ===============
	&__buttons {
		@include flexgrid($columns: 4, $spacing: 15px, $horizontal-align: center, $breakpoint: large up);
		@include flexgrid($columns: 2, $spacing: 15px, $horizontal-align: center, $breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 15px, $horizontal-align: center, $breakpoint: small down);
		@include breakpoint(small down) {
			max-width: 400px;
			margin-left: auto;
			margin-right: auto;
		}
	}

	&__button {
		background-color: #E9F0F2;
		border: none;
		color: #1C1C1C;
		cursor: pointer;
		font-size: 1rem;
		font-weight: $weight-extrabold;
		margin: 0;
		outline: none;
		padding: 24px 30px 24px;
		position: relative;
		text-align: left;
		width: 100%;
		@include transitions();

		&:hover {
			background-color: #0B8FB7;
			color: $white;
		}

		&:after {
			content: '\f078';
			color: inherit;
			font-family: 'Icomoon';
			font-size: .92em;
			font-weight: normal;
			line-height: 1;
			margin-left: 10px;
			position: absolute;
			right: 25px;
			top: 26px;
		}
	}

	// LIGHTBOXES
	// ==================
	&__lightbox_wrapper {
		display: none;
	}

	&__lightbox {
		max-width: 682px;
	}

	&__lightbox_heading {
		margin-bottom: 35px;
	}

	&__lightbox_link {
		margin-top: 14px;
	}
}