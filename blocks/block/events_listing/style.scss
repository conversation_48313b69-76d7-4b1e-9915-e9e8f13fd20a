// ======================================================
// Block Styles
// ============
.block_events_listing {
	// Your SCSS Code Here.
	.featured_entry {
		margin-bottom: 73px;
		margin-top: 76px;
		@include grid($columns: 540px auto, $spacing: 62px, $vertical-align: center);
		
		@include breakpoint(medium down) { 
			margin-bottom: 40px;
			margin-top: 50px;
			display: flex;
			flex-direction: column;
			text-align: center;
		}
		
		&__date {
			background-color: $tertiary-color;
			color: $headings-color;
			font-size:22.4px;
			font-weight: $weight-extrabold;
			height: 80px;
			letter-spacing: 0.56px;
			line-height:30.4px;
			margin-bottom: 0;
			padding: 10px;
			text-align:center;
			text-transform: uppercase;
			width: 80px;
			@include breakpoint(medium only) {
				margin-right: 24px;
			}
			@include breakpoint(small only) {
				margin-bottom: 20px;
			}
		}

		&__head {
			margin-bottom: 26px;
			@include grid($columns: 80px auto, $spacing: 24px, $vertical-align: center);
			@include breakpoint(medium down){
				align-items: center;
				display: flex;
				justify-content: center;
			}
			@include breakpoint(small down){
				flex-direction: column;
			}
		}

		&__heading {
			color: $headings-color;
			font-weight: $weight-bold;
			margin-bottom: 0;
			text-transform: uppercase;
		}

		&__image {
			height: 300px;
			width: 100%;
			@include breakpoint(medium down){
				order: 1;
			}
			@include breakpoint(small only){
				height: 180px;
			}
		}

		&__location {
			color: $secondary-color;
			font-weight: $weight-bold;
			margin-bottom: 0;
			text-transform: uppercase;
		}

		&__text{
			@include breakpoint(medium down){
				margin-top: 30px;
				order: 2;
			}
		}
	}
	
	.upcoming_events{
		background: $background-1;
		padding-bottom: 50px;
		padding-top: 50px;
		
		@include breakpoint(medium down) {
			padding-bottom: 40px;
			padding-top: 45px;
		}
		
		&__item_content{
			padding-top: 16px;
		}

		&__heading{
			margin-bottom: 45px;
			text-align: center;

			@include breakpoint(medium down) {
				margin-bottom: 30px;
			}
		}
		
		&__item, &__item:focus {
			margin: 0 19px;
			outline: none;

			@include breakpoint(small down){
				margin: 0;
			}
		}
		
		&__item_image{
			height: 200px;
			position: relative;
			
			@include breakpoint(small only){
				height: 180px;
			}

			img{
				width: 100%;
			}
		}
		
		&__item_date{
			background: $tertiary-color;
			bottom: 0;
			color: $headings-color;
			font-weight: $weight-extrabold;
			font-size: 0.875rem;
			letter-spacing: 0.35px;
			line-height: 1.1875rem;
			padding: 4px 22px;
			position: absolute;
			text-transform: uppercase;
			right: 0;			
		}
		
		&__item_description{
			color: $text-color;
			font-weight: $weight-normal;
			font-size: 0.9375rem;
			line-height: 1.75rem;
			margin-bottom: 5px;	
			@include breakpoint(medium up){
				
			}		
		}
		
		&__item_heading{
			color: $headings-color;
			font-size: 1.25rem;
			font-weight: $weight-bold;
			margin-bottom: 4px;
		}
		
		&__item_location{
			color: $secondary-color;
			font-weight: $weight-bold;
			font-size: 0.875rem;
			margin-bottom: 6px;
			text-transform: uppercase;
		}
		
		// slick properties
		.slick_dots {
			margin-top: 51px;
			text-align: center;
			li {
				background: $border-color;
				border-radius: 50%;
				cursor: pointer;
				display: inline-block;
				height: 12px;
				margin-bottom: 0;
				opacity: .47;
				padding-left: 0;
				width: 12px;
				text-align: center;
			}
			li.slick-active {
				opacity: 1;
				background: $secondary-color;
			}
			li:not(:last-child) {
				margin-right: 8px;
			}
			li button {
				display: none;
			}
		}
	}
}