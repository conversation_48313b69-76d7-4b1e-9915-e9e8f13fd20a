<% var block = options.block %>
<%- block._editable %>

<article class="block_events_listing">

    <div class="featured_entry container">  
        <% var entry = plugins.getEntry({id: block.featured_event.id} );
        // In case the featured entry is not set
        if(!entry) {
            var featured_entry = null;
            featured_entry = plugins.stories({component:'Event Module', order_by: 'data.date.value', sort: 'desc', limit: 1, context: 'events listing'});
            if(featured_entry) { 
                entry = featured_entry.stories[0];
            }
        } %>

        <% if(entry) {  %>
            <div class="featured_entry__text">
                <div class="featured_entry__head">
                    <% if(entry.data.date) { %>
                        <p class="featured_entry__date ">
                            <%- plugins.formatDate(entry.data.date, 'DD MMM') %>
                        </p>
                    <% } %>
                    <div class="featured_entry__heading_container">
                        <p class="featured_entry__location">
                            <%- entry.data.location_name %>
                        </p>
                        <h2 class="heading--h6 featured_entry__heading ">
                            <%- entry.title %>
                        </h2>
                    </div>
                </div>
                <% if(entry.data.excerpt) { %>
                    <p  class="featured_entry__description">
                        <%- entry.data.excerpt.substring(0,285) %>
                    </p>
                <% } %> 
                <a href="<%- entry.url %>" class="featured_entry__button link__alternative">
                    Continue Reading
                </a>
            </div>
            <% if(entry.data.featured_image) {
                var image = entry.data.featured_image;
            } else {
                var image = site.settings.events_fallback_image;
            } %>  
            <div class="<% if(entry.data.image_contain == true) { %>contain_image<% } else { %>cover_image<% } %> featured_entry__image">
                <img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>"  src="<%- plugins.img(image, { q: 80, w: 500, lossless: 1, auto: 'format' }) %>" alt="<%- entry.title %>">
            </div>
        <% } %>   
    </div>

    <div class="upcoming_events">
        <div class="container">
            <h4 class="upcoming_events__heading">upcoming events</h4>
            <div class="upcoming_events__list">
                    <% var stories = plugins.stories({
                        component: 'Event Module',
                        sort: 'asc',
                        order_by: 'data.date',
                        where: (entry) => {
                            if(!entry.data.date) return false;
                            var entry_date = Date.parse(entry.data.date);
                            var now = new Date();
                            return now < entry_date;
                        }
                    }, (events) => { %>
            
                        <!-- We got some results, list the events! -->
                        <div class="upcoming_events__item">
                                <div class="upcoming_events__item_image cover_image">
                                    <div class="upcoming_events__item_date"><%- plugins.formatDate(events.data.date, 'DD MMMM') %></div>
                                    <img srcset="<%- plugins.imgSrcSet(events.data.featured_image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>"  src="<%- plugins.img(events.data.featured_image, { q:80 , w:1000 , lossless: 1, auto: 'format' }) %>" alt="<%- events.title %>">
                                </div>
                                <div class="upcoming_events__item_content">
                                    <p class="upcoming_events__item_location"><%- events.data.location_name %></p>  
                                    <h4 class="upcoming_events__item_heading">
                                        <%- events.title.substring(0, '45') %>
                                    </h4>
                                    <p class="upcoming_events__item_description">
                                        <%- events.data.excerpt.substring(0, '127') + '...' %>
                                    </p>
                                    <a href="<%- events.url %>" class="upcoming_events__item_link link__alternative">Read More</a>
                                </div>
                        </div>
            
                    <% }, () => { %>
            
                        <!-- Uh oh, we didn't find any events :( -->
                        <p>Sorry, there's currently no events in the database.</p>
            
                    <% }) %>
            </div>
        </div>
    </div>

</article>