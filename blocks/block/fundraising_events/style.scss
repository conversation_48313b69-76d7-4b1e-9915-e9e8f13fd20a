// ======================================================
// Block Styles
// ============
.block_fundraising_events {
	$block: &;
	
	.container{
		padding-top: 52px;		
		
		@include breakpoint( small only ) {
			padding-top: 45px;
		}
	}
	
	&__description{
		margin: auto;
		margin-bottom: 30px;
		max-width: 880px;
		text-align: center;

		@include breakpoint( medium down ) {
			margin-bottom: 20px;
		}
	}
	
	&__events_item{
		margin: 10px 20px;
		outline: 0;
		background: $background-1;

		@include breakpoint( medium up ) {
//			display: inline-grid !important;
//			height: 248px;
//			grid-template-columns: 2fr 3fr;
//			grid-gap: 0 0;
		}
		
		@include breakpoint(small only) {
			flex-direction: column;
		}
	}
	
	
	&__events_item_description{
		color: $text-color;
		font-size: 0.9375rem;
		font-weight: $weight-normal;
		font-size: 0.9375rem;
		height: auto;
		line-height: 1.75rem;
		margin-bottom: 12px;
	}
	
	&__events_item_heading{
		font-size: 1.4375rem;
		line-height: 1.875rem;	
		margin-bottom: 12px;	
		text-transform: capitalize;
		// @include breakpoint( small only ) {
		// 	height: 60px;
		// }
	}
	
	&__events_item_date{
		background: $primary-color;
		color: $white;
		font-weight: $weight-extrabold;
		font-size: 1.5625rem;
		height:81px;
		left: 50%;
		line-height: 1.625rem;
		padding: 14px;		
		position: absolute;
		text-align: center;
		text-transform: uppercase;
		transform: translate(-50%, -50%);
		top: 50%;
		width:81px;
	}
	&__events_item_link{
		margin-bottom: 0;
		@include breakpoint( small only ) {
			
		}
	}
	
	&__events_item_text{	
		display: flex;
		align-items: center;
		justify-content:center;
		padding: 30px 50px 30px 30px;
		
		@include breakpoint( small only ) {
			padding: 25px 30px 25px 30px;
//			height: 252px;
		}
	}

	&__events_item_text_info {	

	}
	
	&__events_item_image{
		height: 100%;
		width: 230px;
		position: relative;
    	flex-shrink: 0;
		
		@include breakpoint(small only) {
			height: 180px;
			width: 100%;
		}
	}
	
	&__heading{
		margin-bottom: 18px;
		text-align: center;
	}
	
	&__gallery_item{
		background-repeat: no-repeat;
		background-size: cover;
		background-position: center center;
		height: 232px;
		
		@include breakpoint( small only ) {
			height: 180px;
		}
	}
	
	&__gallery_list{
		@include flexgrid($columns: 3, $breakpoint : medium up);
	}
	
	.slick-initialized .slick-slide {
		display: inline-flex;
	}
	//slick properties
	.slick-list {
		margin-left: -8px;
		margin-right: -8px;
		margin-bottom: 31px;
		
		@include breakpoint( small only ) {
			margin-bottom: 0;
		}
	}	
	.slick-dots {
		text-align: center;

		@include breakpoint( small only ) {
			margin-top: 20px;
		}
	}	
	.slick-dots li {
		background: $border-color;
		border-radius: 50%;
		cursor: pointer;
		display: inline-block;
		height: 12px;
		margin-bottom: 0;
		padding-left: 0;
		width: 12px;
	}	
	.slick-dots li.slick-active {
		background: $tertiary-color;
		opacity: 1;
	}	
	.slick-dots li:not(:last-child) {
		margin-right: 8px;
	}	
	.slick-dots li button {
		display: none;
	}
}