<% var block = options.block %>
<%- block._editable %>

<article class="block_fundraising_events">
	<div class="block_fundraising_events__gallery_list">
		<% if(block.gallery) { %>
			<% block.gallery.forEach( (image, index) => { %>	
				<div class="block_fundraising_events__gallery_item" style="background-image: url('<%- plugins.img(image.image, { q: 85, w: 1200, lossless: 1, auto: 'format' }) %>');"></div>		
			<% }); %>
		<% } %>
	</div>
	<div class="container">
		<h2 class="block_fundraising_events__heading heading--h4"><%- block.heading %></h2>
		<p class="block_fundraising_events__description"><%- block.description %></p>

		
		<div class="block_fundraising_events__events_list">			
		

			<% plugins.relationship(block.events, event => { %>
				<!--News card-->
				<a href="<%- event.url %>" class="item_card block_fundraising_events__events_item" data-follow-height="event-slider" data-follow-height-break-on="small">						
					<div class="block_fundraising_events__events_item_image cover_image">
						<% if(event.data.featured_image) {
							var image = event.data.featured_image;
						} else {
							var image = site.settings.news_image_placeholder;
						} %>                                   
						<img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- event.title %>">
						<% if(event.data && event.data.date) { %>
							<p class="block_fundraising_events__events_item_date"><%- plugins.formatDate(event.data.date, 'DD MMM') %></p>
						<% } %>
					</div>
					<div class="block_fundraising_events__events_item_text"> 
						<div class="block_fundraising_events__events_item_info">
							<h3 class="block_fundraising_events__events_item_heading"><%- event.title.substring(0,30) %></h3>
							<% if(event.data) { %>
								<p class="block_fundraising_events__events_item_description"><%- event.data.excerpt.substring(0, 101)+'...' %></p>
							<% } %>			
							<p class="link__alternative link__alternative--red block_fundraising_events__events_item_link">Find out more</p>	
						</div>	
					</div>
	
				</a>                            
				<!--End Card-->

			<% }); %>
										
		</div>
		
	</div>
</article>

 