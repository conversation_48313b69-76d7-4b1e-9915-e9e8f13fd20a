// ======================================================
// Block Javascript
// ================
$(document).ready(function() {
	var $block = $('.block_fundraising_events');
	if($block.length && !$('body').hasClass('js_init--block_resources')) {
		$('body').addClass('js_init--block_resources');
		
		$('.block_fundraising_events__events_list').slick({
			infinite: true,
			slidesToShow: 2,
			slidesToScroll: 2,
			arrows: false,
			dots: true,
			dotsClass: 'unstyled slick-dots',
			responsive: [{
				breakpoint: 1024,
				settings: {
					slidesToShow: 1,
					slidesToScroll: 1,
				}
			},
			{
				breakpoint: 640,
				settings: {
					slidesToShow: 1,
					slidesToScroll: 1,
				}
			}]
		});
	}
	// Reinit
	$('.block_fundraising_events__events_list').on('setPosition', function (event, slick, direction) {
		$('.block_fundraising_events__events_list [data-lightbox]').featherlight(lightbox_settings);
	});
	
});