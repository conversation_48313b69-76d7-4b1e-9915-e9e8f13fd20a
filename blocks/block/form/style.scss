// ======================================================
// Block Styles
// ============
.block_form {
	padding-bottom: 70px;
	padding-top: 45px;
	
	@include breakpoint(medium only) {
		padding-bottom: 50px;
	}
	@include breakpoint( small only) {
		padding-top: 40px;
		padding-bottom: 50px;
	}
	
	&__content{
		@include flexgrid($columns: 350px auto, $spacing: 49px, $breakpoint: large up);
		@include flexgrid($columns: 1, $spacing: 20px, $breakpoint: medium down);		
		@include flexgrid($columns: 1, $spacing: 40px, $breakpoint: small down);		
	}
	
	&__form{
		background: $background-1;
		border: 1px solid $border-color;
		border-radius:3px;
		padding: 21px 32px 0px 32px;
		text-align: center;
		width: 100%;
	}
	
	form{
		input.button{
			border: none;
			cursor: pointer;
			outline: none;
			margin-bottom: 24px;;
		}
	}
	.form {
		&__dropdown {
			max-width: 362px;
			margin-bottom: 19px;
		}
		&__content{
			margin-bottom: 12px;
			@include grid($columns: 1fr 1fr, $spacing: 7px, $vertical-spacing: 12px, $breakpoint: large up);	
			@include grid($columns: 1fr, $spacing: 7px, $vertical-spacing: 12px, $breakpoint: medium down);
			.button{
				border: none;
				border-radius:2.4px;
				margin: auto;
			}
			div{
				align-items: flex-start;
				display: flex;
				flex-direction: column;
			}
			input{
				height: 40px;
			}		
			label{
				color: rgba($video-overlay, .8);
				font-weight: $weight-bold;
				font-size: 0.875rem;
			    line-height: 1.9em;
			}
		}
		.select_wrapper {

			&:before {
				background-color: $secondary-color;
				color: rgba($white, 0.5);
			}
			&:after {
				height: 42px;
				bottom: 0;
				line-height: 42px;
				opacity: 0.6;
			}

			select {
				background-color: $secondary-color;
				border: none;
				color: $white;
				font-size: 0.9375rem;
				font-weight: $weight-extrabold;
				height: 42px;
				line-height: 1.4;
				text-transform: uppercase;
				width: 100%;
				padding-left: 30px;
			}
		}
		&__message{
			margin-bottom: 9px;
			text-align: left;
			@include breakpoint(large up) {
				grid-column: 1/3;
			}
			label{
				color: rgba($video-overlay, .8);
				font-weight: $weight-bold;
				font-size: 0.875rem;
				line-height: 1.9em;
			}
			textarea {
				padding-bottom: 15px;
				padding-top: 15px;
				min-height: 80px;
			}
		}
	}
	
	&__form_intro{
		font-style: italic;
		font-size: 0.9375rem;
		line-height: 2.125rem;
		margin-bottom: 10px;
	}
	
	&__image{
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		height: 225px;
		margin-bottom: 33px;
		width: 100%;
	}
	
	&__introduction{
		margin: auto;
		margin-bottom: 35px;
		max-width: 880px;
		text-align: center;	


	}

	&__link{
		color: $text-color;
	}

	.notice.success {
		display: none;
		background: $secondary-color;
		color: white;
	    padding: 20px;
	    line-height: 1.8;
	    margin-bottom: 30px;
	}
	
	&__phone{
		&:hover{
			color: darken( $text-color, 10% );
		}
	}
	&__phone, &__fax, &__mail{
		display: block;
		font-weight: $weight-bold;
		font-size: 1.1875rem;
		line-height: 2.3rem;
		margin-bottom: 0;
		strong{
			color: $primary-color;
			@include transitions();
			
			&:hover{
				color: darken( $primary-color, 10% );
			}
		}
	}
	&__mail{
		margin-bottom: 22px;
	}
	
	select {
		border-radius: 4px;
		background: $secondary-color;
		height: 40px;
	}
	&__social_heading{
		color: $headings-color;
		font-weight: $weight-bold;
		font-size: 0.9375rem;
		margin-bottom: 13px;
		text-transform: uppercase;		
	}

	&__infos {
		@include grid($columns: 1fr 1fr, $spacing: 49px, $breakpoint: medium only);
	}
}