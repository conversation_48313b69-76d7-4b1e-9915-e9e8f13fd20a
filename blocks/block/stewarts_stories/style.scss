// ======================================================
// Block Styles
// ============

.background--grey{
	background: $background-1;
}

.block_stewarts_stories {
	$block: &;
	padding-top: 55px;
	padding-bottom: 45px;

	@include breakpoint( medium down ) {
		padding-top: 45px;
		padding-bottom: 40px;
	}
	
	@include breakpoint( small only ) {
		
	}
	
	
	&__description{
		max-width: 880px;
		margin: auto;
		text-align: center;
		margin-bottom: 35px;
	}
	
	&__heading{
		text-align: center;
		padding-bottom: 24px;
		margin-bottom: 28px;
		position: relative;
		
		&::after{
			background: linear-gradient(to right, $primary-color, $primary-color 33%, $tertiary-color 33%, $tertiary-color 66%, $secondary-color 66% , $secondary-color 100%);
			bottom: 0;
			content: " ";
			height: 4px;
			left: 50%;
			position: absolute;
			transform: translate(-50%, -50%);
			width: 264px;			
		}
	}
	
	&__list{
		@include grid($columns: 3fr 2fr 2fr , $spacing: 55px, $breakpoint: large up);
		@include grid($columns: 1fr 1fr , $spacing: 40px, $breakpoint: medium only);
		@include grid($columns: 1fr , $spacing: 30px, $breakpoint: small only);
	}
	
	
	&__item{
		overflow: hidden;
		position: relative;
		display: flex;
    	flex-direction: column;

		&:nth-child(4) {
			display: none;

			@include breakpoint( medium down) {
				display: flex;
			}
		}

		&:hover{
			.block_stewarts_stories__item_button{
				@include breakpoint( large up) {
					bottom: 0;
				}
				@include breakpoint( medium only) {
					bottom: 0;
				}
				
			}
		}
	}
	
	&__item_excerpt{
		font-weight: $weight-normal;
		font-size: 0.9375rem;
		line-height: 1.75rem;
		
		@include breakpoint( large up) {
			margin-bottom: 40px;
		}
		@include breakpoint( medium down) {
			margin-bottom: 10px;
		}
		
	}
	&__item_image{
		width: 100%;
		height: 225px;
		margin-bottom: 10px;
		
		img{
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	
	&__item_text{
		font-size: 0.9375rem;
		line-height: 1.75rem;
		
	}
	
	&__item_button{
		cursor: pointer;
		@include breakpoint( large up) {		
			position: absolute;
			bottom: -30px;
			@include transitions();
		}
	}
	
	&__item_video{	
		cursor: pointer;
		position: relative;	
		
		&::before{
			background: $black;
			content: " ";
			height: 100%;
			opacity: 0.2;
			position: absolute;
			width: 100%;
			z-index: 1;
		}
		
		&::after{
			content: " ";
			background-image: url(/assets/images/flaticons/circle.svg);height:55px;
			background-repeat: no-repeat;
			height: 40px;
			left: 50%;
			position: absolute;top: 50%;
			transform: translate(-50%, -50%);
			width:40px;
			z-index: 2;
			@include transitions();
		}
	}
	
}