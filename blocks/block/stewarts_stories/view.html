<% var block = options.block %>
<%- block._editable %>

<article class="block_stewarts_stories <%- block.background %>">

	<div class="container">

		<h2 class="block_stewarts_stories__heading"><%- block.heading %></h2>

		<p class="block_stewarts_stories__description"><%- block.description %></p>

		<div class="block_stewarts_stories__list">

			<% var stories_ids = []; %>
				
			<% if(block.stories.ids.length > 0) { %>
				<% block.stories.ids.forEach( (id, index) => { %>
					<% stories_ids.push(id.uuid) %>
					<% var story = plugins.getEntry({id: id}) %>

					<% if (story){ %>
						<!--News card-->
						<a <% if(story.data && story.data.featured_video_url) { %>data-lightbox-video="<%- story.data.featured_video_url %>"<% } else { %>href="<%- story.url %>"<% } %> class="block_stewarts_stories__item">  
							<div class="block_stewarts_stories__item_image cover_image <% if (story.data && story.data.featured_video_url) { %>block_stewarts_stories__item_video<% } %>">
								<% if(story.data.featured_image) {
									var image = story.data.featured_image;
								} else {
									var image = site.settings.news_image_placeholder;
								} %>                                   
								<img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- story.title %>">
							
							</div>						
							
							<div class="block_stewarts_stories__item_text">

								<% if(story.data.excerpt) { %>
									<p class="block_stewarts_stories__item_excerpt">
										<%- story.data.excerpt.substring(0,75) %>
									</p>			
								<% } %>

								<div class="link__alternative block_stewarts_stories__item_button">READ THIS STORY</div>	
										
							</div>
						</a>                         
						<!--End Card-->
					<% } %>
				<% }) %>
			<% } %>

		</div>
	</div>
</article>

 