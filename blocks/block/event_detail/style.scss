// ======================================================
// Block Styles
// ============
.block_events_detail {
	margin-bottom: 75px;

	@include breakpoint( medium down) {
		margin-bottom: 30px;
	}

	&__blocks {
		padding-bottom: 0;
		padding-top: 80px;
		
		@include breakpoint(medium down) {
			padding-top: 50px;
			padding-bottom: 10px;
		}
	}
	
	&__date {
		background-color: $tertiary-color;
		color: $headings-color;
		font-size:22.4px;
		font-weight: $weight-extrabold;
		height: 80px;
		letter-spacing: 0.56px;
		line-height:30.4px;
		padding: 10px;
		text-align:center;
		text-transform: uppercase;
		width: 80px;
		@include breakpoint(medium down) {
			margin-right: 20px;
		}
	}

	&__head {
		margin-bottom: 33px;
		@include grid($columns: 80px auto, $spacing: 20px, $vertical-align: center);
		@include breakpoint(medium down) {
			align-items: center;
			display: flex;
		}
	}

	&__heading {
		font-size: rem-calc(32);
		margin-bottom: 0;
		text-transform: uppercase;
		line-height: 1.59em;
		@include breakpoint(small down) {
			font-size: rem-calc(18);
		}
	}
}

.sidebar_location {
	margin-bottom: 45px;
	margin-top: 10px;

	&__back_link {
		&:before {
			@extend .icon-chevron-left2:before;
			padding-right: 10px;
		}
	}
	
	.gmap {
		height: 129px !important;
	}

	iframe {
		width: 100%;
		height: 100%;
		object-fit: contain;
		border: none;
	}
	
	&__heading{
		color: $secondary-color;
		font-weight: $weight-extrabold;
		font-size: 0.9375rem;
		letter-spacing: 0.8px;			
		margin-bottom: 18px;
		text-transform: uppercase;		
	}
	
	&__map {
		margin-bottom: 10px;
	}

	ul {
		margin-bottom: 7px;	
	}
	
	li {
		color: $text-color;
		font-weight: $weight-normal;
		font-size: 0.9375rem;
		line-height: 1.75rem;	
	}
	
	.button {
		min-width: 140px;
	}
}