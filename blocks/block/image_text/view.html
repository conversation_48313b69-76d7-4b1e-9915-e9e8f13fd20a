<% var block = options.block %>
<%- block._editable %>

<%
	var image_block = `
		<div class="block_image_text__image block_image_text__image--height-${block.height}">
			<img srcset="<%- plugins.imgSrcSet(block.image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="${plugins.img(block.image, { q: 60, w: 1100, lossless: 1, auto: 'format' })}" alt="" /> 
		</div>
	`;
%>

<article>
	
	<div class="container block_image_text block_image_text--padding-<%= block.padding %> align-<%= block.align_content %>">

		<% if(block.align_image == 'left') { %>
			<%- image_block %>
		<% } %>

		<div class="block_image_text__content block_image_text__content--padding-<%= block.align_image %>">
			<% if(block.content) { %>
				<%- plugins.blocks(block.content); %>
			<% } %>
		</div>

		<% if(block.align_image == 'right') { %>
			<%- image_block %>
		<% } %>

	</div>

</article>