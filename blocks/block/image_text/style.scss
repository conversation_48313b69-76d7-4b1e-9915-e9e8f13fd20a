// ======================================================
// Block Styles
// ============
.block_image_text {

	@include grid($columns: 1fr 1fr, $spacing: 55px, $vertical-align: center, $breakpoint: large up);

	// We don't want this to fuck up
	// our responsive rules.
	@include breakpoint(large up) {

		&--padding-none {
			padding: 0 0;
		}

		&--padding-small {
			padding: 10px 0;
		}

		&--padding-medium {
			padding: 30px 0;
		}

		&--padding-large {
			padding: 60px 0;
		}

	}

	&__image {

		img {
			height: 100%;
			object-fit: cover;
			width: 100%;
			@include breakpoint(medium down) { height: 350px; }
			@include breakpoint(medium down) { height: 250px; }
		}

		// Same here
		@include breakpoint(large up) {

			&--height-small {
				height: 275px;
			}

			&--height-medium {
				height: 350px;
			}

			&--height-large {
				height: 450px;
			}

		}

	}
}
