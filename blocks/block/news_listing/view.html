<% var block = options.block %>
<article class="block_news_listing">
    
    <% if(plugins.segment(2) == '') { %>
        <div class="featured_news container">
           <% var featured = plugins.getEntry({id: block.featured_news_article.id} ); %>
     
            <div class="featured_news__description">
                <% if(featured.data.date) { %>
                    <p class="featured_news__date ">
                        <%- plugins.formatDate(featured.data.date, 'DD MMMM') %>
                    </p>
                <% } %>
                <h2 class="featured_news__heading heading--h6"><%- featured.title %></h2>
                <% if (featured.data.excerpt) { %>
                    <p class="featured_news__excerpt"><%- featured.data.excerpt %></p> 
                <% } %>
                <a href="<%- featured.url %>" class="featured_news__link link__alternative">
                    Continue Reading
                </a>
            </div>
            <% if(featured.data.featured_image) {
                    var image = featured.data.featured_image;
                } else {
                    var image = site.settings.news_fallback_image;
            } %>
            <div class="cover_image featured_news__image">
                <img src="<%- plugins.img(image, { q:80 , w:600 , lossless: 1, auto: 'format' }) %>" srcset="<%- plugins.imgSrcSet(image, {w:600}) %>" alt="<%- featured.data.content[0].text %>">
            </div>
        </div>
    <% } else { %>
       
    <% } %>

    <div class="news_listing">
        <div class="container">
            <!-- News Listing -->
            <% var stories = plugins.stories({
                component: 'News Module',
                limit: 9,
                sort: 'desc',
                order_by: 'created_at',
                paginate: true,
                categoriesPages: true
            }, (news) => { %>
                <!-- We got some results, list the news! -->
                <!--News card-->
                <a href="<%- news.url %>" class="news_listing__item news_item">
                
                    <div class="cover_image news_item__image">
                        <% if(news.data.featured_image) {
                            var image = news.data.featured_image;
                        } else {
                            var image = site.settings.news_fallback_image;
                        } %>
                        <img srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 85, w: 800, lossless: 1, auto: 'format' }) %>" alt="<%- news.title %>">
                    </div>
                    <div class="news_item__wrapper">
                        <% if(news.data.date) { %>
                            <div class="news_item__date">
                                <%- plugins.formatDate(news.data.date, 'DD MMMM') %>
                            </div>
                        <% } %>
                        <h3 class="news_item__heading">
                            <%- news.title %>
                        </h3>
                        <% if(news.data.excerpt) { %>
                            <p class="news_item__description">
                                <%- news.data.excerpt.substring(0,105)+"..." %>
                            </p>
                        <% } %>
                        <span class="link__alternative news_item__link">Read More</span>
                    </div>
                    
                </a>
            <% }, () => { %>
                <!-- Uh oh, we didn't find any news :( -->
                <p>Sorry, there's currently no news in the database.</p>
            <% }) %>
        </div>
        <!-- Pagination -->
        <% if(stories.hasOwnProperty('pagination') && stories.pagination.pages.length > 1) { %>
			<%- plugins.include('snippets/pagination.html', { pagination: stories.pagination }) %>
		<% } %>
    </div>
</article>