// ======================================================
// Block Styles
// ============
.block_news_listing {
	$block: &;
	
	.featured_news {
		margin: 66px auto 73px;
	
		@include grid($columns:560px auto, $spacing: 40px, $vertical-align: center, $breakpoint: large up);
		@include grid($columns: 1fr, $spacing: 20px, $vertical-align: center, $breakpoint: medium down);
		@include breakpoint( medium down ) {
			margin: 50px auto 50px;
		}
		&__date {
			color: $border-color;
			margin-bottom: 0;
			font-weight: $weight-bold;
			margin-bottom: 10px;
			text-transform: uppercase;			
		}
		&__description {
			@include breakpoint( medium down ) {
				text-align: center;
				order: 2;
			}
		}
		&__excerpt {
			margin-bottom: 14px;
		}
		&__heading{
			color: $headings-color;
			margin-bottom: 16px;
			font-weight: $weight-bold;
		}
		&__image {
			height: 299px;
			max-width: 580px;
			margin: auto;
			@include breakpoint( medium down ) {
				order: 1;
			}
			@include breakpoint( small down ) {
				height: 190px;
			}
		}
		&__link {
			margin-bottom: 0;
		}
	}

	.news_item {
		display: flex;
		flex-direction: column;

		&__wrapper {
			display: flex;
			flex-direction: column;
			flex-grow: 1;
		}
		&__date {
			color: $secondary-color;
			font-weight: $weight-bold;
			font-size: 0.875rem;
			margin-top: 15px;
    		margin-bottom: 5px;
			text-transform: uppercase;
		}
		&__description{
			color: $text-color;
			font-weight: $weight-normal;
			font-size: 0.9375rem;
			line-height: 1.75rem;
			margin-bottom: 10px;
		}
		&__heading{
			color: $headings-color;
			font-weight: $weight-bold;
			font-size: 1.25rem;
			text-transform: none;
			margin-bottom: 5px;
		}
		&__image{
			height: 190px;
		}
		&__link {
			line-height: 1;
			margin: auto 0 0;
		}
	}
	.news_listing {
		background: $background-1;
		padding-top: 75px;
		padding-bottom: 52px;
		@include breakpoint( medium down) {
			padding-top: 50px;
			padding-bottom: 50px;
		}
		.container {
			margin-bottom: 52px;
			max-width: 1138px;
			@include grid($columns: 1fr 1fr 1fr, $spacing: 49px, $vertical-spacing: 49px);
			@include grid($columns: 1fr 1fr, $spacing: 49px, $vertical-spacing: 49px, $breakpoint: medium only);
			@include grid($columns: 1fr, $spacing: 49px, $vertical-spacing: 49px, $breakpoint: small down);
		}
	}
	.pagination_block__arrow {
		display: none;
	}
}