<article class="row block_style_tile">
	<div class="columns small-10 small-offset-1">
		<div class="box row">
			<div class="columns small-12 medium-3 large-3 sidebar">
				<div class="logo align-center"><img src="/assets/images/design/logo.svg" alt="Logo"></div>
			</div>
			<div class="columns small-12 medium-9 large-9 content">
				<div class="intro group">
					<h1>Style Tile</h1>
					<!-- <h1>{{ site.data.settings.general_site_name }}</h1> -->
					<!-- <h2>Style Tile</h2> -->
					<p>Here you will find the style tile for the {{ site.data.settings.general_site_name }} project. This simple document gives a visual feel for the entire site in a single location - colours, fonts, heading and text styles, imagery, links, forms and buttons. This is the starting point for your project. Get the style tile correct and everything else flows from here.</p>
				</div>
				<div class="row">
					<div class="columns small-12 medium-12 large-6 group">
						<h3>Headings</h3>
						<div class="headings">
							<h1>Heading 1</h1>
							<h2>Heading 2</h2>
							<h3>Heading 3</h3>
							<h4>Heading 4</h4>
							<h5>Heading 5</h5>
							<h6>Heading 6</h6>
						</div>
					</div>
					<div class="columns small-12 medium-12 large-6 group">
						<h3>Colors</h3>
						<div>
							<div class="colorbox colorbox-1">primary</div>
							<div class="colorbox colorbox-2">secondary</div>
							<div class="colorbox colorbox-3">third</div>
							<div class="colorbox colorbox-4">success</div>
							<div class="colorbox colorbox-5">warning</div>
							<div class="colorbox colorbox-6">alert</div>
							<div class="colorbox colorbox-7">heading</div>
							<div class="colorbox colorbox-8">text</div>
							<div class="colorbox colorbox-9">text light</div>
						</div>
					</div>
					<div class="columns small-12 group">
						<h3>Image Style</h3>
						{% for block in block.images %}
							{% if forloop.index <= 6 %}
								<img src="{{ block.image | remove: site.imgix_storyblok | imgix_url: fit: 'crop', crop: 'entropy', q: 60, w: 300, h: 200, lossless: 1, auto: 'format' }}" class="imagebox" />
							{% endif %}
						{% endfor %}
					</div>
					<div class="columns small-12 group">
						<h3>Form Style</h3>
						<form action="#">

							<div class="form_field">
								<label for="text1">Text Input</label>
								<input type="text" name="text1" id="text1" placeholder="Enter text here">								
							</div>

							<div class="form_field">
								<label for="textarea1">Textarea</label>
								<textarea name="textarea1" id="textarea1" cols="30" rows="10" placeholder="Enter text here"></textarea>
							</div>

							<div class="form_field">
								<label for="select1">Select</label>
								<select name="select1" id="select1">
									<option value="" selected disabled>Please select</option>
									<optgroup label="Group A">
										<option value="1">First</option>
										<option value="2">Second</option>
										<option value="3">Third</option>
									</optgroup>
									<optgroup label="Group B">
										<option value="4">Fourth</option>
										<option value="5">Fifth</option>
										<option value="6">Sixth</option>
									</optgroup>
								</select>
							</div>

							<div class="form_field">
								<label>Radio Buttons</label>
								<label for="radio1" class="label_radio">
									<input type="radio" id="radio1" name="radio1" value="1">
									Radio 1
								</label>
								<label for="radio2" class="label_radio">
									<input type="radio" id="radio2" name="radio1" value="2">
									Radio 2
								</label>
								<label for="radio3" class="label_radio">
									<input type="radio" id="radio3" name="radio1" value="3">
									Radio 3
								</label>
							</div>

							<div class="form_field">
								<label>Checkboxes</label>
								<label for="checkbox1" class="label_checkbox">
									<input type="checkbox" id="checkbox1" name="checkbox1" value="1">
									Checkbox 1
								</label>
								<label for="checkbox2" class="label_checkbox">
									<input type="checkbox" id="checkbox2" name="checkbox1" value="2">
									Checkbox 2
								</label>
								<label for="checkbox3" class="label_checkbox">
									<input type="checkbox" id="checkbox3" name="checkbox1" value="3">
									Checkbox 3
								</label>
							</div>

							<div class="form_submit">
								<input type="submit" name="submit" value="Submit" class="button"> <input type="submit" name="submit" value="Alternative" class="button button--alternative">
							</div>

						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</article>