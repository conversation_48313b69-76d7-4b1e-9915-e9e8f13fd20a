<% var block = options.block %>
<%- block._editable %>

<article class="block_past_events">
	<h2 class="block_past_events__heading heading--h4">Past Events</h2>
	<div class="container block_past_events__list">
		<!-- Events Listing -->
		<% var stories = plugins.stories({
			component: 'Event Module',
			sort: 'asc',
            order_by: 'date',
            where: (entry) => {
                    if(!entry.data.date) return false;
                    var entry_date = Date.parse(entry.data.date);
                    var now = new Date();
                    return now >= entry_date;
            }
		}, (events) => { %>

			<!-- We got some results, list the events! -->
			<div class="block_past_events__item">
				<div class="block_past_events__wrapper">
					<div class="block_past_events__image cover_image" data-follow-height="events-slider" data-follow-height-break-on="medium">
						<div class="block_past_events__date"><%- plugins.formatDate(events.data.date, 'DD MMM') %></div>
						<img srcset="<%- plugins.imgSrcSet(events.data.featured_image, { q: 80, w: 1000, lossless: 1, auto: 'format' }) %>"  src="<%- plugins.img(events.data.featured_image, { q:80 , w:1000 , lossless: 1, auto: 'format' }) %>" alt="<%- events.title %>">
					</div>
					<div class="block_past_events__text" data-follow-height="events-slider" data-follow-height-break-on="medium">
						<h4 class="block_past_events__item_heading">
							<%- events.title.substring(0, '30') %>
						</h4>
						<p class="block_past_events__description">
							<%- events.data.excerpt.substring(0, '88') + '...' %>
						</p>
						<a href="<%- events.url %>" class="block_past_events__link link__alternative link__alternative--red">Find Out More</a>
					</div>
				</div>
			</div>

		<% }, () => { %>

			<!-- Uh oh, we didn't find any events :( -->
			<p>Sorry, there's currently no events in the database.</p>

		<% }) %>
	</div>
</article>
