// ======================================================
// Block Styles
// ============
.block_past_events {
	$block: &;
	margin-top: 56px;
	margin-bottom: 69px;

	@include breakpoint(medium down) {
		margin-bottom: 40px;
		margin-top: 45px;
	}

	&__date {
		background: $primary-color;
		color: $white;
		font-size:22.4px;
		font-weight: $weight-extrabold;
		height: 80px;
		letter-spacing: 0.56px;
		left: calc(50% - 40px);
		line-height:30.4px;
		padding: 10px;
		position: absolute;
		text-align:center;
		text-transform: uppercase;
		top: calc(50% - 40px);
		width: 80px;
		background-image: none !important;
	}

	&__description {
		color: $text-color;
		font-weight: $weight-normal;
		font-size: 0.9375rem;
		line-height: 1.75rem;
		margin-bottom: 10px;

		@include breakpoint(large up){
			height: 112px;
		}
		@include breakpoint(medium only){
			height: 100px;
		}
	}

	&__heading {
		margin: auto;
		margin-bottom: 56px;
		text-align: center;

		@include breakpoint(medium down) {
			margin-bottom: 30px;
		}
	}

	&__image {
		height: 100%;
		position: relative;
		
		@include breakpoint(medium down){
			height: 180px;
		}

		img {
			width: 100%;
		}
	}

	&__item, &__item:focus {
		margin: 0 19px;
		outline: none;

		@include breakpoint(small down){
			margin: 0;
		}
	}

	&__item_heading {
		color: $headings-color;
		font-weight: $weight-bold;
		font-size: 1.4375rem;
		line-height: 1.875rem;
		margin-bottom: 8px;
		text-transform: none;
		@include breakpoint(medium up){
			height: 60px;
		}
	}

	&__link {
		margin-bottom: 0;
	}

	&__text {
		padding: 29px 30px;
	}
	
	&__wrapper {
		background-color: $background-1;
		@include flexgrid($columns: 33% auto, $breakpoint : large up);
		@include flexgrid($columns: 1 , $breakpoint : medium down);
	}

	// slick properties
	.slick_dots {
		margin-top: 51px;
		text-align: center;
		li {
			background: $border-color;
			border-radius: 50%;
			cursor: pointer;
			display: inline-block;
			height: 12px;
			margin-bottom: 0;
			opacity: .47;
			padding-left: 0;
			width: 12px;
			text-align: center;
		}
		li.slick-active {
			opacity: 1;
			background: $secondary-color;
		}
		li:not(:last-child) {
			margin-right: 8px;
		}
		li button {
			display: none;
		}
	}
}