// ======================================================
// Block Javascript
// ================
$(document).ready(function() {
	var $block = $('.block_past_events');
	if($block.length && !$('body').hasClass('js_init--block_past_events')) {
		$('body').addClass('js_init--block_past_events');
		
		$('.block_past_events__list').slick({
			infinite: false,
			slidesToShow: 2,
			slidesToScroll: 2,
			arrows: false,
			dots: true,
			dotsClass: 'unstyled slick_dots',
			responsive: [
                {
                    breakpoint: 640,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                    }
                }]
		});

	}
});