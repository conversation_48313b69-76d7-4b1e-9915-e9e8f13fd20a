// ======================================================
// Block Styles
// ============
.block_our_programmes {
	$block: &;
	background: $background-1;
	padding-bottom: 80px;
	padding-top: 50px;
	
	@include breakpoint(medium down) {
		padding-top: 45px;
		padding-bottom: 50px;
	}
	
	&__description{
		margin: auto;
		margin-bottom: 33px;
		max-width: 880px;
		text-align: center;
	}
	
	&__heading{
		margin-bottom: 10px;
		text-align: center;
	}
	
	&__item{		
		border: 1px solid $border-color;
		cursor: pointer;
		display: flex;
    	flex-direction: column;

		&:hover{
			.block_our_programmes__item_button{
				color: darken( $primary-color, 10% );
			}
		}
	}
	
	&__item_content{
		background: $white;
		padding: 25px 20px 25px 20px; 
		text-align: center;
		display: flex;
	    flex-direction: column;
	    flex-grow: 1;
	}
	
	&__item_description{
		font-size: 0.9375rem;
		font-weight: $weight-normal;
		line-height: 1.75rem;
		margin-bottom: 17px;
		text-align: center;
	}

	&__item_button {
		margin: auto 0 0;
	}
	
	&__item_heading{
		align-items: center;
		display: flex;
		justify-content: center;
		margin-bottom: 9px;
		
		@include breakpoint( medium up) {
			
		}
		
	}
	
	&__item_image{
		height: 159px;
	}
	
	&__list{
		
	}

	&__list_item {
		@include flexgrid($columns: 4, $spacing: 20px,$breakpoint: large up);
		@include flexgrid($columns: 2, $spacing: 20px,$breakpoint: medium only);
		@include flexgrid($columns: 1, $spacing: 20px,$breakpoint: small only);	
		justify-content: center;	
	}
	
	//lightbox
	.js-lightbox{
		
		@include breakpoint( medium only ) {
			width: 100% !important;
		}
		
		@include breakpoint( small only ) {
			width: 80% !important;
		}
	}
	
	&__lightbox{
		@include breakpoint( small only ) {
			padding: 20px 30px;
		}
	}
	&__lightbox_arrows{
		cursor: pointer;
		display: flex;
		justify-content: space-between;
		width: 100%;
		
		@include breakpoint( small only ) {
			flex-direction: row;
			align-items: center;
		}
		
		.link__alternative{
			color: #999999;
			text-align: center;
			@include transitions();
			&:hover {
				color: darken(#999999, 10% );
			}
		}
		.link__alternative + .link__alternative{
			@include breakpoint( small only ) {
				margin-left: 0;
			}
		}
		.link__alternative--left::before {
			content: "\f0a4" !important;

			@include breakpoint(small down) {
				background: $secondary-color;
				border-radius: 2px;
				color: $white;
				display: block;
				font-size: 19px;
				height: 41px;
				line-height: 42px;
				width: 41px;
				cursor: pointer;

				&:hover {
					background: darken( $secondary-color, 10% ) !important;
				}
			}
		}

		.link__alternative::after {

			@include breakpoint(small down) {
				background: $secondary-color;
				border-radius: 2px;
				color: $white;
				display: block;
				font-size: 19px;
				height: 41px;
				line-height: 42px;
				width: 41px;
				cursor: pointer;

				&:hover {
					background: darken( $secondary-color, 10% ) !important;
				}
			}
		}

	}

	&__lightbox_arrows_mobile {
		
		&:before {


		}
		
		
		@include breakpoint( small only ) {
			display: none;
		}
	}
	
	&__lightbox_button{
		margin-bottom: 0;
	}
	&__lightbox_content{
		margin-bottom: 45px;
		@include grid($columns: 1fr 1fr , $spacing: 44px,$breakpoint: large up);
		@include grid($columns: 1fr , $spacing: 30px,$breakpoint: medium down);
	}
	&__lightbox_description{
		color: $text-color;
		margin-bottom: 23px;
		font-weight: $weight-normal;
	}
	&__lightbox_heading{
		color: $headings-color;
		font-weight: $weight-bold;
		margin-bottom: 25px;	

		@include breakpoint( medium down) {
			    font-size: 2rem;
		}	
	}
	&__lightbox_img_container{
		height: 393px;
		
		@include breakpoint( medium down) {
			order: -1;
		}@include breakpoint( medium only ) {
			height: 250px;
		}
		@include breakpoint( small only ) {
			height: 180px;
		}
	}
	
	.js-lightbox .block_our_programmes__lightbox {
		display: block;
	}
	.block_our_programmes__lightbox {
		display: none;
	}
}