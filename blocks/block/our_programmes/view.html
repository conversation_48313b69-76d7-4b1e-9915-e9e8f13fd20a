<% var block = options.block %>
<%- block._editable %>

<article class="block_our_programmes">
	<div class="container">
		<h2 class="block_our_programmes__heading"><%- block.heading %></h2>
		<p class="block_our_programmes__description"><%- block.description %></p>
		<div class="block_our_programmes__list">
			<div class="block_our_programmes__list_item">
				<% if(block.items) { %>
					<% block.items.forEach( (item, index) => { %>
						<!-- Card-->
						<a href="<%- plugins.storylink(item.link) %>" class="block_our_programmes__item">	   				
							<div class="block_our_programmes__item_image cover_image">
							<% if(item.image) {
								var image = item.image;
							} else {
								var image = site.settings.news_image_placeholder;
							} %>                                   
								<img  srcset="<%- plugins.imgSrcSet(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.img(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- item.heading %>">
							</div>
							<div class="block_our_programmes__item_content">
								<h3 class="block_our_programmes__item_heading heading--h6"><%- item.heading %></h3>
								<p class="block_our_programmes__item_description"><%- item.description.substring(0,50) %>...</p>
								<% if(item.link) { %>
									<span class="block_our_programmes__item_button link__alternative">read more</span>
								<% } %>
							</div>  	
						</a>    

					<% }); %>
				<% } %>
			</div>
		</div>
	</div>
</article>

 