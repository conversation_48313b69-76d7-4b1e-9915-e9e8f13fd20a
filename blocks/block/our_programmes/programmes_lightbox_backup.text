<% var block = options.block %>
<%- block._editable %>

<article class="block_our_programmes">
	<div class="container">
		<h2 class="block_our_programmes__heading"><%- block.heading %></h2>
		<p class="block_our_programmes__description"><%- block.description %></p>
		<div class="block_our_programmes__list">
			<div class="block_our_programmes__list_item">
				<% if(block.items) { %>
					<% block.items.forEach( (item, index) => { %>			    
						<!-- Card-->
						<a class="block_our_programmes__item " data-active-ignore data-lightbox-group="awards" data-lightbox="#awards<%- block._uid %><%- index %>" >	   				
							<div class="block_our_programmes__item_image cover_image">
							<% if(item.image) {
								var image = item.image;
							} else {
								var image = site.settings.news_image_placeholder;
							} %>                                   
								<img  srcset="<%- plugins.srcset(image,  { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" src="<%- plugins.imgix(image, { q: 80, w: 800, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- item.heading %>">
							</div>
							<div class="block_our_programmes__item_content">
								<h3 class="block_our_programmes__item_heading heading--h6"><%- item.heading %></h3>
								<p class="block_our_programmes__item_description"><%- item.description.substring(0,50) %>...</p>
								<% if(item.link) { %>
									<span class="block_our_programmes__item_button link__alternative">read more</span>
								<% } %>
							</div>  	
						</a>    

					<% }); %>
				<% } %>
			</div>
			<% if(block.items) { %>
				<% block.items.forEach( (item, index) => { %>			       

					<!--lightbox-->
					<div class="block_our_programmes__lightbox" id="awards<%- block._uid %><%- index %>" >
						<h3 class="block_our_programmes__lightbox_heading"><%- item.heading  %></h3>
						<div class="block_our_programmes__lightbox_content">
							<div class="block_our_programmes__lightbox_content_text">
								<p class="block_our_programmes__lightbox_description"><%- item.description %></p>
								<% if (item.link && item.link.cached_url){ %>
									<a href="<%- plugins.storylink(item.link) %>" class="block_our_programmes__lightbox_button button button--secondary">find out more</a> 
								<% } %>
							</div>
							
							<div class="block_our_programmes__lightbox_img_container cover_image">
								<img srcset="<%- plugins.srcset(item.image, { q: 100, w: 600, lossless: 1, auto: 'format' }) %>" src="<%- plugins.imgix(item.image,  { q: 100, w: 600, h: 400, lossless: 1, auto: 'format' }) %>" alt="<%- item.heading %>" class="block_our_programmes__lightbox_img">  
							</div>
						</div>
								
						<div class="block_our_programmes__lightbox_arrows">
							<div class="link link__alternative link__alternative--left lightbox__navigation_arrow lightbox__navigation_arrow--previous" data-lightbox-previous> 
								<span class="block_our_programmes__lightbox_arrows_mobile"><% if(index > 0){ %>
									<%- block.items[index - 1].heading %>
								<% } else { %>
									<%- block.items[block.items.length -1].heading %>
								<% } %></span>
							</div>
							<div class="link link__alternative lightbox__navigation_arrow lightbox__navigation_arrow--next" data-lightbox-next> 
								<span class="block_our_programmes__lightbox_arrows_mobile"><% if(index < (block.items.length - 1)) { %>
									<%- block.items[index + 1].heading %>
								<% } else { %>
									<%- block.items[0].heading %>
								<% } %></span>
							</div> 
						</div>	
					</div> 
					<!--end lightbox-->

				<% }); %>
			<% } %>
		</div>
	</div>
</article>

 