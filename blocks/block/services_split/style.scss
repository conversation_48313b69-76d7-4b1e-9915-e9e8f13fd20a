// ======================================================
// Block Styles
// ============
.block_services_split {
	$block: &;
	padding-bottom: 50px;
	padding-top: 55px;

	@include breakpoint( medium down) {
		padding-top: 50px;
	}

	&__button{
		margin: auto 0 0;
	}

	.container{
		@include grid($columns: 1fr 1fr, $spacing:91px, $breakpoint: medium up);
		@include grid($columns: 1fr , $spacing:30px, $breakpoint: medium down);	
	}

	&__description{
		margin-bottom: 14px;
	}

	&__heading{
		margin-bottom: 20px;
	}

	&__toc{
		border-left: 2px solid $border-color;
		padding-bottom: 20px;
		padding-left: 35px;
		padding-top: 20px;
		display: flex;
    	flex-direction: column;

		@include breakpoint( small only) {
			padding: 20px 0 20px 30px;
		}
	}

}