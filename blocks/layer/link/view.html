<% var block = options.block %>
<%- block._editable %>

<!-- Is that a video ? -->
<% var is_video = (block.link.url && (block.link.url.indexOf('yout') >= 0 || block.link.url.indexOf('vimeo') >= 0)); %>

<!-- Template -->
<% if(block.open == 'url') { %>

	<a 
		href="
			<% if(block.link.linktype == 'url') { %>
				<%= block.link.url %>
			<% } else { %>
				<%= plugins.urlByUid(block.link.id) %>
			<% } %>
		" 
		class="
			<%= block.type %>
			<% if(is_video) { %>
				 button--video
			<% } %>

			<% if (block.custom_class) { %>
				<%= block.custom_class %>
			<% } %>
		" 
		<% if(block.new_tab) { %>target="_blank"<% } %>

		<% if(is_video) { %>
			data-lightbox-video="<%= block.link.url %>"
		<% } %>>
		
		<%= block.text %>
	</a>	
	
<% } else if(block.open == 'file') { %>

		<a href="<%- block.file %>" class="<%= block.type %>
			<% if (block.custom_class) { %>
				<%= block.custom_class %>
			<% } %>
			" target="_blank">
			<%= block.text %>
		</a>	

<% } else if(block.open == 'form'){ %>

    <a href="#" class="<%= block.type %>
        <% if (block.custom_class) { %>
            <%= block.custom_class %>
        <% } %>
        " data-lightbox="<%- block.uuid %>">
        <%= block.text %>
    </a>	
    <div class="layer_link__form">
        <%- plugins.blocks(block.form) %>
    </div>

<% } %>
