// ======================================================
// Block Styles
// ============
$image_margin: 10px 25px 0;

.content_image {
    height: 100%;
    margin-bottom: 30px;
	margin-left: auto;
	margin-right: auto;
    width: 100%;

    @include breakpoint( small down) {
        height: 190px;
	}

	&__image {
		height: auto;
		border-radius: 4px;
		width: 100%;
	}

	div.img {
		height: 370px;

		@include breakpoint( small down ) {		
			height: 200px;
		}
	}
	
	// Alignment
	&--left {
		float: left;
		margin: $image_margin;
		margin-left: 0;
	}

	&--right {
		float: right;
		margin: $image_margin;
		margin-right: 0;
	}

	// Proportions
	&--cover {
		height: 370px;

		@include breakpoint( small down ) {		
			height: 200px;
		}
	}

	&--contain {
        height:auto!important;
	}

	// Width
	&--width-10 {
		height: 9%;
		width: 10%;
	}

	&--width-25 {
		height: 20%;
		width: 25%;
	}

	&--width-50 {
		height: 35%;
		width: 50%;
	}

	&--width-75 {
		height: 40%;
		width: 61.42%;
	}

	&--width-100 {
		height: 50%;
		width: 100%;
	}

	&__description {
		font-size: 0.85em;
		text-align: center !important;
		width: 95%;
		margin: auto;
		font-style: italic;
		margin-bottom: 6px;
		background-image: none !important;
	}
}