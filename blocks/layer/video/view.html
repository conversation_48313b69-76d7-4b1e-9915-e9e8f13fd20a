<% var block = options.block %>
<%- block._editable %>

<article class="video layer_video video--lightbox layer_video--width_100" <% if(block.url) { %>data-lightbox-video="<%- block.url %>"<% } %>>
	<div class="video__container <% if(block.cover) { %>video--cover<% } %>">
		<% if(block.cover) { %>		
			<div class="cover_image video__image">
				<img src="<%- plugins.img(block.cover, { q: 60, w: 900, fm: 'jpg' }) %>">
			</div>
		<% } %>
	</div>
</article>