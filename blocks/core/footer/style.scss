// ======================================================
// Block Styles
// ============
.core_footer {
	
	// General Footer Styles
	background-color:$white;
	border-top: 1px solid $border-color;
	padding-top: 54px;
	
	// Core Footer Upper Section
	// ============================
	&__columns {
		@include grid($columns: 1fr 1fr 1fr 1fr 2fr, $spacing: 30px);
		@include grid($columns: 1fr 1fr 1fr, $spacing: 25px, $breakpoint: medium only);
		@include grid($columns: 1fr 1fr , $spacing: 25px, $breakpoint: small down);
		
		@media screen and (max-width: 440px) {
			grid-template-columns: repeat(1, 1fr);
			text-align: center;
		}
	}
	
	&__heading {
		color: $headings-color;
		font-weight: $weight-bold;
		font-size: 0.9375rem;		
		margin-bottom: 18px;
	}
	
	&__navigation_item {
		font-size: rem-calc(15);
		line-height: 1.1875rem;
		color: $text-color;
		display: block;
		margin-bottom: 7px;		
		font-weight: $weight-normal;
		line-height: 1.625rem;
		text-transform: capitalize;
		
		@include transitions();
		
		&:hover {
			color: $primary-color;
		}
	}
	
	
	
	&__subheading {
		font-size: rem-calc(13);
		font-weight: $weight-semibold;
	}
	
	&__address {
		font-size: rem-calc(13);
		line-height: 1.1875rem;
		margin-bottom: 5px;
	}
	
	&__telephone, &__email {
		font-size: rem-calc(13);
		color: $text-color;
		display: block;
		@include transitions();
		
		&:hover {
			color: $primary-color;
		}
	}
	
	&__button {
		font-size: rem-calc(12);
		line-height: 16px;
		text-align: center;
		padding: 9px 19px;
		margin-bottom: 0;
		&--donate {
			padding: 10px 19px 8px 19px;
			min-width: unset;
		}
	}
	
	&__social {
		margin-top: 5px;
		
		@media screen and (max-width: 440px) {
			justify-content: center;
		}

		a {
			margin: 0;
			&:not(:last-child) {
				margin-right: 4px;
			}
		}
	}
	
	&__sport_description{
		font-size: 0.9375rem;
		line-height: 1.625rem;	
		margin-bottom: 16px;	
	}
	// Core Footer Bottom
	// ========================
	&__bottom {
		margin-top: 70px;
		background: $background-1;
		
		@include breakpoint( medium down ) {
			margin-top: 40px; 
		}
		
		.container {
			@include grid($columns: auto auto);
			padding-top: 10px;
			padding-bottom: 10px;
			
			@include breakpoint( medium down ) {
				text-align: center;
			}
		}
	}
	
	&__copyright {
		
		&_text {
			opacity: 0.8;
			font-size: rem-calc(12);
		}
		
		&_link {
			font-size: rem-calc(12);
			@include transitions();
			
			&:hover {
				color: darken( $primary-color, 10% );
			}
		}
		
		&_madeby {
			@include breakpoint( small down ) {
				display: block;
				opacity: 0.8;
			}
		}
	}
	
	&__links {
		text-align: right;
		@include breakpoint(medium down) {
			text-align: center;
		}
		&_item {
			color: $text-color;
			opacity: 0.8;
			font-size: rem-calc(12);
			display: inline-block;
			font-weight: $weight-normal;
			@include transitions();
			
			&:hover {
				color: $primary-color;
			}
			
			&:not(:last-child) { 
				margin-right: 45px; 
				@include breakpoint(small down) {
					margin-right: 15px;
				}
			}
		}
	}

	&__column_follow {

		&--desktop {
			@include breakpoint(medium down) {
				display: none;
			}
		}
		
		&--mobile {
			display: none;

			@include breakpoint(medium down) {
				display: block;
			}
		}

	}

	&__charity_info {
		margin-top: 15px;
		font-style: italic;
		font-size: 0.9375rem;
		line-height: 1.625rem;
		margin-bottom: 0px;
	}

	&__column {
		&_section {
			&:not(:first-child) {
				margin-top: 25px;
				@include breakpoint( small down ) {
					margin-top: 50px;
				}
			}
		}
	}
	
}