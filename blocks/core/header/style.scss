// ======================================================
// Block Styles
// ============
.core_header {
	position: relative;
	background-color: $white;
	padding:0;
	
		> * { position: relative; }
			
		.logo_container {
			display: flex;
    		align-items: center;
		}
		// Logo
		.logo {
			
			&__image {
				display: block;
				height: 100px;

			    @include breakpoint( large up ) { 
					 margin-top: 20px;
					 margin-bottom: 20px;
			    }
			}
			
		}
		
		// Navigation
		.navigation {
			height: 100%;

			&__item {
				display: inline-block;
				text-transform: uppercase;				
				color: $headings-color;
				font-weight: $weight-bold;
				font-size: 0.9375rem;
				height: 100%; 
				letter-spacing: 0.2px;
				padding: 55px 0 25px;
				position: relative;
				margin-left: 50px; 
				@include transitions();

				&:hover {
					
					&:before {
						opacity: 1;
					}
					
				}

				&.active, &.open {

					&:before {
						opacity: 1;
					}
				} 

				&:first-child {
					margin-left: 0; 
				}

				&::before{
					position: absolute;
					content: " ";
					width: 100%;
					bottom: 0;
					left: 0;
					border-bottom: 3px solid $tertiary-color;
					opacity: 0;
					@include transitions();
				}
				
				&--mobile {
					display: none;
					
					@include breakpoint(medium down) { 
						display: inline-block; 
					}
				}
				
				&--button {
					font-weight: $weight-bold;
					min-width: 138px;
					margin-bottom: 0;
					margin-left: 50px;
					font-size: 0.9375rem;
					padding: 10px 18px 9px 19px;
				}
				
				&--arrow:after {
					content: '\f0a3';
					color: inherit;
					font-family: 'Icomoon';
					font-size: .85em;
					font-weight: normal;
					line-height: 1;
					margin-left: 7px;
					position: relative;
					@include transitions();
				}
			}
		}
		
		//secondary navigation
		.secondary_navigation {
			display: flex;
			height: 100%;
			justify-content: flex-end;
			padding-left:2px;
			padding-right: 2px;
			text-align: right;
			
			&__item {
				color: $headings-color;
				display: inline-block;
				display: flex;
				align-items: center;

				font-weight: $weight-semibold;
				font-size: 0.75rem;
				opacity:0.8;
				text-transform: uppercase;
				vertical-align: center;
				@include transitions();
				
				&:hover, &.active { color: $primary-color; }
				
				&:not(:last-child) { padding-right: 42px; }
				&:last-child{
					background: $border-color;
					font-weight: $weight-bold;
					padding-left: 20px;
					padding-right: 20px;
				}
			}	
			
			@include breakpoint(medium down) { display: none; }
		}
		
		//Top nav
		.top_nav{
			background: $background-1;
			height: 45px;
			@include breakpoint(medium down) { display: none; }
			.container{
				height: 100%;
			}
		}
		
		//Megamenu
		.megamenu{
			background: rgba(233,240,242,0.40);
			padding-top: 55px;
			padding-bottom: 55px;
			@include breakpoint(medium down) { display: none !important; }

			

		}
		.megamenu__inner--work{
			@include grid($columns: 2fr 3fr , $spacing: 100px,$breakpoint: large up);
		}
		.megamenu_card__image{
			height: 153px;
			margin-bottom: 23px;
		}
		.megamenu_featured__heading{
			color: $headings-color;
			font-weight: $weight-bold;
			font-size: 1.25rem;
			margin-bottom: 9px;
		}
		.megamenu_featured__description{
			color: $text-color;
			font-weight: $weight-normal;	
			line-height:28px;		
			margin-bottom: 5px;
		}
		.megamenu_panel{
			height: 325px;
			@include grid($columns: 2 , $spacing: 33px, $vertical-spacing:35px, $breakpoint: large up);
		}
		.megamenu_list{
			@include grid($columns: 3 , $spacing: 33px, $vertical-spacing:35px, $breakpoint: large up);
		}

		.megamenu_about_list{
			@include grid($columns: 4 , $spacing: 45px, $vertical-spacing:25px, $breakpoint: large up);
		}
		.megamenu_panel_item {
			&__image {
				overflow: hidden;
				height: 125px;
				img {
					width: 100%;
					height: 100%;
					max-width: 100%;
					object-fit: cover;
					@include transitions();
				}
			}
			&_bg {
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				display: flex;
				height: 100%;
				padding: 20px;
				align-items: center;
				justify-content: center;
				position: relative;
				@include transitions();
				&::after{
					position: absolute;
					content: " ";
					width: 100%;
					height: 100%;
					top: 0;
					left: 0;
					background: $video-overlay;
					opacity: 0.4;
					z-index: 1;
				}
	
				&:hover {
					transform: scale(1.05);
					
				}
			}
			&:hover {
				img {
					transform: scale(1.1);
				}
				.megamenu_panel_heading {
					a {
						color: $primary-color;
					}
				}
			}
		}
		.megamenu_panel_heading {
			margin-bottom: 0;	
			margin-top: 13px;
			font-size: rem-calc(16);
			line-height: rem-calc(25);
			a {
				color: $headings-color;
				font-weight: $weight-extrabold;
				@include transitions();
				text-transform: capitalize;
				display: inline-flex;
				align-items: center;
				span {
					margin-left: 4px;
					font-size: rem-calc(14);
					transform: translateY(-1px);
				}
			}
		}
		
		// Banner
		.banner {			
			background-position: center center;
			background-size: cover;
			padding: 167px 0 168px;
			text-align: center;
			position: relative;

			&--link {
				text-align: left;
			}

			.container {
				position: relative;
				z-index: 1;
			}

			&--top {
				background-position: top center;
			}

			&--bottom {
				background-position: bottom center;
			}

			@include breakpoint(small down) {
				padding: 70px 0 70px;
			}

			&__hide {
				display: none;
			}
			
			&__heading {
				color: white;
				font-weight: bold;
				margin-bottom: 0;
				position: relative;
				text-align: left;
				z-index: 2;
				line-height: 1.3em;
				@include breakpoint(medium down) { text-align: center; }
			}
			&__strapline {
				color: white;
				opacity: 0.6;
			}
			
			&::before{
				background-color: rgba(85, 84, 84, 0.342);
				content: "";
				height: 100%;
				left: 0;
				position: absolute;
				top: 0;
				width: 100%;
				z-index: 1;
			}

			&__links {
				@include breakpoint(medium down) {
					text-align: center;
				}
			}

			.button {
				margin-bottom: 0;
				margin-top: 20px;
			}

		}
		
		// Top Menu
		div.top_menu {
			height: 100%;
			position: relative;
			@include flexgrid($columns: 220px auto, $vertical-align: center);
			@include breakpoint(medium down) { padding: 26px 0 23px 0; }
			div.navigation {
				height: 140px;
				text-align: right;
				@include breakpoint(medium down) { display: none; }
			}
		}
		
		// Burger Menu
		div.burger_menu {
			@include vertical-align();
			right: 0;
			@include breakpoint(large up) { display: none !important; }
		}
		
	}