<header class="core_header">
	<div class="top_nav">
		<div class="container">
			<% if(plugins.segment(1) == 'sports-centre') { %>
                <%- plugins.include('snippets/header/secondary-nav-centre.html') %>
            <% } else { %>
                <%- plugins.include('snippets/header/secondary-nav.html') %>
            <% } %>
		</div>
	</div>
	<div class="container">
		<div class="top_menu">
			<div class="logo_container">
				<a href="/" class="logo"><img class="logo__image" src="/assets/images/design/logo-square.png" alt="<%= site.config.name %> Logo" /></a>
			</div>
			<div class="navigation">
				<% if(plugins.segment(1) == 'sports-centre') { %>
	                <%- plugins.include('snippets/header/sports-nav.html') %>
	            <% } else { %>
	                <%- plugins.include('snippets/header/nav.html') %>
	            <% } %>
			</div>
			<div class="burger_menu">
				<a href="#" class="burger" data-responsive-menu-trigger>
					<span class="burger__icon">
						<span></span>
						<span></span>
						<span></span>
					</span>
				</a>
			</div>
		</div>
		
	</div>

	<!-- Megamenu -->
	<%- plugins.include('snippets/header/megamenu-our-services.html', {cache: true}) %>
	<%- plugins.include('snippets/header/megamenu-support-us.html', {cache: true}) %>
	<%- plugins.include('snippets/header/megamenu-about.html', {cache: true}) %>
	<%- plugins.include('snippets/header/megamenu-pool.html', {cache: true}) %>
	<%- plugins.include('snippets/header/megamenu-fitness.html', {cache: true}) %>
	<%- plugins.include('snippets/header/megamenu-childrens-camps.html', {cache: true}) %>


	<!-- Banner -->
	<% if(page.data && !page.data.banner_hide && page.data.component != 'Products Module') { %>
		<% var event_listing_page = plugins.getEntry({slug: 'events'}); %>
		<% var news_listing_page = plugins.getEntry({slug: 'news'}); %>
		<% var services_listing_page = plugins.getEntry({slug: 'services'}); %>
		<% var stewarts_stories_page = plugins.getEntry({slug: 'stewarts-stories'}); %>
		<div class="banner <% if(page.data && page.data.banner_link) { %>banner--link<% } %><% if(page.data.component == 'Event Module') { %><% if(page.data.page_type == 'blocks') { %>banner__hide<% } else { %><% } %><% } %> <% if(page.data.banner_image_position == "top") { %>banner--top<% } else if(page.data.banner_image_position == "bottom") { %>banner--bottom<% } else { %><%}%>" <% if(page.data.banner_image) { %>style="background-image: url('<%- plugins.img(page.data.banner_image, { h: 800 }) %>'); background-size: cover;"<% } else { %><% if(plugins.segment(2) == 'careers') { %>class="banner__careers"<% } %> style="background-image: url('<% if(plugins.segment(1) == 'news') { %><%- plugins.img(news_listing_page.data.banner_image, { h: 800 }) %><% } else if(plugins.segment(1) == 'work-we-do') { %><%- plugins.img(services_listing_page.data.banner_image, { h: 800 }) %><% } else if(plugins.segment(2) == 'stewarts-stories') { %><%- plugins.img(stewarts_stories_page.data.banner_image, { h: 800 }) %><% } else if(plugins.segment(1) == 'events') { %><%- plugins.img(event_listing_page.data.banner_image, { h: 800 }) %><% } else { %><% if(page.data && page.data.banner_image) { %><%- plugins.img(page.data.banner_image, { h: 800 }) %><% } else { %><%- plugins.img(site.settings.news_fallback_image, { h: 800 }) %><% } %><% } %>'); background-size: cover; "<%}%>>
			<div class="container">
				<div class="banner__content">
					<% if((['News Module','Event Module']).includes(page.data.component)) { %>
						<h2 class="banner__heading heading--h1">
							<% if(plugins.segment(1) == 'news') { %>News<% } else if(plugins.segment(1) == 'events') { %>Events<% } %>
						</h2>
					<% } else { %>
						<h1 class="banner__heading">
							<% if(page.data && page.data.banner_heading) { %>
								<%= page.data.banner_heading %>
							<% } else { %>
								<%= page.title %>
							<% } %>
						</h1>
					<% } %>
					<% if(page.data && page.data.banner_link) { %>
						<div class="banner__links">
							<% if(page.data && page.data.banner_link && page.data.banner_link[0] && page.data.banner_link[0].link.anchor) { %>
								<a href="<%- page.data.banner_link[0].cached_url %>#<%= page.data.banner_link[0].link.anchor %>" class="button banner__link"><%- page.data.banner_link[0].text %></a>
							<% } else { %>
								<%- plugins.simpleLink(page.data.banner_link, 'button banner__link') %>
							<% } %>
						</div>
					<% } %>	
				</div>
			</div>
		</div>
	<% } %>
</header>

<div class="responsive_menu" data-responsive-menu>
	<a href="#" class="close-icon" data-close-responsive-menu><i class="icon icon-close2"></i></a>
	<% if(plugins.segment(1) == 'sports-centre') { %>
        <%- plugins.include('snippets/header/sports-nav.html', { class: 'responsive_navigation', cache: false }); %>
    <% } else { %>
        <%- plugins.include('snippets/header/nav.html', { class: 'responsive_navigation', cache: false }); %>
    <% } %>
</div>